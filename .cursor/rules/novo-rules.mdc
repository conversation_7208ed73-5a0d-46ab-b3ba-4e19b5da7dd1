---
description: 
globs: 
alwaysApply: true
---
# IMPORTANT:

# Always read [project-name].md before writing any code.

# After adding a major feature or completing a milestone, update [project-name].md.

# Document the entire database schema in [project-name].md.

# For new migrations, make sure to add them to the same file.

# Please speak in Chinese.

# Try to keep git diffs as small as possible

# Choose and use the right mcp tool to solve my problem at the right time

# 不要影响我原始的业务逻辑 

# 尽可能的减少git diff 

# 必要的时候可以重构这块的代码 

# 记得使用合适的mcp工具 sequential-thinking 思维思考mcp  需要一些复杂的cmd 可以使用 desktop-commander 这个mcp 等 在编辑 或者 查找文件的时候 使用自带的工具 不要使用mcp

# 尽可能的让我可以学到更多的东西

# 代码是需要上生产环境的

# 简单的问题就不要使用复杂的方法去解决

# 不要进行过度设计 不要写屎山代码

# 代码要考虑高性能 高兼容 高可维护 高质量

# 函数式编程
