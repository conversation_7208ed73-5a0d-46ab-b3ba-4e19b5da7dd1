# CLAUDE.md - DNurse Flutter App Developer Guide

## Project Overview

**D<PERSON>urse (糖护士)** is a comprehensive diabetes management Flutter application that supports Android, iOS, and HarmonyOS (OHOS). This is a complete rewrite using Flutter 3.22.1 specifically for HarmonyOS compatibility.

**App Name**: <PERSON><PERSON><PERSON><PERSON> (糖护士)  
**Version**: 4.3.19+115  
**Flutter SDK**: >=3.4.0 <4.0.0  
**Special Flutter Version**: 3.22.0-ohos (for HarmonyOS compatibility)

---

## Quick Start Commands

### Environment Setup
```bash
# Use FVM for Flutter version management
cd ~/fvm/versions/
git clone -b 3.22.0-ohos https://gitcode.com/openharmony-sig/flutter_flutter.git custom_3.22-ohos

# Set global Flutter version
fvm global custom_3.22-ohos

# Install dependencies
flutter pub get
```

### Development Commands
```bash
# Code generation (models, DTOs, assets)
./tools/code_gen.sh

# Code formatting and fixes
./tools/code_format.sh
./tools/code_fix.sh

# Code analysis
./tools/code_analyze.sh

# Clean generated files
./tools/code_gen_clean.sh
```

### Build Commands
```bash
# Android builds
flutter build apk --dart-define-from-file lib/env/dev.json
flutter build apk --dart-define-from-file lib/env/uat.json
flutter build apk --dart-define-from-file lib/env/prod.json

# HarmonyOS/OHOS build
./tools/build_ohos.sh
flutter build app --dart-define-from-file lib/env/dev.json --release

# iOS build (use Xcode after running once with Android Studio)
flutter build ios --dart-define-from-file lib/env/prod.json
```

### Module Management
```bash
# Add new module
./tools/gen_mod_template.sh

# Generate startup code for modules
./tools/gen_mod_list.sh
```

---

## Architecture Overview

### Modular Architecture
The app uses a **modular monolith** architecture with each feature as a separate module:

- **lib/app/activities/** - Special activities and campaigns
- **lib/app/common/** - Shared utilities and services
- **lib/app/community/** - Community features
- **lib/app/data/** - Blood glucose data management
- **lib/app/device/** - Medical device connectivity (Bluetooth, etc.)
- **lib/app/game/** - Gamification features
- **lib/app/knowledge/** - Educational content
- **lib/app/main/** - App initialization and home screen
- **lib/app/message/** - Messaging and notifications
- **lib/app/shop/** - In-app purchases and store
- **lib/app/task/** - Daily tasks and achievements
- **lib/app/user/** - User management and authentication

### State Management
- **GetX** - Primary state management solution
- Pattern: Logic + State + View + Binding for each page
- Controllers extend `DNUTabController` or `BaseController`

### Database
- **SQLite** via `sqflite` (HarmonyOS compatible fork)
- Migration system in each module's `db/migration/` folder
- Database models use **Freezed** for immutability

### Networking
- **Dio** for HTTP requests
- Custom interceptors for authentication and logging
- Mock support for development/testing

---

## Key Frameworks & Libraries

### Core Dependencies
- **get: ^4.6.5** - State management, routing, dependency injection
- **bruno: 3.4.3** - UI component library
- **flutter_screenutil: ^5.6.1** - Screen adaptation
- **sqflite** - Database (HarmonyOS-compatible fork)
- **dio: ^5.4.3+1** - HTTP client

### Data & Serialization
- **freezed: ^2.3.2** - Immutable data classes
- **json_annotation: ^4.8.1** - JSON serialization
- **build_runner: ^2.3.3** - Code generation

### UI & Charts
- **fl_chart: 0.69.0** - Charts for glucose trends
- **carousel_slider: ^5.0.0** - Image carousels
- **pull_to_refresh: ^2.0.0** - Pull-to-refresh functionality
- **flutter_svg: ^2.0.21** - SVG support

### Platform Integration
- **All major plugins use HarmonyOS-compatible forks from OpenHarmony SIG**
- Custom plugins in `/plugins/` directory for specific hardware integration

---

## Platform Support

### Multi-Platform Configuration
1. **Android** - Standard Flutter support
2. **iOS** - Standard Flutter support  
3. **HarmonyOS (OHOS)** - Custom Flutter fork with extensive plugin ecosystem

### HarmonyOS Specific
- Uses OpenHarmony SIG packages: https://gitee.com/openharmony-sig/
- Special build configuration in `/ohos/` directory
- Custom plugins adapted for HarmonyOS API compatibility

---

## Development Workflow

### Code Generation
The app heavily uses code generation for:
- **Assets** - `./tools/assets_gen.pl` generates `lib/resource/dnu_assets.g.dart`
- **Models** - Freezed generates `.freezed.dart` and `.g.dart` files
- **DTOs** - API response models
- **Database** - Provider classes for SQLite

### Environment Variables
Uses `--dart-define-from-file` for environment-specific configuration:
- **Development**: `lib/env/dev.json`
- **Production**: `lib/env/prod.json`
- **UAT**: `lib/env/uat.json` (if exists)

Contains API hosts, app secrets, third-party SDK keys, etc.

### Internationalization
- **flutter_intl** plugin with custom configuration
- Primary language: Chinese (`zh`)
- ARB files in `lib/resource/l10n/`
- Generated files in `lib/resource/generated/`

---

## Testing Strategy

### Current Test Setup
- Basic widget test in `/test/widget_test.dart`
- **Note**: Test is outdated and needs updating for current app structure

### Testing Frameworks Available
- **flutter_test** - Widget and unit testing
- **dart_code_linter** - Static analysis with custom rules

### Recommended Testing Approach
1. Update existing widget test
2. Add unit tests for business logic (services, converters)
3. Integration tests for critical user flows (glucose logging, device sync)
4. Golden tests for UI consistency

---

## Custom Plugins

The app includes several custom plugins in `/plugins/`:

### Medical Device Integration
- **dnurse_spug_plugin** - SPUG blood glucose meter
- **dnurse_ble_plugin** - Bluetooth Low Energy device communication
- **dnurse_cgm_plugin** - Continuous Glucose Monitor support
- **dnurse_bs_test_plugin** - Blood sugar testing integration

### Platform Services
- **dnurse_common_plugin** - Shared native functionality
- **dnurse_reminder_plugin** - Medication/testing reminders
- **huawei_kit** - Huawei services integration

### Payment & Social
- **alipay_kit** - Alipay payment integration
- **wechat_kit** - WeChat integration
- **tencent_kit** - Tencent QQ integration

---

## Key Directories

### Application Code
- `lib/bootstrap/` - App initialization
- `lib/framework/` - Core framework (networking, database, services)
- `lib/router/` - Navigation and routing
- `lib/ui/` - Shared UI components and themes
- `lib/resource/` - Assets, localization, generated code

### Platform Specific
- `android/` - Android configuration
- `ios/` - iOS configuration  
- `ohos/` - HarmonyOS configuration
- `web/` - Web platform support

### Development Tools
- `tools/` - Build scripts and code generation utilities
- `assets/` - Images, fonts, database files

---

## Important Notes

### HarmonyOS Development
- Requires **DevEco Studio** for HarmonyOS development
- Many standard Flutter plugins replaced with HarmonyOS-compatible versions
- Special signing configuration in `ohos/build-profile.json5`

### Code Style
- Uses **dart_code_linter** with custom rules
- Excludes generated files from linting (`.g.dart`, `.freezed.dart`, etc.)
- Follows GetX architecture patterns consistently

### Bluetooth & Device Integration
- Extensive Bluetooth Low Energy support for medical devices
- Custom protocols for various glucose meters and CGM devices
- Data synchronization with cloud backend

### Data Privacy
- Comprehensive privacy handling in bootstrap sequence
- Third-party SDK initialization only after privacy consent
- Medical data encryption and secure storage

---

## Common Tasks

### Adding a New Feature Module
1. Run `./tools/gen_mod_template.sh`
2. Implement module structure (pages, services, entities)
3. Add database migrations if needed
4. Update `./tools/gen_mod_list.sh` and run it
5. Add routing in module's `getRouters()` method

### Adding New Medical Device Support
1. Extend relevant plugin (typically `dnurse_ble_plugin`)
2. Add device-specific protocol in `lib/app/device/entity/`
3. Implement data converters in `lib/app/data/convertor/`
4. Add UI components in device module

### Updating API Models
1. Add/modify DTO classes in `lib/app/*/api/dto/`
2. Use Freezed annotations for immutability
3. Run `./tools/code_gen.sh` to generate serialization code
4. Update corresponding service classes

---

This guide should help any Claude instance quickly understand and work productively with the DNurse Flutter codebase. The app's modular architecture, comprehensive tooling, and multi-platform support make it a complex but well-structured diabetes management solution.