/// FileName: webview_handler
///
/// @Author: ygc
/// @Date: 2024/6/20 11:37
/// @Description:
///
import 'package:dnurse/app/common/web/webview_interface.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../framework/utils/log.dart';
import '../service/pay_service.dart';
import 'dto/add_comment_dto/add_comment_dto.dart';
import 'dto/set_close_flag_dto/set_close_flag_dto.dart';
import 'dto/set_share_data_dto/set_share_data_dto.dart';
import 'entity/webview_title_info.dart';

class WebInterfaceHandler implements DnursePayHandler {
  WebInterfaceHandler({
    required this.webViewController,
    this.onActionClick,
    this.onTitleChanged,
    this.onShareChanged,
    this.onSetCloseFlagChanged,
    this.onCommentClick,
    this.onClosePage,
    this.onAppBarHidden,
  });

  WebViewController webViewController;
  ValueChanged<String>? onActionClick;
  BuildContext? _context;

  void Function(WebViewTitleInfo?, Map<String, Object?>)? onTitleChanged;
  void Function(SetShareDataDTO)? onShareChanged;
  void Function(SetCloseFlagDTO)? onSetCloseFlagChanged;
  void Function(AddCommentDTO)? onCommentClick;
  VoidCallback? onAppBarHidden;
  VoidCallback? onClosePage;

  static const String actionSetShareData = "setShareData";
  static const String actionSetCloseFlag = "setCloseFlag";
  static const String actionAddComment = "addComment";
  static const String actionHideNavigation = "hideNavigation";
  static const String actionCloseWindow = "closeWindow";

  String? _payOrderNo;
  String? _wxPrepayId;

  void init() {
    PayService.to.addPayHandler(this);
  }

  void dispose() {
    PayService.to.removePayHandler(this);
  }

  void setBuildContext(BuildContext context) {
    _context = context;
  }

  bool handleMessage(String message, {BuildContext? context}) {
    Log.i("handleMessage: $message");

    if (!WebViewInterface.check(message)) {
      return false;
    }

    Map<String, Object?> appJson = WebViewInterface.parse(message);
    String action = WebViewInterface.getAction(appJson);
    if (action.isEmpty) {
      return false;
    }

    if (WebViewInterface.isTitleMessage(appJson)) {
      WebViewTitleInfo? titleInfo = WebViewInterface.parseTitle(
          appJson, webViewController, onActionClick);
      onTitleChanged?.call(titleInfo, appJson);
      return true;
    } else if (action == actionSetShareData) {
      SetShareDataDTO setShareDataDTO =
          WebViewInterface.parseSetShareData(appJson);
      onShareChanged?.call(setShareDataDTO);
      return true;
    } else if (action == actionSetCloseFlag) {
      SetCloseFlagDTO setCloseFlagDTO =
          WebViewInterface.parseSetCloseFlag(appJson);
      onSetCloseFlagChanged?.call(setCloseFlagDTO);
      return true;
    } else if (action == actionAddComment) {
      AddCommentDTO addCommentDTO = WebViewInterface.parseAddComment(appJson);
      onCommentClick?.call(addCommentDTO);
      return true;
    } else if (action == "alipay") {
      String payString = isAlipayJson(appJson);
      _payOrderNo = appJson["out_trade_no"] as String;
      PayService.to.alipay(payString);
      return true;
    } else if (action == "wxpay") {
      // Log.i("wx pay: $appJson");
      try {
        _wxPrepayId = appJson["prepayid"] as String?;

        PayService.to.wechatPay(appJson);
      } catch (e) {
        if (context != null) {
          DNUToast.show("启动支付错误", context);
        }
      }
      return true;
    } else if (action == actionHideNavigation) {
      onAppBarHidden?.call();
      return true;
    } else if (action == actionCloseWindow) {
      onClosePage?.call();
      return true;
    }

    return false;
  }

  bool isWXPayJson(Map<String, Object?> appJson) {
    return appJson.containsKey("appid") &&
        appJson.containsKey("partnerid") &&
        appJson.containsKey("prepayid");
  }

  String isAlipayJson(Map<String, Object?> appJson) {
    try {
      if (appJson.containsKey("paystring") &&
          appJson.containsKey("out_trade_no") &&
          appJson.containsKey("act") &&
          appJson["act"] == "alipay") {
        return appJson["paystring"] as String;
      } else {
        return "";
      }
    } catch (e) {
      Log.e("check isAlipayJson error:", e);
    }

    return "";
  }

  @override
  void onPayResult(PayResult payResult) {
    Log.d(
        "onPayResult: apyType: ${payResult.type}, status: ${payResult.resultCode}, orderNo: ${payResult.result}, message: ${payResult.message}");

    if (payResult.resultCode == PayResultCode.success) {
      if (payResult.type == PayType.alipay) {
        if (_payOrderNo != null) {
          webViewController.runJavaScript("Res_alipay('${_payOrderNo!}')");
        }
        _payOrderNo = null;
      } else if (payResult.type == PayType.wechat) {
        if (_wxPrepayId != null) {
          webViewController.runJavaScript("Res('${_wxPrepayId!}')");
        }

        _wxPrepayId = null;
      }
    } else {
      String msg = payResult.message ?? "支付失败";
      if (msg.isEmpty) {
        msg = '用户取消支付';
      }

      // if (payResult.status == PayStatus.confirming) {
      //   msg = "支付结果确认中";
      // }

      if (_context != null) {
        DNUToast.show(msg, _context!);
      }
    }
  }
}
