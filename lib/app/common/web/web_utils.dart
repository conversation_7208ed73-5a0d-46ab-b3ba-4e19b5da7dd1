import 'dart:async';
import 'dart:io';

import 'package:dnurse/framework/service/app_config.dart';
import 'package:dnurse/framework/utils/platform_utils.dart';
import 'package:dnurse_common_plugin/dnurse_common_plugin.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

// ignore: depend_on_referenced_packages
import 'package:webview_flutter_android/webview_flutter_android.dart';

import '../../../bootstrap/app.dart';
import '../../../framework/network/api_host.dart';
import '../../../framework/utils/log.dart';
import '../link/deep_link.dart';

/// FileName: web_utils
///
/// @Author: ygc
/// @Date: 2024/6/12 17:53
/// @Description:

const webFlutterScrollObject = "FlutterScroll";

class WebUtils {
  WebUtils._();

  static Future<void> setTokenCookie() async {
    WebViewCookieManager webViewCookieManager = WebViewCookieManager();

    WebViewCookie webViewCookie = WebViewCookie(
      name: "token",
      value: AppContext.to.hasToken ? AppContext.to.token : '',
      domain: DNUHost.getWebCookieHost(),
    );
    await webViewCookieManager.setCookie(webViewCookie);
  }

  static Future<void> setUserAgent(WebViewController webViewController,
      {bool appVerAtHead = false,}) async {
    String userAgent;
    if (DNUPlatform.isOhos) {
      userAgent = AppConfig.to.osWebUserAgent;

      if(appVerAtHead) {
        userAgent = "${AppConfig.to.webUserAgent} $userAgent Android/99";
      } else {
        userAgent = "$userAgent ${AppConfig.to.webUserAgent} Android/99";
      }
    } else {
      dynamic ua = await webViewController
          .runJavaScriptReturningResult("navigator.userAgent");

      if(appVerAtHead) {
        userAgent = "${AppConfig.to.webUserAgent} $ua";
      } else {
        userAgent = "$ua ${AppConfig.to.webUserAgent}";
      }
    }

    Log.i("web user agent: $userAgent");
    // "Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  ArkWeb/******* Mobile"
    webViewController.setUserAgent(userAgent);
  }

  static Future<void> setupScrollChannel(WebViewController webViewController,
      void Function(double scrollX, double scrollY) onScroll) async {
    await webViewController.removeJavaScriptChannel(webFlutterScrollObject);

    await webViewController.addJavaScriptChannel(webFlutterScrollObject,
        onMessageReceived: (JavaScriptMessage message) {
          Log.i("setupScrollListener: ${message.message}");
          List<String> parts = message.message.split(",");
          double scrollX = 0;
          double scrollY = 0;
          if (parts.isNotEmpty && parts[0].isNotEmpty) {
            scrollX = double.parse(parts[0]);
          }
          if (parts.length > 1 && parts[1].isNotEmpty) {
            scrollY = double.parse(parts[1]);
          }

          onScroll(scrollX, scrollY);
        });
  }

  static Future<void> setupScrollListener(
      WebViewController webViewController) async {
    await webViewController.runJavaScript('''      
      window.onscroll = function() {
        $webFlutterScrollObject.postMessage(window.scrollX + "," + window.scrollY);
      };
    ''');
  }

  static Future<void> stopAudioPlay(WebViewController webViewController) {
    const String jsStr = """var players = document.querySelectorAll("audio");
         if (players != null) {
          players.forEach(function(item) {
            item.pause();
          });
         }
        """;
    return webViewController.runJavaScript(jsStr);
  }

  static NavigationDelegate getNavigationDelegate({
    FutureOr<NavigationDecision> Function(NavigationRequest request)?
    onNavigationRequest,
    void Function(String url)? onPageStarted,
    void Function(String url)? onPageFinished,
    void Function(int progress)? onProgress,
    void Function(WebResourceError error)? onWebResourceError,
    void Function(UrlChange change)? onUrlChange,
    void Function(String url, dynamic result)? onDeepLinkResult,
  }) {
    return NavigationDelegate(
      onNavigationRequest: (NavigationRequest request) async {
        if (request.url.startsWith('mailto:')) {
          final uri = Uri.parse(request.url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri);
          }
          return NavigationDecision.prevent;
        } else if (DeepLink.isUrl(request.url)) {
          DeepLinkResult result = DeepLink.handle(request.url, source: "web");
          if (request.url != null && result.pageResult != null) {
            result.pageResult!.then((value) {
              Log.i("webView scan code: $value");
              onDeepLinkResult?.call(request.url, value);
            });
          }
          return NavigationDecision.prevent;
        } else {
          if(DNUPlatform.isOhos) {
            String url = request.url.toLowerCase();

            if(!url.startsWith('http://') && !url.startsWith('https://')) {
              final dnurseCommonPlugin = DnurseCommonPlugin();
              dnurseCommonPlugin.openLink(request.url);
              return NavigationDecision.prevent;
            }
          }
        }

        if (onNavigationRequest != null) {
          return onNavigationRequest(request);
        }

        return NavigationDecision.navigate;
      },
      onPageStarted: (String url) {
        if (onPageStarted != null) {
          onPageStarted(url);
        }
      },
      onPageFinished: (String url) {
        if (onPageFinished != null) {
          onPageFinished(url);
        }
      },
      onProgress: (int progress) {
        if (onProgress != null) {
          onProgress(progress);
        }
      },
      onWebResourceError: (WebResourceError error) {
        Log.e('''
Page resource error:
  code: ${error.errorCode}
  description: ${error.description}
  errorType: ${error.errorType}
  isForMainFrame: ${error.isForMainFrame}
          ''');
        if (onWebResourceError != null) {
          onWebResourceError(error);
        }
      },
      onUrlChange: (UrlChange change) {
        if (onUrlChange != null) {
          onUrlChange(change);
        } else {
          if (change.url != null) {
            DeepLink.handle(change.url!, source: 'web');
          }
        }
      },
    );
  }

  static void initGrant(WebViewController webViewController) {
    if (Platform.isAndroid) {
      final AndroidWebViewController androidWebViewController =
      webViewController.platform as AndroidWebViewController;
      androidWebViewController.setGeolocationPermissionsPromptCallbacks(
          onShowPrompt: (re) async {
            const location = Permission.location; //定位状态确认
            var status = await location.status;
            if (!status.isGranted) {
              status = await location.request();
            }
            Log.d('是否同意定位权限：${status.isGranted}');

            return GeolocationPermissionsResponse(
                allow: status.isGranted, retain: true);
          });
      androidWebViewController.setOnPlatformPermissionRequest((request) async {
        for (WebViewPermissionResourceType item in request.types) {
          if (WebViewPermissionResourceType.microphone == item) {
            Log.d('Android申请权限麦克风权限');
            const location = Permission.microphone; //定位状态确认
            var status = await location.status;
            if (!status.isGranted) {
              status = await location.request();
            }
            if (status.isGranted) {
              request.grant();
            } else {
              request.deny();
            }
            Log.d('是否同意麦克风权限：${status.isGranted}');
          }
        }
        // Log.d('Android申请权限：$request');
        request.grant();
      });
    }
  }
}
