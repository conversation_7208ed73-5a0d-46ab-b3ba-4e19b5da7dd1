/// FileName: webview_interface
///
/// @Author: ygc
/// @Date: 2024/6/12 13:14
/// @Description:

import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:dnurse/app/common/web/dto/shop_header/shop_update_header_dto.dart';
import 'package:dnurse/app/common/web/widget/web_title_tabs_widget.dart';
import 'package:dnurse/app/user/pages/my/logic.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../framework/utils/log.dart';
import '../../../resource/dnu_assets.dart';
import '../../../ui/widgets/dnu_toast.dart';
import 'dto/add_comment_dto/add_comment_dto.dart';
import 'dto/set_close_flag_dto/set_close_flag_dto.dart';
import 'dto/set_share_data_dto/set_share_data_dto.dart';
import 'dto/shop_header/shop_header_item_dto.dart';
import 'dto/update_titles_and_func/update_titles_and_func_dto.dart';
import 'entity/webview_title_info.dart';

const _dnuAppJs = '''
window.dnuApp = {
  setShareDataV2() {
  dnurseFlutter.postMessage(JSON.stringify(arguments));
  },
  ShareCommunity() {
  for (var i = 0; i < arguments.length; i++) {
        console.log('ShareCommunity args', i,  arguments[i]); // 输出每一个参数
    }
    var gg = {
    'function_name':'ShareCommunity',
    'args':arguments[0],
    }
    
    
  dnurseFlutter.postMessage(JSON.stringify(gg));
  },
  closeWindow() {
    var gg = {
      'function_name':'closeWindow',
      'args':arguments[0],
      }
    
    dnurseFlutter.postMessage(JSON.stringify(gg));
  }
};
''';

const appMessagePrefix = "toApp:";
const appMessageActKey = "act";
const actionUpdateHeader = "updateHeader";
const _actionUpdateTitlesAndFunc = "updateTitlesAndFunc";
const actionSetShareData = "setShareData";
const actionSetCloseFlag = "setCloseFlag";
const actionAddComment = "add_comment";

class WebViewInterface {
  static void injectDnuApp(WebViewController webViewController) {
    webViewController.runJavaScript(_dnuAppJs);
  }

  static void setInterface(WebViewController webViewController) {
    final controller = Get.find<MyLogic>();

    webViewController.setOnConsoleMessage((message) {
      Log.i("setOnConsoleMessage: ${message.message}");
    });
    webViewController.addJavaScriptChannel("dnurseFlutter",
        onMessageReceived: (message) {
      Log.d("dnurseFlutter:  ${message.message}");
      final Map<String, dynamic> appJson = jsonDecode(message.message);
      Log.i("dnurseFlutter2:args  ${appJson['args']}");
      if (!appJson.containsKey('function_name')) {
        return;
      }

      final String functionName = appJson['function_name'] as String;
      if (functionName == "ShareCommunity") {
        DNUToast.show('当前版本不支持分享', Get.context!);
        // controller.showShareSheet(Get.context!);
        return;
      } else if (functionName == "closeWindow") {
        AppRouter.back();
      }

      // Log.i("dnurseFlutter2:function_name  ${appJson['function_name']}");
      // final String appArgsStr = appJson['args'] as String;
      // final Map<String, dynamic> appArgs = jsonDecode(appArgsStr);
      // Log.i("dnurseFlutter2:args1  $appArgs");

      // Log.d("dnurseFlutter2:title  ${appJson['args']['title']}");
    });
  }

  static bool check(String appMessage) {
    return appMessage.startsWith(appMessagePrefix);
  }

  static Map<String, Object?> parse(String appMessage) {
    return jsonDecode(appMessage.replaceFirst(appMessagePrefix, ""));
  }

  static String getAction(Map<String, Object?> appJson) {
    return appJson[appMessageActKey] as String? ?? "";
  }

  static bool isTitleMessage(Map<String, Object?> appJson) {
    String action = getAction(appJson);
    if (action.isEmpty) {
      return false;
    }

    List<String> actions = [actionUpdateHeader, _actionUpdateTitlesAndFunc];

    return actions.contains(action);
  }

  static WebViewTitleInfo? parseTitle(
      Map<String, Object?> appJson,
      WebViewController webViewController,
      ValueChanged<String>? onActionClick) {
    String action = getAction(appJson);
    if (action.isEmpty) {
      return null;
    }

    if (action == _actionUpdateTitlesAndFunc) {
      return parseTitlesAndFunc(appJson, webViewController, onActionClick);
    } else if (action == actionUpdateHeader) {
      return parseUpdateHeader(appJson, webViewController, onActionClick);
    }

    return null;
  }

  static WebViewTitleInfo? parseTitlesAndFunc(
      Map<String, Object?> appJson,
      WebViewController webViewController,
      ValueChanged<String>? onActionClick) {
    Widget? titleWidget;
    List<Widget> action = [];
    UpdateTitlesAndFuncDTO dto = UpdateTitlesAndFuncDTO.fromJson(appJson);
    if (dto.title_tab != null && dto.title_tab!.isNotEmpty) {
      titleWidget = WebTitleTabsWidget(
          tabs: dto.title_tab!, webViewController: webViewController);
    }
    if (dto.iconUrl.isNotEmpty) {
      Map<String, Object?> iconData = {
        "type": "icon",
        "iconUrl": dto.iconUrl,
        "hasAction": dto.actUrl.isEmpty,
        "action": {"actionType": "link", "linkHref": dto.actUrl}
      };
      ShopHeaderItemDTO itemDTO = ShopHeaderItemDTO.fromJson(iconData);

      action.add(_buildAction(itemDTO, webViewController, onActionClick));
    }

    if (titleWidget == null && action.isEmpty) {
      return null;
    }

    return WebViewTitleInfo(center: titleWidget, actions: action);
  }

  static Widget _buildAction(
      ShopHeaderItemDTO itemDTO,
      WebViewController webViewController,
      ValueChanged<String>? onActionClick) {
    Widget? iconWidget;
    if (itemDTO.type == "icon") {
      if (itemDTO.iconUrl == 'cart') {
        iconWidget = Image.asset(itemDTO.badge.isEmpty
            ? DNUAssets.to.images.shop.cartEmpty
            : DNUAssets.to.images.shop.cartNotEmpty);
      } else if (itemDTO.iconUrl == "share_v2") {
        // iconWidget = const Icon(Icons.share);
        iconWidget = Container();
      }
    }

    return BrnIconAction(
      size: 40,
      child: Stack(
        fit: StackFit.expand,
        clipBehavior: Clip.none,
        children: [
          if (iconWidget != null) iconWidget,
          if (itemDTO.badge.isNotEmpty)
            Positioned(
                right: -4,
                top: 0,
                child: Container(
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: Colors.red),
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Text(
                      itemDTO.badge,
                      style: const TextStyle(fontSize: 12, color: Colors.white),
                    ),
                  ),
                )),
        ],
      ),
      iconPressed: () {
        if (itemDTO.hasAction && itemDTO.action != null) {
          if (itemDTO.action!.actionType == 'function' &&
              itemDTO.action!.clickFunc.isNotEmpty) {
            webViewController.runJavaScript(itemDTO.action!.clickFunc);
          }
        }

        if (onActionClick != null) {
          onActionClick(itemDTO.iconUrl);
        }
      },
    );
  }

  static WebViewTitleInfo? parseUpdateHeader(
      Map<String, Object?> appJson,
      WebViewController webViewController,
      ValueChanged<String>? onActionClick) {
    Widget? centerWidget;
    List<Widget> actions = [];

    Log.d('parseUpdateHeader: $appJson');
    ShopUpdateHeaderDTO dto = ShopUpdateHeaderDTO.fromJson(appJson);
    if (dto.center != null && dto.center!.has) {
      ShopHeaderItemDTO itemDTO = dto.center!.child.first;
      if (itemDTO.type == "text") {
        centerWidget = Text(itemDTO.textFont);
      } else if (itemDTO.type == "search") {
        centerWidget = setSearchGoodChild(itemDTO, webViewController);
      }
    }
    if (dto.right != null && dto.right!.has) {
      for (ShopHeaderItemDTO itemDTO in dto.right!.child) {
        actions.add(_buildAction(itemDTO, webViewController, onActionClick));
      }
    }

    return WebViewTitleInfo(center: centerWidget, actions: actions);
  }

  static Widget? _searchGoodChild;
  static TextEditingController? _searchTextController;
  static ShopHeaderItemDTO? _itemDTO;
  static FocusNode? _focusNode;
  static Widget setSearchGoodChild(
      ShopHeaderItemDTO itemDTO1, WebViewController webViewController) {
    _itemDTO = itemDTO1;
    if (_searchGoodChild == null) {
      _focusNode = FocusNode();
      BrnSearchTextController searchTextController = BrnSearchTextController();
      _searchTextController = TextEditingController();
      String hintText = "";

      bool isGoodSearch = true; //itemDTO.action?.actionType == "goodSearch";
      // focusNode.addListener((){
      //   if(focusNode.hasFocus){
      //     if(!isGoodSearch) {
      //       _handleSearchAction(itemDTO, webViewController, "");
      //     }
      //   }
      // });

      _searchGoodChild = Container(
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 10),
          child: BrnSearchText(
            searchController: searchTextController,
            controller: _searchTextController,
            hintText: hintText,
            onTextClear: () {
              return false;
            },
            focusNode: _focusNode,
            autoFocus: isGoodSearch,
            action: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: GestureDetector(
                  onTap: () {
                    // focusNode.unfocus();
                    Log.i('onActionTap : ${_searchTextController!.text}');
                    _handleSearchAction(_itemDTO!, webViewController,
                        _searchTextController!.text);
                  },
                  child: const Text(
                    "搜索",
                    style: TextStyle(color: AppColor.primary, fontSize: 14),
                  )),
            ),
            onTextCommit: (text) {
              // BrnToast.show('提交内容 : $text', context);
              Log.i('提交内容 : $text');
              _handleSearch(_itemDTO!, webViewController, text);
            },
            onTextChange: (text) {
              searchTextController.isActionShow = text.isNotEmpty;
              // BrnToast.show('输入内容 : $text', context);
              _handleSearchInput(_itemDTO!, webViewController, text);
            },
          ));
    }

    if (_itemDTO!.searchArr.isNotEmpty &&
        _itemDTO!.searchArr.first.isNotEmpty) {
      Log.d('重设搜索文字：${_itemDTO!.searchArr.length}');
      _searchTextController!.text = _itemDTO!.searchArr.first;
    }

    return _searchGoodChild!;
  }

  static void _handleSearchInput(ShopHeaderItemDTO itemDTO,
      WebViewController webViewController, String text) {
    String js = "input_search('$text')";
    Log.i('_handleSearchInput : $js');
    webViewController.runJavaScript(js);
  }

  static void _handleSearch(ShopHeaderItemDTO itemDTO,
      WebViewController webViewController, String text) {
    _focusNode?.unfocus();
    if (text.isEmpty) {
      return;
    }
    String js = "search('$text')";
    Log.i('_handleSearch : $js');
    webViewController.runJavaScript(js);
  }

  static void _handleSearchAction(ShopHeaderItemDTO itemDTO,
      WebViewController webViewController, String text) {
    if (_itemDTO!.action?.actionType == "goodSearch") {
      _handleSearch(_itemDTO!, webViewController, text);
    } else if (_itemDTO!.action?.actionType == "function") {
      if (_itemDTO!.action!.clickFunc.isNotEmpty) {
        webViewController.runJavaScript(_itemDTO!.action!.clickFunc);
      }
    }
  }

  static void _handleGoodSearchAction(ShopHeaderItemDTO itemDTO,
      WebViewController webViewController, String text) {}

  static SetShareDataDTO parseSetShareData(Map<String, Object?> appJson) {
    return SetShareDataDTO.fromJson(appJson);
  }

  static SetCloseFlagDTO parseSetCloseFlag(Map<String, Object?> appJson) {
    return SetCloseFlagDTO.fromJson(appJson);
  }

  static AddCommentDTO parseAddComment(Map<String, Object?> appJson) {
    // {"act":"add_comment","aid":24981,"comment_id":9182,"comment_to_name":"snldhb","comment_to_sn":"95e81387c4c850c852b9681e2a7092f0"}
    return AddCommentDTO.fromJson(appJson);
  }
}
