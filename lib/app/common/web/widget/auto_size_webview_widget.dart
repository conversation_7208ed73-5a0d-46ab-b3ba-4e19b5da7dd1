/// FileName: auto_size_webview_widget
///
/// @Author: ygc
/// @Date: 2022/10/14 16:00
/// @Description: 自适应大小的webview

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';


String _htmlPage = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8">
    <style type="text/css">
    body { background: transparent; margin: 0; padding: 0; border-width: 0;font-size:20px; font-family: "InstrumentSans", "InstrumentSans-SemiBold", "微软雅黑", sans-serif;}
    <!--HEAD-->
    </style>
</head>
<body>
<!--BODY-->
</body>
<script>
    const resizeObserver = new ResizeObserver(entries =>
        Resize.postMessage(document.documentElement.scrollHeight.toString()))
    resizeObserver.observe(document.body)
</script>
</html>
""";

class AutoSizeWebViewWidget extends StatefulWidget {
  const AutoSizeWebViewWidget(
      {Key? key,
        required this.webViewController,
      this.initialHeight,
      this.htmlString,
      this.cssString,
      this.navigationDelegate,
      this.backgroundColor})
      : super(key: key);
  final double? initialHeight;
  final String? htmlString;
  final String? cssString;
  final Color? backgroundColor;
  final NavigationDelegate? navigationDelegate;
  final WebViewController webViewController;

  @override
  State<AutoSizeWebViewWidget> createState() => _AutoSizeWebViewWidgetState();
}

class _AutoSizeWebViewWidgetState extends State<AutoSizeWebViewWidget> {
  late double webViewHeight;

  late String _htmlString;
  late Color? _backgroundColor;

  @override
  void initState() {
    super.initState();
    webViewHeight = widget.initialHeight ?? 1;
    _backgroundColor = widget.backgroundColor;
    _htmlString = _genHtml(
      body: widget.htmlString,
      css: widget.cssString,
    );

    widget.webViewController.addJavaScriptChannel('Resize',
        onMessageReceived: (JavaScriptMessage message) {
          double height = double.parse(message.message);
          setState(() {
            webViewHeight = height;
          });
        });

    // if (Platform.isAndroid) {
    //   WebView.platform = SurfaceAndroidWebView();
    // }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: webViewHeight,
      child: WebViewWidget(controller: widget.webViewController,
          // onWebViewCreated: (WebViewController webViewController) {
          //   _controller = (webViewController);
          //
          //   if (widget.htmlString != null) {
          //     webViewController.loadHtmlString(_htmlString);
          //   }
          // },
          // gestureNavigationEnabled: false,
          // backgroundColor: _backgroundColor ?? const Color(0x00000000),
          // javascriptMode: JavascriptMode.unrestricted,
          // navigationDelegate: widget.navigationDelegate ??
          //     (NavigationRequest request) {
          //       return NavigationDecision.prevent;
          //     },
          // javascriptChannels: {
          //   JavascriptChannel(
          //       name: "Resize",
          //       onMessageReceived: (JavascriptMessage message) {
          //         double height = double.parse(message.message);
          //         setState(() {
          //           webViewHeight = height;
          //         });
          //       })
          // }
          ),
    );
  }

  String _genHtml({String? body, String? css}) {
    String html = _htmlPage;
    if (css != null) {
      html = html.replaceFirst("<!--HEAD-->", css);
    }

    if (body != null) {
      html = html.replaceFirst("<!--BODY-->", body);
    }

    return html;
  }

  @override
  void didUpdateWidget(covariant AutoSizeWebViewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.backgroundColor != widget.backgroundColor) {
      _backgroundColor = widget.backgroundColor;
    }

    if (oldWidget.htmlString != widget.htmlString ||
        oldWidget.cssString != widget.cssString) {
      _htmlString = _genHtml(
        body: widget.htmlString,
        css: widget.cssString,
      );

      webViewHeight = 10;

      if (widget.webViewController != null) {
        widget.webViewController.loadHtmlString(_htmlString);
      }
    }
  }
}
