/// FileName: WebTitleTabsWidget
///
/// @Author: ygc
/// @Date: 2024/6/13 10:21
/// @Description:
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../dto/update_titles_and_func/title_tab_item_dto.dart';

class WebTitleTabsWidget extends StatefulWidget {
  const WebTitleTabsWidget(
      {super.key, required this.tabs, required this.webViewController});

  final List<TitleTabItemDTO> tabs;
  final WebViewController webViewController;

  @override
  State<WebTitleTabsWidget> createState() => _WebTitleTabsWidgetState();
}

class _WebTitleTabsWidgetState extends State<WebTitleTabsWidget> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> list = [];
    for (int i = 0; i < widget.tabs.length; i++) {
      list.add(_buildTab(i, widget.tabs[i]));
      if (i < widget.tabs.length - 1) {
        list.add(const SizedBox(
          width: 24,
        ));
      }
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: list,
    );
  }

  Widget _buildTab(int index, TitleTabItemDTO dto) {
    return GestureDetector(
      onTap: () {
        currentIndex = index;
        widget.webViewController.runJavaScript(dto.click_func);

        setState(() {});
      },
      child: Text(
        dto.title,
        style: currentIndex == index
            ? const TextStyle(color: Colors.black)
            : const TextStyle(color: Colors.grey),
      ),
    );
  }
}
