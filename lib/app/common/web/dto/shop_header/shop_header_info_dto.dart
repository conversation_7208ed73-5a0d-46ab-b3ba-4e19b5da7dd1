/// FileName: shop_header_info_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:27
/// @Description:
import 'package:dnurse/app/common/web/dto/shop_header/shop_header_item_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_header_info_dto.freezed.dart';
part 'shop_header_info_dto.g.dart';

@freezed
class ShopHeaderInfoDTO with _$ShopHeaderInfoDTO {
  factory ShopHeaderInfoDTO({
    @Default(false) bool has,
    @Default([]) List<ShopHeaderItemDTO> child,
  }) = _ShopHeaderInfoDTO;

  ShopHeaderInfoDTO._();

  factory ShopHeaderInfoDTO.fromJson(Map<String, Object?> json) =>
      _$ShopHeaderInfoDTOFromJson(json);
}
