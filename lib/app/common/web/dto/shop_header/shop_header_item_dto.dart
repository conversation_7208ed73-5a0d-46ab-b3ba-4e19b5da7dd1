/// FileName: shop_header_info_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:27
/// @Description:
import 'package:dnurse/app/common/web/dto/shop_header/shop_header_action_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_header_item_dto.freezed.dart';
part 'shop_header_item_dto.g.dart';

@freezed
class ShopHeaderItemDTO with _$ShopHeaderItemDTO {
  factory ShopHeaderItemDTO({
    @Default("") String type,
    @Default("") String iconUrl,
    @Default("") String badge,
    @Default("") String textFont,
    @Default([]) List<String> searchArr,
    @Default(false) bool hasAction,
    ShopHeaderActionDTO? action,
  }) = _ShopHeaderItemDTO;

  ShopHeaderItemDTO._();

  factory ShopHeaderItemDTO.fromJson(Map<String, Object?> json) =>
      _$ShopHeaderItemDTOF<PERSON><PERSON><PERSON>(json);
}
