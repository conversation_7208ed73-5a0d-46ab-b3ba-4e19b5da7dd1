/// FileName: shop_header_info_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:27
/// @Description:
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_header_action_dto.freezed.dart';
part 'shop_header_action_dto.g.dart';

@freezed
class ShopHeaderActionDTO with _$ShopHeaderActionDTO {
  factory ShopHeaderActionDTO({
    @Default("") String actionType,
    @Default("") String clickFunc,
    @Default("") String linkHref,
  }) = _ShopHeaderActionDTO;

  ShopHeaderActionDTO._();

  factory ShopHeaderActionDTO.fromJson(Map<String, Object?> json) =>
      _$ShopHeaderActionDTOFromJson(json);
}
