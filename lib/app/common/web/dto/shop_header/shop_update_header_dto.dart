/// FileName: shop_update_header_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:22
/// @Description:

import 'package:dnurse/app/common/web/dto/shop_header/shop_header_info_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_update_header_dto.freezed.dart';
part 'shop_update_header_dto.g.dart';

@freezed
class ShopUpdateHeaderDTO with _$ShopUpdateHeaderDTO {
  factory ShopUpdateHeaderDTO({
    @Default("") String act,
    ShopHeaderInfoDTO? left,
    ShopHeaderInfoDTO? center,
    ShopHeaderInfoDTO? right,

  }) = _ShopUpdateHeaderDTO;

  ShopUpdateHeaderDTO._();

  factory ShopUpdateHeaderDTO.fromJson(Map<String, Object?> json) =>
      _$ShopUpdateHeaderDTOFromJson(json);
}
