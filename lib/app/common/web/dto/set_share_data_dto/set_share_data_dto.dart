/// FileName: shop_update_header_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:22
/// @Description:

// ignore_for_file: non_constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'set_share_data_dto.freezed.dart';
part 'set_share_data_dto.g.dart';

@freezed
class SetShareDataDTO with _$SetShareDataDTO {
  factory SetShareDataDTO({
    @Default("") String act,
    @Default("") String share_type,
    @Default("") String share_title,
    @Default("") String share_desc,
    @Default("") String share_url,
    @Default("") String share_icon,
  }) = _SetShareDataDTO;

  SetShareDataDTO._();

  factory SetShareDataDTO.fromJson(Map<String, Object?> json) =>
      _$SetShareDataDTOFromJson(json);
}
