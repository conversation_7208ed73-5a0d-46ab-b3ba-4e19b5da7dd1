/// FileName: add_comment_dto
///
/// @Author: ygc
/// @Date: 2024/6/20 11:16
/// @Description:
///

// ignore_for_file: non_constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_comment_dto.freezed.dart';
part 'add_comment_dto.g.dart';

@freezed
class AddCommentDTO with _$AddCommentDTO {
  factory AddCommentDTO({
    @Default("") String act,
    @Default(0) int aid,
    @Default(0) int comment_id,
    @Default("") String comment_to_name,
    @Default("") String comment_to_sn,
  }) = _AddCommentDTO;

  AddCommentDTO._();

  factory AddCommentDTO.fromJson(Map<String, Object?> json) =>
      _$AddCommentDTOFromJson(json);
}
