/// FileName: set_close_flag_dto
///
/// @Author: ygc
/// @Date: 2024/6/14 14:24
/// @Description:
///

// ignore_for_file: non_constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'set_close_flag_dto.freezed.dart';
part 'set_close_flag_dto.g.dart';

@freezed
class SetCloseFlagDTO with _$SetCloseFlagDTO {
  factory SetCloseFlagDTO({
    @Default("") String act,
    @Default(0) int isClose,
    @Default("") String goUrl,
  }) = _SetCloseFlagDTO;

  SetCloseFlagDTO._();

  factory SetCloseFlagDTO.fromJson(Map<String, Object?> json) =>
      _$SetCloseFlagDTOFromJson(json);
}
