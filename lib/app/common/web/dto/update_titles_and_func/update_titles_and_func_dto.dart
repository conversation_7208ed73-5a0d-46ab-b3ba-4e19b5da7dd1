/// FileName: shop_update_header_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:22
/// @Description:

// ignore_for_file: non_constant_identifier_names

import 'package:dnurse/app/common/web/dto/update_titles_and_func/title_tab_item_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'update_titles_and_func_dto.freezed.dart';
part 'update_titles_and_func_dto.g.dart';

@freezed
class UpdateTitlesAndFuncDTO with _$UpdateTitlesAndFuncDTO {
  factory UpdateTitlesAndFuncDTO({
    @Default("") String act,
    List<TitleTabItemDTO>? title_tab,
    @Default("")  String iconUrl,
    @Default("") String actUrl,
  }) = _UpdateTitlesAndFuncDTO;

  UpdateTitlesAndFuncDTO._();

  factory UpdateTitlesAndFuncDTO.fromJson(Map<String, Object?> json) =>
      _$UpdateTitlesAndFuncDTOFromJson(json);
}
