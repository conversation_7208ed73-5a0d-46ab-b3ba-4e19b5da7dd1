/// FileName: shop_update_header_dto
///
/// @Author: ygc
/// @Date: 2024/6/12 15:22
/// @Description:

// ignore_for_file: non_constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'title_tab_item_dto.freezed.dart';
part 'title_tab_item_dto.g.dart';

@freezed
class TitleTabItemDTO with _$TitleTabItemDTO {
  factory TitleTabItemDTO({
    @Default("") String title,
    @Default("") String click_func,
  }) = _TitleTabItemDTO;

  TitleTabItemDTO._();

  factory TitleTabItemDTO.fromJson(Map<String, Object?> json) =>
      _$TitleTabItemDTOFromJson(json);
}
