/// FileName: wechat_service
///
/// @Author: ygc
/// @Date: 2024/11/11 18:34
/// @Description:

import 'dart:async';

import './tencent_api/tencent_api.dart';

import './tencent_api/model/tencent_api_resp.dart';
import 'package:dnurse/config/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/file.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:tencent_kit/tencent_kit.dart';

import '../../../framework/utils/log.dart';

class QQUserInfo {
  final String openId;
  String? unionId;
  final String nickName;
  final String accessToken;
  final String avatar;

  QQUserInfo({
    required this.openId,
    this.unionId,
    required this.nickName,
    required this.accessToken,
    required this.avatar,
  });

  @override
  String toString() {
    return "WechatUserInfo(openId: $openId, unionId: $unionId, nickName: $nickName, accessToken: $accessToken, avatar: $avatar)";
  }
}

class QQError {
  final String code;
  final String message;

  QQError({required this.code, required this.message});

  @override
  String toString() {
    return "WechatError(code: $code, msg: $message)";
  }
}

class TencentService extends GetxService {
  static TencentService get to => Get.find();
  final TencentKitPlatform _tencentKitPlatform = TencentKitPlatform.instance;
  late final StreamSubscription<TencentResp> _respSubs;

  Function(QQUserInfo)? _onLoginSuccess;
  Function(QQError)? _onLoginError;
  VoidCallback? _onShareSuccess;
  Function(QQError)? _onShareError; // 添加专门的分享错误回调

  @override
  void onInit() {
    super.onInit();

    _respSubs = _tencentKitPlatform.respStream().listen(_listenResp);
    Log.i(
        "QQLoginAppIdSecret: ${AppConstants.tencentAppId}-${AppConstants.tencentAppSecret}");
    _tencentKitPlatform.setIsPermissionGranted(granted: true);

    // TODO: 检查参数
    _tencentKitPlatform.registerApp(
        appId: AppConstants.tencentAppId,
        universalLink: AppConstants.tencentAppUniversalLink);
  }

  void _listenResp(TencentResp resp) {
    if (!resp.isSuccessful) {
      // 区分处理不同类型的错误响应
      if (resp is TencentShareMsgResp) {
        // 处理分享错误
        _handleShareError(resp);
      } else {
        // 处理其他错误（如登录错误）
        _handelError(resp);
      }
      return;
    }

    if (resp is TencentLoginResp) {
      final String content = 'login: ${resp.openid} - ${resp.accessToken}';
      Log.d('登录信息：$content');
      _handleAuthResp(resp);
    } else if (resp is TencentShareMsgResp) {
      final String content = 'share: ${resp.ret} - ${resp.msg}';
      Log.d('分享信息：$content');
      _onShareSuccess?.call();
    }
  }

  // 处理分享错误
  void _handleShareError(TencentResp resp) {
    String message = "分享失败";
    if (resp.ret == TencentResp.kRetUserCancel) {
      message = "用户取消分享";
    } else if (resp.ret == TencentResp.kRetFailed) {
      message = "分享失败";
    } else {
      message = resp.msg ?? "未知错误";
    }

    Log.e("QQ分享错误: $message (${resp.ret})");
    _onShareError?.call(QQError(code: resp.ret.toString(), message: message));
  }

  void login(
      {required Function(QQUserInfo) onLoginSuccess,
      required Function(QQError) onLoginError}) {
    _onLoginSuccess = onLoginSuccess;
    _onLoginError = onLoginError;

    _tencentKitPlatform.login(scope: [TencentScope.kAll]);
  }

  void _handleAuthResp(TencentLoginResp qqLoginResp) async {
    Log.i('resp :${qqLoginResp}');

    // final WechatAccessTokenResp accessTokenResp =
    //     await WechatExtension.getAccessTokenUnionID(
    //   appId: AppConstants.wechatAppId,
    //   appSecret: AppConstants.wechatAppSecret,
    //   code: resp.code!,
    // );

    // Log.i('accessTokenResp :${accessTokenResp.toJson()}');

    if (qqLoginResp.isSuccessful) {
      final TencentUserInfoResp qqUserInfoResp = await TencentApi.getUserInfo(
        appId: AppConstants.tencentAppId,
        openid: qqLoginResp.openid!,
        accessToken: qqLoginResp.accessToken!,
      );
      if (qqUserInfoResp.isSuccessful) {
        Log.d(
            '用户信息: ${qqUserInfoResp.nickname} - ${qqUserInfoResp.gender} - ${qqUserInfoResp.genderType}');

        QQUserInfo qqUserInfo = QQUserInfo(
          openId: qqLoginResp.openid!,
          nickName: qqUserInfoResp.nickname!,
          accessToken: qqLoginResp.accessToken!,
          avatar: qqUserInfoResp.figureurl!,
        );

        Log.i('qqUserInfo : $qqUserInfo');
        _onLoginSuccess?.call(qqUserInfo);
        return;
      } else {
        Log.d('用户信息错误：${qqUserInfoResp.ret} - ${qqUserInfoResp.msg}');
      }
    }
    _onLoginError?.call(QQError(code: "100000", message: "授权失败"));
  }

  void _handelError(TencentResp resp) {
    String message = "QQ授权失败";
    if (resp.ret == TencentResp.kRetUserCancel) {
      message = "用户取消";
    } else if (resp.ret == TencentResp.kRetFailed) {
      message = "未知错误";
    } else {
      message = resp.msg ?? '未知错误';
    }

    _onLoginError?.call(QQError(code: resp.ret.toString(), message: message));
  }

  /// 分享图片到QQ好友
  void shareShareImage({
    required String url,
    required Function() onSuccess,
    required Function(QQError) onError,
  }) async {
    _onShareSuccess = onSuccess;
    _onShareError = onError; // 使用正确的分享错误回调

    try {
      Uri fileUri;
      if (!url.startsWith("http")) {
        fileUri = Uri.file(url);
      } else {
        final File file = await DefaultCacheManager().getSingleFile(url);
        fileUri = Uri.file(file.path);
      }

      Log.i("分享图片到QQ好友: $url");
      await _tencentKitPlatform.shareImage(
        scene: TencentScene.kScene_QQ,
        imageUri: fileUri,
        appName: "DNurse", // 添加应用名称
      );
    } catch (e) {
      Log.e("分享图片到QQ好友出错: $e");
      onError(QQError(code: "-1", message: "分享失败: $e"));
    }
  }

  // 分享图片到QQ
  void shareShareImageToQzone(
      {required String url,
      required Function() onSuccess,
      required Function(QQError) onError,
      required String title,
      required String summary,
      required String targetUrl,
      required int scene}) async {
    _onShareSuccess = onSuccess;
    _onShareError = onError; // 使用正确的分享错误回调

    try {
      Uri? imageUri;
      String targetUrl = "https://dnurse.com"; // 默认目标网址

      if (!url.startsWith("http")) {
        imageUri = Uri.file(url);
      } else {
        final File file = await DefaultCacheManager().getSingleFile(url);
        imageUri = Uri.file(file.path);
      }

      Log.i("分享图片到QQ空间: $url");

      // 使用 shareWebpage 方法代替 shareImage 方法
      // QQ空间分享需要网页类型的分享
      await _tencentKitPlatform.shareWebpage(
        scene: scene,
        title: title ?? "DNurse分享",
        summary: summary ?? "来自DNurse的分享",
        targetUrl: targetUrl, // 必须提供一个有效的URL
        imageUri: imageUri, // 提供图片作为缩略图
        appName: "DNurse",
      );
    } catch (e) {
      Log.e("分享图片到QQ空间出错: $e");
      onError(QQError(code: "-1", message: "分享失败: $e"));
    }
  }

  /// 分享图片到QQ群
  void shareShareImageToQQGroup({
    required String url,
    required Function() onSuccess,
    required Function(QQError) onError,
  }) async {
    _onShareSuccess = onSuccess;
    _onShareError = onError; // 使用正确的分享错误回调

    try {
      Uri fileUri;
      if (!url.startsWith("http")) {
        fileUri = Uri.file(url);
      } else {
        final File file = await DefaultCacheManager().getSingleFile(url);
        fileUri = Uri.file(file.path);
      }

      Log.i("分享图片到QQ群: $url");

      // 注意：QQ SDK 可能不直接支持 QQ 群分享
      // 这里使用 TencentScene.kScene_QQ 并添加特殊的应用名称以区分
      await _tencentKitPlatform.shareImage(
        scene: TencentScene.kScene_QQ,
        imageUri: fileUri,
        appName: "DNurse群分享", // 添加特殊应用名称以区分
      );
    } catch (e) {
      Log.e("分享图片到QQ群出错: $e");
      onError(QQError(code: "-1", message: "分享失败: $e"));
    }
  }
}
