/// FileName: boot_service
///
/// @Author: ygc
/// @Date: 2025/6/18 10:14
/// @Description:
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dnurse/app/common/api/boot_api.dart';
import 'package:dnurse/framework/service/key_value_storage.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/utils/log.dart';
import '../../../framework/utils/network_utils.dart';
import '../api/dto/boot_item_dto/boot_item_dto.dart';
import 'base_service.dart';

const bootItemKey = "boot_item";
const _flashAdShowTimesKey = "flash_ad_show_times";

class BootImageCacheManager {
  static const key = 'bootImageCacheKey';
  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 7),
      maxNrOfCacheObjects: 20,
      repo: JsonCacheInfoRepository(databaseName: key),
      fileSystem: IOFileSystem(key),
      fileService: HttpFileService(),
    ),
  );

  static Future<FileInfo?> getImage(String url) {
    return instance.getFileFromCache(url);
  }

  static Future<File> getFile(String url) {
    return instance.getSingleFile(url);
  }
}

class BootService extends BaseService {
  BootService();

  static BootService get to => Get.find();

  Future<void> getLatestItem(
    int height,
    int width,
    int density,
  ) async {
    final param = {
      'dev_type': 'android',
      'height': height,
      'width': width,
      'density': density,
    };

    Log.i('getLatestItem param: $param');
    final JsonResponse response = await BootApi.getLatestItems(param);
    Log.i('getLatestItem response: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }

    if (!response.hasData && response.dataList != null) {
      return;
    }

    List<dynamic> dataList = response.dataList!.where((e) {
      return e != null;
    }).toList();
    Log.i('getLatestItem dataList: $dataList');

    await _saveItems(dataList);
  }

  void _saveBootItem(List<dynamic> dataList) async {
    String json = "";

    if (dataList.isNotEmpty) {
      try {
        json = jsonEncode(dataList);
      } catch (e) {
        Log.e("_saveBootItem dataList: $dataList,  error: $e");
        return;
      }
    }

    KeyValueStorage.to.setString(bootItemKey, json);
  }

  Future<void> _saveItems(List<dynamic> dataList) async {
    _saveBootItem(dataList);

    if (dataList.isEmpty) {
      return;
    }

    List<BootItemDTO> itemList = dataList.map((e) {
      return BootItemDTO.fromJson(e);
    }).toList();

    for (BootItemDTO item in itemList) {
      if (item.type.isEmpty) {
        continue;
      }

      String requestUrl = item.pic;
      if (item.type == "2") {
        requestUrl = item.videoUrl;
        if (!await NetworkUtils.isWifi()) {
          continue;
        }
      }

      if (requestUrl.isEmpty) {
        continue;
      }

      if (item.type == "2") {
        if (!await NetworkUtils.isWifi()) {
          continue;
        }
      }

      await BootImageCacheManager.getFile(requestUrl);
    }
  }

  List<BootItemDTO> getCacheBootItems() {
    String json = KeyValueStorage.to.getString(bootItemKey);
    if (json.isEmpty) {
      return [];
    }

    try {
      List<dynamic> dataList = jsonDecode(json);
      return dataList.map((e) {
        return BootItemDTO.fromJson(e);
      }).toList();
    } catch (e) {
      print(e);
      return [];
    }
  }

  Map<String, dynamic> getBootShowTimes() {
    String json = KeyValueStorage.to.getString(_flashAdShowTimesKey);
    if (json.isEmpty) {
      return {};
    }
    try {
      return jsonDecode(json);
    } catch (e) {
      print(e);
      return {};
    }
  }

  int _getItemShowTimes(String itemId, Map<String, dynamic> showTimes) {
    if (showTimes.containsKey(itemId)) {
      return int.tryParse(showTimes[itemId].toString()) ?? 0;
    }
    return 0;
  }

  BootItemDTO? randomBootItem() {
    List<BootItemDTO> items = getCacheBootItems();

    if (items.isEmpty) {
      return null;
    }

    Map<String, dynamic> showTimes = getBootShowTimes();

    List<BootItemDTO> validItems = [];

    int currentTime = DateTime.now().secondsSinceEpoch;

    for (BootItemDTO item in items) {
      if (item.type.isEmpty || item.pic.isEmpty) {
        continue;
      }

      int hasShowTimes = _getItemShowTimes(item.id, showTimes);
      int frequency = int.tryParse(item.frequency) ?? 0;
      if (frequency != 0 && hasShowTimes > 0 && hasShowTimes >= frequency) {
        continue;
      }

      int startTime = int.tryParse(item.startTimestamp) ?? 0;
      if (startTime > 0 && startTime > currentTime) {
        continue;
      }

      int endTime = int.tryParse(item.endTimeStamp) ?? 0;
      if (endTime > 0 && endTime < currentTime) {
        continue;
      }

      validItems.add(item);
    }

    if (validItems.isEmpty) {
      return null;
    }

    final random = Random();
    return validItems[random.nextInt(validItems.length)];
  }

  void increaseShowTimes(BootItemDTO item) {
    int frequency = int.tryParse(item.frequency) ?? 0;
    if (frequency == 0) {
      return;
    }

    Map<String, dynamic> showTimes = getBootShowTimes();
    int hasShowTimes = _getItemShowTimes(item.id, showTimes);
    showTimes[item.id] = hasShowTimes + 1;
    KeyValueStorage.to.setString(_flashAdShowTimesKey, jsonEncode(showTimes));
  }
}
