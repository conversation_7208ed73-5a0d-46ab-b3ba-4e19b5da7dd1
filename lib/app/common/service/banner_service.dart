/// FileName: banner_service
///
/// @Author: ygc
/// @Date: 2025/6/23 09:52
/// @Description:
import 'dart:convert';
import 'dart:io';

import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/service/key_value_storage.dart';
import '../../../framework/utils/log.dart';
import '../api/banner_api.dart';
import '../api/dto/notice_item_dto/notice_item_dto.dart';
import '../entity/first_popup/first_popup_info_item.dart';
import 'base_service.dart';

const _bannerItemKey = "first_popup_item";
const _bannerAdShowTimesKey = "first_popup_show_times";

class FirstPopupImageCacheManager {
  static const key = 'firstPopupImageCacheKey';
  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 14),
      maxNrOfCacheObjects: 20,
      repo: JsonCacheInfoRepository(databaseName: key),
      fileSystem: IOFileSystem(key),
      fileService: HttpFileService(),
    ),
  );

  static Future<FileInfo?> getImage(String url) {
    return instance.getFileFromCache(url);
  }

  static Future<File> getFile(String url) {
    return instance.getSingleFile(url);
  }
}

class BannerService extends BaseService {
  BannerService();

  static BannerService get to => Get.find();

  Map<String, dynamic> getNeedListParam(bool isBanner, bool isPersonality) {
    String location = isBanner ? "APP患者端首页 广告位" : "APP患者端首页 弹框";
    String target = isBanner ? "ad" : "box";
    Map<String, dynamic> param = {
      'notice_location': location,
      'notice_target': target,
    };

    if (isPersonality) {
      param['type'] = 1;
    }

    return param;
  }

  Future<void> getFirstPopupList() async {
    final JsonResponse response =
        await BannerApi.getNeedList(getNeedListParam(false, false));
    Log.i('getFirstPopList response: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }

    if (!response.hasData || response.dataMap == null) {
      return;
    }

    List<dynamic>? data = response.dataMap?['data'] as List<dynamic>?;
    if (data == null) {
      return;
    }

    List<dynamic> dataList = data.where((e) {
      return e != null;
    }).toList();
    Log.i('getLatestItem dataList: $dataList');

    await _saveFirstPopItems(dataList);
    //   NoticeItemDTO
  }

  Future<void> _saveFirstPopItems(List<dynamic> dataList) async {
    _saveFirstPopItem(dataList);

    if (dataList.isEmpty) {
      return;
    }

    List<NoticeItemDTO> itemList = dataList.map((e) {
      return NoticeItemDTO.fromJson(e);
    }).toList();

    for (NoticeItemDTO item in itemList) {
      if (item.noticePic.isEmpty) {
        continue;
      }

      String requestUrl = item.noticePic;
      await FirstPopupImageCacheManager.getFile(requestUrl);
    }
  }

  void _saveFirstPopItem(List<dynamic> dataList) async {
    String json = "";

    if (dataList.isNotEmpty) {
      try {
        json = jsonEncode(dataList);
      } catch (e) {
        Log.e("_saveBootItem dataList: $dataList,  error: $e");
        return;
      }
    }

    KeyValueStorage.to.setString(_bannerItemKey, json);
  }

  /// 获取缓存的弹窗
  List<NoticeItemDTO> getCacheFirstPopupItems() {
    String json = KeyValueStorage.to.getString(_bannerItemKey);
    if (json.isEmpty) {
      return [];
    }

    try {
      List<dynamic> dataList = jsonDecode(json);
      return dataList.map((e) {
        return NoticeItemDTO.fromJson(e);
      }).toList();
    } catch (e) {
      print(e);
      return [];
    }
  }

  /// 获取弹窗已显示信息
  FirstPopupInfoItem? _getItemShowInfo(
      String itemId, Map<String, FirstPopupInfoItem> showTimes) {
    if (showTimes.containsKey(itemId)) {
      return showTimes[itemId];
    }
    return null;
  }

  Map<String, FirstPopupInfoItem> getFirstPopupShowInfo() {
    String json = KeyValueStorage.to.getString(_bannerAdShowTimesKey);
    if (json.isEmpty) {
      // 缓存为空，则返回空
      return {};
    }

    try {
      Map<String, dynamic> jsonMap = jsonDecode(json);

      // 转换为Map
      return jsonMap.map((key, value) {
        return MapEntry(key, FirstPopupInfoItem.fromJson(value));
      });
    } catch (e) {
      Log.e("getFirstPopupShowInfo: $e");
      return {};
    }
  }

  /// 获取首页弹窗
  NoticeItemDTO? getFirstPopupItem() {
    List<NoticeItemDTO> items = getCacheFirstPopupItems();

    if (items.isEmpty) {
      // 数据为空，则返回null
      return null;
    }

    // 获取弹窗已显示信息
    Map<String, FirstPopupInfoItem> showTimes = getFirstPopupShowInfo();

    // 获取当前时间
    int currentTime = DateTime.now().secondsSinceEpoch;

    for (NoticeItemDTO item in items) {
      if (item.noticePic.isEmpty) {
        // 图片为空，则跳过
        continue;
      }

      FirstPopupInfoItem? hasShowTimes =
          _getItemShowInfo(item.noticeId, showTimes);

      int startTime = int.tryParse(item.startTime) ?? 0;
      int endTime = int.tryParse(item.endTime) ?? 0;
      if (startTime <= 0 || endTime <= 0) {
        // 时间段为空，则跳过
        continue;
      }

      if (startTime > 0 && startTime * 1000 > currentTime) {
        // 时间段未开始，则跳过
        continue;
      }

      if (endTime > 0 && endTime * 1000 < currentTime) {
        // 时间段已结束，则跳过
        continue;
      }

      if (hasShowTimes != null) {
        int totalTimes = int.tryParse(item.totalTimes) ?? 0;
        int timeInterval =
            int.tryParse(item.timeInterval) ?? 0 * 60 * 60 * 1000;
        if (totalTimes > 0 && hasShowTimes.times >= totalTimes) {
          // 显示次数已满，则跳过
          continue;
        }

        if (hasShowTimes.lastTime > 0 &&
            (hasShowTimes.lastTime + timeInterval) < currentTime) {
          // 时间间隔未到，则跳过
          continue;
        }
      }

      return item;
    }

    // 没有符合条件的弹窗，则返回null
    return null;
  }

  /// 保存首页弹窗显示信息
  ///
  /// 该方法用于记录给定通知项的首次弹窗显示信息。通常在用户首次查看通知详情时调用，
  /// 以标记该通知已被用户查看。这有助于在用户界面中决定是否显示弹窗提示。
  ///
  /// 参数:
  ///   item - 首页显示弹窗的通知项
  ///
  void saveFirstPopupShowInfo(NoticeItemDTO item) {
    int totalTimes = int.tryParse(item.totalTimes) ?? 0;
    int interval = int.tryParse(item.timeInterval) ?? 0;
    if (totalTimes == 0 && interval == 0) {
      // 没有限制不保存
      return;
    }

    Map<String, FirstPopupInfoItem> showTimes = getFirstPopupShowInfo();
    FirstPopupInfoItem? hasShowTimes =
        _getItemShowInfo(item.noticeId, showTimes);

    if (hasShowTimes == null) {
      hasShowTimes = FirstPopupInfoItem(
        times: 0,
        lastTime: 0,
      );
    }

    hasShowTimes = hasShowTimes.copyWith(
      times: hasShowTimes.times + 1,
      lastTime: DateTime.now().secondsSinceEpoch,
    );

    showTimes[item.noticeId] = hasShowTimes;
    KeyValueStorage.to.setString(_bannerAdShowTimesKey, jsonEncode(showTimes));
  }
}
