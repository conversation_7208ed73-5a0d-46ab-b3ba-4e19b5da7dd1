/// FileName: share_helper
///
/// @Author: ygc
/// @Date: 2025/05/29 16:40
/// @Description: 统一处理分享功能的辅助类

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:tencent_kit/tencent_kit.dart';
import 'package:wechat_kit/wechat_kit.dart';
import 'package:wechat_kit_extension/wechat_kit_extension.dart';
import '../../../framework/utils/log.dart';
import '../../../ui/widgets/dnu_toast.dart';
import 'tencent_service.dart';
import 'wechat_service.dart';

/// 分享平台类型
enum SharePlatform {
  /// 微信
  wechat,

  /// 微信朋友圈
  wechatTimeline,

  /// 微信收藏
  wechatFavorite,

  /// QQ
  qq,

  /// QQ空间
  qzone,

  /// QQ群
  qqGroup,
}

/// 分享错误类
class ShareError {
  final String code;
  final String message;

  ShareError({required this.code, required this.message});

  @override
  String toString() {
    return "ShareError(code: $code, msg: $message)";
  }
}

/// 分享辅助类
class ShareHelper {
  /// 单例实例
  static final ShareHelper _instance = ShareHelper._internal();

  /// 工厂构造函数
  factory ShareHelper() => _instance;

  /// 内部构造函数
  ShareHelper._internal();

  /// 分享图片
  ///
  /// [platform] 分享平台
  /// [url] 图片URL或本地路径
  /// [context] 上下文
  /// [onSuccess] 成功回调
  /// [onError] 错误回调
  void shareImage({
    required SharePlatform platform,
    required String url,
    required BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
    String title = "",
    String summary = "",
    String targetUrl = "",
  }) {
    Log.i("shareImage: platform=$platform, url=$url");

    switch (platform) {
      case SharePlatform.wechat:
        _shareImageToWechat(url, context, onSuccess, onError);
        break;
      case SharePlatform.wechatTimeline:
        // 支持朋友圈分享
        _shareImageToWechatTimeline(url, context, onSuccess, onError);
        break;
      case SharePlatform.wechatFavorite:
        // 支持收藏分享
        _shareImageToWechatFavorite(url, context, onSuccess, onError);
        break;
      case SharePlatform.qq:
        _shareImageToQQ(url, context, onSuccess, onError);
        break;
      case SharePlatform.qzone:
        // QQ空间分享
        _shareImageToQzone(
            url, context, onSuccess, onError, title, summary, targetUrl);
        break;
      case SharePlatform.qqGroup:
        // QQ群分享
        _shareImageToQQGroup(url, context, onSuccess, onError);
        break;
    }
  }

  /// 分享文本
  ///
  /// [platform] 分享平台
  /// [text] 文本内容
  /// [context] 上下文
  /// [onSuccess] 成功回调
  /// [onError] 错误回调
  void shareText({
    required SharePlatform platform,
    required String text,
    required BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  }) {
    Log.i("shareText: platform=$platform, text=$text");

    // 目前SDK不支持文本分享，提示用户
    if (onError != null) {
      onError(ShareError(code: "-1", message: "暂不支持文本分享"));
    } else if (context.mounted) {
      DNUToast.show("暂不支持文本分享", context);
    }
  }

  /// 分享网页
  ///
  /// [platform] 分享平台
  /// [url] 网页URL
  /// [title] 标题
  /// [description] 描述
  /// [thumbnailUrl] 缩略图URL
  /// [context] 上下文
  /// [onSuccess] 成功回调
  /// [onError] 错误回调
  void shareWebpage({
    required SharePlatform platform,
    required String url,
    required String title,
    String? description,
    String? thumbnailUrl,
    required BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  }) {
    Log.i("shareWebpage: platform=$platform, url=$url, title=$title");

    // 目前SDK不支持网页分享，提示用户
    if (onError != null) {
      onError(ShareError(code: "-1", message: "暂不支持网页分享"));
    } else if (context.mounted) {
      DNUToast.show("暂不支持网页分享", context);
    }
  }

/***
 * 图片分享相关
 */

  /// 分享到微信
  void _shareImageToWechat(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) {
    _shareImageToWechatWithScene(
        url, WechatScene.kSession, context, onSuccess, onError);
  }

  /// 分享到微信朋友圈
  void _shareImageToWechatTimeline(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) {
    // 直接调用WechatService的朋友圈分享方法
    WechatService.to.shareImage(
      url: url,
      scene: WechatScene.kTimeline,
      onSuccess: () {
        if (onSuccess != null) {
          onSuccess();
        } else if (context.mounted) {
          DNUToast.show("分享成功", context);
        }
      },
      onError: (error) {
        if (onError != null) {
          onError(ShareError(code: error.code, message: error.message));
        } else if (context.mounted) {
          DNUToast.show(error.message, context);
        }
      },
    );
  }

  /// 分享到微信收藏
  void _shareImageToWechatFavorite(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) {
    // 直接调用WechatService的收藏分享方法
    WechatService.to.shareImage(
      url: url,
      scene: WechatScene.kFavorite,
      onSuccess: () {
        if (onSuccess != null) {
          onSuccess();
        } else if (context.mounted) {
          DNUToast.show("分享成功", context);
        }
      },
      onError: (error) {
        if (onError != null) {
          onError(ShareError(code: error.code, message: error.message));
        } else if (context.mounted) {
          DNUToast.show(error.message, context);
        }
      },
    );
  }

  /// 根据场景分享图片到微信
  void _shareImageToWechatWithScene(
    String url,
    int scene,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) async {
    try {
      // 目前只支持微信会话分享，朋友圈和收藏暂不支持
      if (scene == WechatScene.kSession) {
        // 分享到微信好友
        WechatService.to.shareImage(
          url: url,
          scene: WechatScene.kSession,
          onSuccess: () {
            if (onSuccess != null) {
              onSuccess();
            } else if (context.mounted) {
              DNUToast.show("分享成功", context);
            }
          },
          onError: (error) {
            if (onError != null) {
              onError(ShareError(code: error.code, message: error.message));
            } else if (context.mounted) {
              DNUToast.show(error.message, context);
            }
          },
        );
      } else {
        // 朋友圈和收藏分享暂不支持，提示用户
        String message =
            scene == WechatScene.kTimeline ? "暂不支持朋友圈分享" : "暂不支持收藏分享";
        Log.i("微信分享: $message");

        if (onError != null) {
          onError(ShareError(code: "-1", message: message));
        } else if (context.mounted) {
          DNUToast.show(message, context);
        }
      }
    } catch (e) {
      Log.e("分享图片到微信出错: $e");
      if (onError != null) {
        onError(ShareError(code: "-1", message: "分享失败: $e"));
      } else if (context.mounted) {
        DNUToast.show("分享失败", context);
      }
    }
  }

  /// 分享到QQ
  void _shareImageToQQ(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) {
    TencentService.to.shareShareImage(
      url: url,
      onSuccess: () {
        if (onSuccess != null) {
          onSuccess();
        } else if (context.mounted) {
          DNUToast.show("分享成功", context);
        }
      },
      onError: (error) {
        if (onError != null) {
          onError(ShareError(code: error.code, message: error.message));
        } else if (context.mounted) {
          DNUToast.show(error.message, context);
        }
      },
    );
  }

  /// 分享到QQ空间
  void _shareImageToQzone(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
    String title,
    String summary,
    String targetUrl,
  ) {
    TencentService.to.shareShareImageToQzone(
      url: url,
      title: title,
      onSuccess: () {
        if (onSuccess != null) {
          onSuccess();
        } else if (context.mounted) {
          DNUToast.show("分享成功", context);
        }
      },
      onError: (error) {
        if (onError != null) {
          onError(ShareError(code: error.code, message: error.message));
        } else if (context.mounted) {
          DNUToast.show(error.message, context);
        }
      },
      summary: summary,
      targetUrl: targetUrl,
      scene: TencentScene.kScene_QZone,
    );
  }

  /// 分享到QQ群
  void _shareImageToQQGroup(
    String url,
    BuildContext context,
    VoidCallback? onSuccess,
    Function(ShareError)? onError,
  ) {
    TencentService.to.shareShareImageToQQGroup(
      url: url,
      onSuccess: () {
        if (onSuccess != null) {
          onSuccess();
        } else if (context.mounted) {
          DNUToast.show("分享成功", context);
        }
      },
      onError: (error) {
        if (onError != null) {
          onError(ShareError(code: error.code, message: error.message));
        } else if (context.mounted) {
          DNUToast.show(error.message, context);
        }
      },
    );
  }
}

/**
 * 网页分享相关
 */
/// 分享网页
///
/// [platform] 分享平台
/// [url] 网页URL
/// [title] 标题
/// [description] 描述
/// [thumbnailUrl] 缩略图URL
/// [context] 上下文
/// [onSuccess] 成功回调
/// [onError] 错误回调
// void shareWebpage({
//   required SharePlatform platform,
//   required String url,
//   required String title,
//   String? description,
//   String? thumbnailUrl,
//   required BuildContext context,
//   VoidCallback? onSuccess,
//   Function(ShareError)? onError,
// }) {
//   Log.i("shareWebpage: platform=$platform, url=$url, title=$title");

//   switch (platform) {
//     case SharePlatform.wechat:
//       WechatService.to.shareWebpage(
//         url: url,
//         title: title,
//         description: description,
//         thumbnailUrl: thumbnailUrl,
//         onSuccess: () {
//           if (onSuccess != null) {
//             onSuccess();
//           } else if (context.mounted) {
//             DNUToast.show("分享成功", context);
//           }
//         },
//         onError: (error) {
//           if (onError != null) {
//             onError(ShareError(code: error.code, message: error.message));
//           } else if (context.mounted) {
//             DNUToast.show(error.message, context);
//           }
//         },
//       );
//       break;
//     case SharePlatform.qq:
//       TencentService.to.shareShareImage(
//         url: url,
//         onSuccess: () {
//           if (onSuccess != null) {
//             onSuccess();
//           } else if (context.mounted) {
//             DNUToast.show("分享成功", context);
//           }
//         },
//         onError: (error) {
//           if (onError != null) {
//             onError(ShareError(code: error.code, message: error.message));
//           } else if (context.mounted) {
//             DNUToast.show(error.message, context);
//           }
//         },
//       );
//       break;
//     case SharePlatform.wechatTimeline:
//       WechatService.to.shareWebpage(
//         url: url,
//         title: title,
//         description: description,
//         thumbnailUrl: thumbnailUrl,
//         onSuccess: () {
//           if (onSuccess != null) {
//             onSuccess();
//           } else if (context.mounted) {
//             DNUToast.show("分享成功", context);
//           }
//         },
//         onError: (error) {
//           if (onError != null) {
//             onError(ShareError(code: error.code, message: error.message));
//           } else if (context.mounted) {
//             DNUToast.show(error.message, context);
//           }
//         },
//       );
//       break;
//     case SharePlatform.qqGroup:
//       TencentService.to.shareShareImageToQQGroup(
//         url: url,
//         onSuccess: () {
//           if (onSuccess != null) {
//             onSuccess();
//           } else if (context.mounted) {
//             DNUToast.show("分享成功", context);
//           }
//         },
//         onError: (error) {
//           if (onError != null) {
//             onError(ShareError(code: error.code, message: error.message));
//           } else if (context.mounted) {
//             DNUToast.show(error.message, context);
//           }
//         },
//       );
//       break;
//     case SharePlatform.qzone:
//       TencentService.to.shareShareImageToQzone(
//         url: url,
//         onSuccess: () {
//           if (onSuccess != null) {
//             onSuccess();
//           } else if (context.mounted) {
//             DNUToast.show("分享成功", context);
//           }
//         },
//         onError: (error) {
//           if (onError != null) {
//             onError(ShareError(code: error.code, message: error.message));
//           } else if (context.mounted) {
//             DNUToast.show(error.message, context);
//           }
//         },
//       );
//       break;
//   }
// }
