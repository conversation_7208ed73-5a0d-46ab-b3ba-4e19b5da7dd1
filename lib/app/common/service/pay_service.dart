/// FileName: pay_service
///
/// @Author: ygc
/// @Date: 2025/6/11 10:30
/// @Description:
import 'dart:async';

import 'package:alipay_kit/alipay_kit.dart';
import 'package:dnurse/app/common/service/wechat_service.dart';
import 'package:get/get.dart';
import 'package:wechat_kit/wechat_kit.dart';

enum PayType {
  wechat,
  alipay,
}

enum PayResultCode {
  success,
  handling,
  failed,
  repeatedRequest,
  cancelled,
  networkError,
  notSupport,
  unknown,
}

class PayResult {
  PayResult({
    required this.type,
    required this.resultCode,
    this.message,
    this.result,
  });

  final PayType type;
  final PayResultCode resultCode;
  final String? message;
  final String? result;
}

abstract class DnursePayHandler {
  void onPayResult(PayResult payResult);
}

class PayService extends GetxService implements WechatPayHandler {
  PayService();

  late final StreamSubscription<AlipayResp> _aliPaySubs;

  static PayService get to => Get.find();

  final List<DnursePayHandler> _payHandlers = [];

  void addPayHandler(DnursePayHandler handler) {
    if (!_payHandlers.contains(handler)) {
      _payHandlers.add(handler);
    }
  }

  void removePayHandler(DnursePayHandler handler) {
    _payHandlers.remove(handler);
  }

  void _notifyPayResult(PayResult payResult) {
    for (var handler in _payHandlers) {
      handler.onPayResult(payResult);
    }
  }

  @override
  void onInit() {
    super.onInit();

    _aliPaySubs = AlipayKitPlatform.instance.payResp().listen(_listenAliPay);

    WechatService.to.addPayHandler(this);
  }

  @override
  void onClose() {
    _aliPaySubs.cancel();

    WechatService.to.removePayHandler(this);

    super.onClose();
  }

  void _listenAliPay(AlipayResp resp) {
    // final String content = 'pay: ${resp.resultStatus} - ${resp.result}';
    // _showTips('支付', content);

    PayResultCode resultCode = PayResultCode.unknown;
    switch (resp.resultStatus) {
      case 9000:
        resultCode = PayResultCode.success;
        break;
      case 8000:
        resultCode = PayResultCode.handling;
        break;
      case 4000:
        resultCode = PayResultCode.failed;
        break;
      case 5000:
        resultCode = PayResultCode.repeatedRequest;
        break;
      case 6001:
        resultCode = PayResultCode.cancelled;
        break;
    }

    PayResult payResult = PayResult(
      type: PayType.alipay,
      resultCode: resultCode,
      message: resp.memo,
      result: resp.result,
    );

    _notifyPayResult(payResult);
  }

  @override
  void onWechatPayResult(WechatPayResp payResult) {
    String? message = payResult.errorMsg;

    PayResultCode resultCode = PayResultCode.unknown;
    if (payResult.isSuccessful) {
      resultCode = PayResultCode.success;
    } else if (payResult.isCancelled) {
      resultCode = PayResultCode.cancelled;
      message ??= '用户取消支付';
    } else if (payResult.errorCode == WechatResp.kErrorCodeSentFail) {
      resultCode = PayResultCode.networkError;
      message ??= '发送失败';
    } else if (payResult.errorCode == WechatResp.kErrorCodeUnsupport) {
      resultCode = PayResultCode.notSupport;
      message ??= '微信不支持';
    }

    PayResult myPayResult = PayResult(
      type: PayType.wechat,
      resultCode: resultCode,
      message: message,
      result: payResult.returnKey,
    );

    _notifyPayResult(myPayResult);
  }

  Future<void> alipay(String orderInfo) {
    return AlipayKitPlatform.instance.pay(orderInfo: orderInfo);
  }

  Future<void> wechatPay(Map<String, Object?> param) {
    final String appId = (param["appid"] as String?) ?? '';
    final String partnerId = (param["partnerid"] as String?) ?? '';
    final String prepayId = (param["prepayid"] as String?) ?? '';
    final String package = (param["package"] as String?) ?? '';
    final String nonceStr = (param["noncestr"] as String?) ?? '';
    final int timeStamp = (param["timestamp"] as int?) ?? 0;
    final String sign = (param["sign"] as String?) ?? '';

    return WechatService.to.pay(
        appId: appId,
        partnerId: partnerId,
        prepayId: prepayId,
        package: package,
        nonceStr: nonceStr,
        timeStamp: "$timeStamp",
        sign: sign);
  }

  String? getAlipayOrderNo(String orderInfo) {
    try {
      final uri = Uri.parse(orderInfo);
      if (uri.queryParameters.containsKey('out_trade_no')) {
        return uri.queryParameters['out_trade_no'];
      }
    } catch (e) {
      print(e);
    }

    return null;
  }
}
