/// FileName: wechat_service
///
/// @Author: ygc
/// @Date: 2024/11/11 18:34
/// @Description:

import 'dart:async';
import 'dart:typed_data';

import 'package:dnurse/config/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/file.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:wechat_kit/wechat_kit.dart';
import 'package:wechat_kit_extension/wechat_kit_extension.dart';

import '../../../framework/utils/log.dart';

class WechatUserInfo {
  final String openId;
  String? unionId;
  final String nickName;
  final String accessToken;
  final String avatar;

  WechatUserInfo({
    required this.openId,
    this.unionId,
    required this.nickName,
    required this.accessToken,
    required this.avatar,
  });

  @override
  String toString() {
    return "WechatUserInfo(openId: $openId, unionId: $unionId, nickName: $nickName, accessToken: $accessToken, avatar: $avatar)";
  }
}

class WechatError {
  final String code;
  final String message;

  WechatError({required this.code, required this.message});

  @override
  String toString() {
    return "WechatError(code: $code, msg: $message)";
  }
}

abstract class WechatPayHandler {
  void onWechatPayResult(WechatPayResp payResult);
}

class WechatService extends GetxService {
  static WechatService get to => Get.find();
  final WechatKitPlatform _wechatKitPlatform = WechatKitPlatform.instance;
  late final StreamSubscription<WechatResp> _respSubs;
  WechatAuthResp? _wechatAuthResp;

  Function(WechatUserInfo)? _onLoginSuccess;
  Function(WechatError)? _onLoginError;
  VoidCallback? _onShareSuccess;

  final List<WechatPayHandler> _payHandlers = [];

  void addPayHandler(WechatPayHandler handler) {
    if (!_payHandlers.contains(handler)) {
      _payHandlers.add(handler);
    }
  }

  void removePayHandler(WechatPayHandler handler) {
    _payHandlers.remove(handler);
  }

  void _notifyPayResult(WechatPayResp payResult) {
    for (var handler in _payHandlers) {
      handler.onWechatPayResult(payResult);
    }
  }

  @override
  void onInit() {
    super.onInit();

    _respSubs = WechatKitPlatform.instance.respStream().listen(_listenResp);
    Log.i("wechatAppSecret: ${AppConstants.wechatAppId}");
    _wechatKitPlatform.registerApp(
        appId: AppConstants.wechatAppId,
        universalLink: AppConstants.wechatAppUniversalLink);
  }

  void _listenResp(WechatResp resp) {
    Log.i("_listenResp: ${resp.toJson()}");

    if (resp.isSuccessful) {
      if (resp is WechatAuthResp) {
        _handleAuthResp(resp);
      } else if (resp is WechatShareMsgResp) {
        _onShareSuccess?.call();
      } else if (resp is WechatPayResp) {
        _notifyPayResult(resp);
      }
    } else {
      _handelError(resp);
      if (resp is WechatPayResp) {
        _notifyPayResult(resp);
      }
    }
    // if (resp is WechatAuthResp) {
    //
    //   // final String content = 'auth: ${resp.errorCode} ${resp.errorMsg}';
    //   // _showTips('登录', content);
    // }
    // else if (resp is WechatShareMsgResp) {
    //   final String content = 'share: ${resp.errorCode} ${resp.errorMsg}';
    //   _showTips('分享', content);
    // } else if (resp is WechatPayResp) {
    //   final String content = 'pay: ${resp.errorCode} ${resp.errorMsg}';
    //   _showTips('支付', content);
    // } else if (resp is WechatLaunchMiniProgramResp) {
    //   final String content = 'mini program: ${resp.errorCode} ${resp.errorMsg}';
    //   _showTips('拉起小程序', content);
    // }
  }

  void login(
      {required Function(WechatUserInfo) onLoginSuccess,
      required Function(WechatError) onLoginError}) {
    _onLoginSuccess = onLoginSuccess;
    _onLoginError = onLoginError;

    _wechatKitPlatform.auth(
      scope: <String>[WechatScope.kSNSApiUserInfo],
      state: 'auth',
    );
  }

  void _handleAuthResp(WechatAuthResp resp) async {
    Log.i('resp :$resp');

    final WechatAccessTokenResp accessTokenResp =
        await WechatExtension.getAccessTokenUnionID(
      appId: AppConstants.wechatAppId,
      appSecret: AppConstants.wechatAppSecret,
      code: resp.code!,
    );

    Log.i('accessTokenResp :${accessTokenResp.toJson()}');

    if (accessTokenResp.isSuccessful) {
      final WechatUserInfoResp userInfoResp =
          await WechatExtension.getUserInfoUnionID(
        openId: accessTokenResp.openid!,
        accessToken: accessTokenResp.accessToken!,
      );

      Log.i('userInfoResp :${userInfoResp.toJson()}');
      if (userInfoResp.isSuccessful) {
        // _showTips('用户信息',
        //     '${userInfoResp.nickname} - ${userInfoResp.sex}');

        Log.i('${userInfoResp.nickname} - ${userInfoResp.sex}');

        WechatUserInfo wechatUserInfo = WechatUserInfo(
          openId: accessTokenResp.openid!,
          nickName: userInfoResp.nickname!,
          accessToken: accessTokenResp.accessToken!,
          avatar: userInfoResp.headimgurl!,
        );

        Log.i('wechatUserInfo : $wechatUserInfo');
        _onLoginSuccess?.call(wechatUserInfo);
        return;
      }
    }
    _onLoginError?.call(WechatError(code: "100000", message: "授权失败"));
  }

  void _handelError(WechatResp resp) {
    String message = "微信授权失败";
    if (resp.errorCode == WechatResp.kErrorCodeAuthDeny) {
      message = "取消授权";
    } else if (resp.errorCode == WechatResp.kErrorCodeSentFail) {
      message = "发送失败";
    } else if (resp.errorCode == WechatResp.kErrorCodeCommon) {
      message = resp.errorMsg ?? "未知错误";
    } else if (resp.errorCode == WechatResp.kErrorCodeUserCancel) {
      message = "用户取消";
    } else if (resp.errorCode == WechatResp.kErrorCodeUnsupport) {
      message = "微信不支持";
    }

    _onLoginError
        ?.call(WechatError(code: resp.errorCode.toString(), message: message));
  }

  /// 分享图片到微信
  void shareImage({
    required String url,
    required Function() onSuccess,
    required Function(WechatError) onError,
    required int scene,
  }) async {
    _onShareSuccess = onSuccess;
    _onLoginError = onError;

    try {
      Uri fileUri;
      if (!url.startsWith("http")) {
        fileUri = Uri.file(url);
      } else {
        final File file = await DefaultCacheManager().getSingleFile(url);
        fileUri = Uri.file(file.path);
      }

      await WechatKitPlatform.instance.shareImage(
        scene: scene,
        imageUri: fileUri,
      );
    } catch (e) {
      Log.e("分享图片到微信收藏出错: $e");
      onError(WechatError(code: "-1", message: "分享失败: $e"));
    }
  }

  /// 分享网页到微信
  Future<void> shareWebpage({
    required String url,
    required String title,
    String? description,
    Uint8List? thumbnailUrl,
    required Function() onSuccess,
    required Function(WechatError) onError,
    required int scene,
  }) async {
    _onShareSuccess = onSuccess;
    _onLoginError = onError;
    try {
      await WechatKitPlatform.instance.shareWebpage(
        scene: scene,
        title: title,
        description: description ?? '',
        webpageUrl: url,
        thumbData: thumbnailUrl,
      );
    } catch (e) {
      Log.e("分享网页到微信出错: $e");
      onError(WechatError(code: "-1", message: "分享失败: $e"));
    }
  }

  Future<void> pay({
    required String appId,
    required String partnerId,
    required String prepayId,
    required String package,
    required String nonceStr,
    required String timeStamp,
    required String sign,
  }) async {
    return WechatKitPlatform.instance.pay(
      appId: appId,
      partnerId: partnerId,
      prepayId: prepayId,
      package: package,
      nonceStr: nonceStr,
      timeStamp: timeStamp,
      sign: sign,
    );
  }

  void launchMiniProgram({
    required String userName,
    String? path,
    int type = WechatMiniProgram.kRelease,
  }) {
    WechatKitPlatform.instance.launchMiniProgram(
      userName: userName,
      path: path,
      type: type,
    );
  }

  Future<void> openCustomerServiceChat({
    required String corpId,
    required String url,
  }) {
    return WechatKitPlatform.instance.openCustomerServiceChat(
      corpId: corpId,
      url: url,
    );
  }
}
