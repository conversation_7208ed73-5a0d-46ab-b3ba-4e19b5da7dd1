/// FileName: base_service
///
/// @Author: ygc
/// @Date: 2024/8/6 17:11
/// @Description:
import 'dart:async';

import 'package:dnurse/framework/event/app_boot_finished.dart';
import 'package:dnurse/framework/event/main_has_show.dart';
import 'package:get/get.dart';

import '../../../bootstrap/app.dart';
import '../../../framework/service/event_service.dart';
import '../../user/event/login_changed.dart';

class BaseService extends GetxService {
  BaseService({
    this.needLoginChanged = true,
    this.needAppBootFinished = true,
    this.needMainHasShow = true,
  });

  final bool needLoginChanged;
  final bool needAppBootFinished;
  final bool needMainHasShow;
  late StreamSubscription loginChangedSubscription;
  late StreamSubscription appBootFinishedSubscription;
  late StreamSubscription needMainHasShowSubscription;

  @override
  void onInit() {
    super.onInit();

    initSubscription();
  }

  @override
  void onClose() {
    cancelSubscription();

    super.onClose();
  }

  void initSubscription() {
    if (needLoginChanged) {
      loginChangedSubscription =
          EventService.to.bus.on<LoginChanged>().listen(onLoginChanged);
    }
    if (needAppBootFinished) {
      appBootFinishedSubscription =
          EventService.to.bus.on<AppBootFinished>().listen(onAppBootFinished);
    }
    if (needMainHasShow) {
      needMainHasShowSubscription =
          EventService.to.bus.on<MainHasShow>().listen(onMainHasShow);
    }
  }

  void cancelSubscription() {
    if (needLoginChanged) {
      loginChangedSubscription.cancel();
    }

    if (needAppBootFinished) {
      appBootFinishedSubscription.cancel();
    }

    if (needMainHasShow) {
      needMainHasShowSubscription.cancel();
    }
  }

  Future<void> onLoginChanged(LoginChanged event) async {}

  Future<void> onAppBootFinished(AppBootFinished event) async {}

  Future<void> onMainHasShow(MainHasShow event) async {}

  String getStorageKey(String prefix, {String? userSn}) {
    return "${prefix}_${userSn ?? AppContext.to.auth.value.sn}";
  }

  void register() {}
}
