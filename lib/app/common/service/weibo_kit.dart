import 'dart:async';

import 'package:dnurse/config/constants.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:get/get.dart';
import 'package:wechat_kit/wechat_kit.dart';
import 'package:weibo_kit/weibo_kit.dart';
import 'package:weibo_kit/weibo_kit_platform_interface.dart';

class WeiboService extends GetxService {
  static WeiboService get to => Get.find();

  late final StreamSubscription<BaseResp> _streamSubscription;

  AuthResp? _authResp;

  @override
  void onInit() {
    super.onInit();

    _streamSubscription = Weibo.instance.respStream().listen(_listenResp);

    // Weibo.instance.registerApp(
    //   appKey:
    // );
  }

  void _listenResp(BaseResp req) {
    Log.i("WeiboService: $req");

    if (req is AuthResp) {
      _authResp = req;
      final String content = 'auth: ${req.errorCode}';

      // 登录代码 todo:
    } else if (req is ShareMsgResp) {
      final String content = 'share: ${req.errorCode}';
      Log.i(content);
      // 分享代码 todo:
    }
  }
}
