/// FileName: change_event
///
/// @Author: ygc
/// @Date: 2025/7/10 10:16
/// @Description:

enum Operation {
  add,
  update,
  deleted,
}

class ChangeEvent<T> {
  final Operation operation;
  final T data;
  final T? oldData;

  ChangeEvent.add({required this.data})
      : operation = Operation.add,
        oldData = null;

  ChangeEvent.update({required this.data, required this.oldData})
      : operation = Operation.update;

  ChangeEvent.deleted({required this.data})
      : operation = Operation.deleted,
        oldData = null;

  @override
  String toString() {
    return 'ChangeEvent: {data: $data, operation: $operation}';
  }
}
