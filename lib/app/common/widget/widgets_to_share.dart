import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:dnurse/framework/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

typedef OnImageReady = void Function(WidgetsToImageController controller);

class WidgetsToShare extends StatefulWidget {
  const WidgetsToShare({
    super.key,
    required this.child,
    this.onImageReady,
    this.width = 300,
    this.height = 510,
  });

  final Widget child;
  final OnImageReady? onImageReady;
  final double width;
  final double height;

  /// 静态方法，用于异步生成图片并返回文件路径
  static Future<String> captureAndSave({
    required BuildContext context,
    required Widget child,
    required String filePath,
    double width = 306,
    double height = 510,
    int retryCount = 3,
    Duration waitDuration = const Duration(milliseconds: 300),
  }) async {
    // 创建一个 Completer 来等待图片生成完成
    final completer = Completer<String>();

    // 创建一个控制器
    final controller = WidgetsToImageController();

    // 创建一个临时的 widget
    final widget = RepaintBoundary(
      key: controller.containerKey,
      child: Material(
        color: Colors.transparent,
        child: SizedBox(
          width: width.w,
          height: height.h,
          child: child,
        ),
      ),
    );

    // 创建一个临时的 Overlay 来渲染 widget
    final entry = OverlayEntry(
      builder: (ctx) => Positioned(
        left: -10000, // 放在屏幕外
        child: widget,
      ),
    );

    // 添加到 Overlay
    Overlay.of(context).insert(entry);

    // 尝试捕获图像的函数
    Future<void> attemptCapture(int remainingRetries) async {
      try {
        // 等待足够长的时间确保渲染完成
        await Future.delayed(waitDuration);

        // 捕获图像
        final bytes = await controller.capture();
        if (bytes != null && bytes.isNotEmpty) {
          // 保存到文件
          final file = await File(filePath).create(recursive: true);
          await file.writeAsBytes(bytes);

          // 检查文件大小确保图片正确生成
          final fileSize = await file.length();
          if (fileSize > 100) {
            // 确保文件大小合理
            completer.complete(filePath);
          } else if (remainingRetries > 0) {
            // 文件太小，可能没有完全渲染，重试
            Log.i('图片文件太小 ($fileSize bytes)，重试捕获...');
            await attemptCapture(remainingRetries - 1);
          } else {
            completer.completeError('生成的图片文件太小');
          }
        } else if (remainingRetries > 0) {
          // 捕获失败，重试
          Log.i('捕获图像失败，重试...');
          await attemptCapture(remainingRetries - 1);
        } else {
          completer.completeError('无法捕获图片，已重试多次');
        }
      } catch (e) {
        if (remainingRetries > 0) {
          // 发生错误，重试
          Log.e('捕获图像时发生错误: $e，重试...');
          await attemptCapture(remainingRetries - 1);
        } else {
          completer.completeError('生成图片失败: $e');
        }
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        await attemptCapture(retryCount);
      } finally {
        // 确保最终移除临时 widget
        entry.remove();
      }
    });

    return completer.future;
  }

  @override
  State<WidgetsToShare> createState() => _WidgetsToShareState();
}

class _WidgetsToShareState extends State<WidgetsToShare> {
  final WidgetsToImageController _controller = WidgetsToImageController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onImageReady?.call(_controller);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: _controller.containerKey,
      child: SizedBox(
        width: widget.width.w,
        height: widget.height.h,
        child: widget.child,
      ),
    );
  }
}
