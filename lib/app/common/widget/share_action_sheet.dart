import 'dart:io';
import 'dart:typed_data';

import 'package:bruno/bruno.dart';
import 'package:dnurse/app/common/service/tencent_service.dart';
import 'package:dnurse/app/common/service/wechat_service.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_kit/tencent_kit.dart';
import 'package:wechat_kit/wechat_kit.dart';

/// 分享渠道定义，使用位运算标志
class ShareChannel {
  static const int weiXin = 1 << 0; // 微信
  static const int friend = 1 << 1; // 朋友圈
  static const int qq = 1 << 2; // QQ
  static const int qZone = 1 << 3; // QQ空间
  static const int weibo = 1 << 4; // 微博
  static const int all = 0xFF; // 所有渠道
}

/// 分享策略接口
abstract class ShareStrategy {
  int get channel;

  void share(
    BuildContext context,
    String? title,
    String? content,
    String? url,
    Uint8List? thumbnailUrl,
    String? imageUrl,
    ShareResultCallback? onSuccess,
    ShareResultCallback? onFail,
    ShareType shareType,
  );
}

// 定义分享的类型  图片 | 网页 | 链接
enum ShareType {
  image,
  webpage,
  link,
}

/// 微信分享策略
class WechatShareStrategy implements ShareStrategy {
  final int scene;

  WechatShareStrategy(this.scene);

  @override
  int get channel =>
      scene == WechatScene.kSession ? ShareChannel.weiXin : ShareChannel.friend;

  @override
  void share(
    BuildContext context,
    String? title,
    String? content,
    String? url,
    Uint8List? thumbnailUrl,
    String? imageUrl,
    ShareResultCallback? onSuccess,
    ShareResultCallback? onFail,
    ShareType shareType,
  ) {
    if (shareType == ShareType.webpage) {
      WechatService.to.shareWebpage(
        scene: scene,
        url: url ?? '',
        title: title ?? '',
        description: content ?? '',
        thumbnailUrl: thumbnailUrl,
        onSuccess: () {
          DNUToast.show("分享成功", context);
          if (onSuccess != null) {
            onSuccess(channel);
          }
        },
        onError: (error) {
          DNUToast.show(error.message, context);
          if (onFail != null) {
            onFail(channel);
          }
        },
      );
    } else if (shareType == ShareType.image) {
      WechatService.to.shareImage(
        scene: scene,
        url: url ?? '',
        onSuccess: () {
          DNUToast.show("分享成功", context);
          if (onSuccess != null) {
            onSuccess(channel);
          }
        },
        onError: (error) {
          print('分享失败: ${error.message}');

          DNUToast.show(error.message, context);
          if (onFail != null) {
            onFail(channel);
          }
        },
      );
    }
  }
}

/// QQ分享策略
class QQShareStrategy implements ShareStrategy {
  final int scene;

  QQShareStrategy(this.scene);

  @override
  int get channel =>
      scene == TencentScene.kScene_QQ ? ShareChannel.qq : ShareChannel.qZone;

  @override
  void share(
    BuildContext context,
    String? title,
    String? content,
    String? url,
    Uint8List? thumbnailUrl,
    String? imageUrl,
    ShareResultCallback? onSuccess,
    ShareResultCallback? onFail,
    ShareType shareType,
  ) {
    TencentService.to.shareShareImageToQzone(
      targetUrl: url ?? '',
      title: title ?? '',
      summary: content ?? '',
      url: imageUrl ?? '',
      scene: scene,
      onSuccess: () {
        DNUToast.show("分享成功", context);
        if (onSuccess != null) {
          onSuccess(channel);
        }
      },
      onError: (error) {
        DNUToast.show(error.message, context);
        if (onFail != null) {
          onFail(channel);
        }
      },
    );
  }
}

/// 微博分享策略
class WeiboShareStrategy implements ShareStrategy {
  @override
  int get channel => ShareChannel.weibo;

  @override
  void share(
    BuildContext context,
    String? title,
    String? content,
    String? url,
    Uint8List? thumbnailUrl,
    String? imageUrl,
    ShareResultCallback? onSuccess,
    ShareResultCallback? onFail,
    ShareType shareType,
  ) {
    // 微博分享功能暂未实现
    DNUToast.show("微博分享功能暂未实现", context);
    if (onFail != null) {
      onFail(channel);
    }
  }
}

/// 分享策略工厂
class ShareStrategyFactory {
  static final Map<int, ShareStrategy> _strategies = {
    BrnShareItemConstants.shareWeiXin:
        WechatShareStrategy(WechatScene.kSession),
    BrnShareItemConstants.shareFriend:
        WechatShareStrategy(WechatScene.kTimeline),
    BrnShareItemConstants.shareQQ: QQShareStrategy(TencentScene.kScene_QQ),
    BrnShareItemConstants.shareQZone:
        QQShareStrategy(TencentScene.kScene_QZone),
    BrnShareItemConstants.shareWeiBo: WeiboShareStrategy(),
  };

  static ShareStrategy? getStrategy(int shareType) {
    return _strategies[shareType];
  }
}

/// 分享结果回调函数类型
typedef ShareResultCallback = void Function(int channel);

/// 分享表单工具类
class ShareSheet {
  /// 显示分享操作表
  ///
  /// [context] BuildContext对象
  /// [shareChannel] 要显示的分享渠道，默认为全部
  /// [title] 分享标题
  /// [content] 分享内容
  /// [url] 分享链接
  /// [imageUrl] 分享图片URL
  /// [onSuccess] 分享成功回调
  /// [onFail] 分享失败回调
  /// [onCancel] 分享取消回调
  static Future<void> show(
    BuildContext context, {
    required ShareType shareType,
    required int shareChannel,
    String? title,
    String? content,
    String? url,
    Uint8List? thumbnailUrl,
    String? imageUrl,
    Function(int)? onSuccess,
    Function(int)? onFail,
    Function(int)? onCancel,
    String? cancelTitle,
    VoidCallback? onClose,
  }) async {
    final List<BrnShareItem> shareItems = [];

    // 检查每个渠道是否需要显示
    if (shareChannel & ShareChannel.weiXin != 0) {
      shareItems.add(BrnShareItem(
        BrnShareItemConstants.shareWeiXin,
        canClick: true,
      ));
    }

    if (shareChannel & ShareChannel.friend != 0) {
      shareItems.add(BrnShareItem(
        BrnShareItemConstants.shareFriend,
        canClick: true,
      ));
    }

    if (shareChannel & ShareChannel.qq != 0) {
      shareItems.add(BrnShareItem(
        BrnShareItemConstants.shareQQ,
        canClick: true,
      ));
    }

    if (shareChannel & ShareChannel.qZone != 0) {
      shareItems.add(BrnShareItem(
        BrnShareItemConstants.shareQZone,
        canClick: true,
      ));
    }

    if (shareChannel & ShareChannel.weibo != 0) {
      shareItems.add(BrnShareItem(
        BrnShareItemConstants.shareWeiBo,
        canClick: true,
      ));
    }

    // 创建分享操作表
    final actionSheet = BrnShareActionSheet(
      firstShareChannels: shareItems,
      cancelTitle: cancelTitle,
      clickCallBack: (int section, int index, BrnShareItem shareItem) {
        print('##########----shareItem---------: $shareItem');
        // 使用策略模式处理分享
        final strategy = ShareStrategyFactory.getStrategy(shareItem.shareType);
        if (strategy != null) {
          strategy.share(context, title, content, url, thumbnailUrl, imageUrl,
              onSuccess, onFail, shareType);
        }
      },
    );

    // 显示分享操作表并监听关闭事件
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Column(
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.only(bottom: 40.w, top: 74.w),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(
                      20.r,
                    ),
                  ),
                  child: Image.file(
                    File(url ?? ''),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            actionSheet,
          ],
        );
      },
    ).then((_) {
      // 底部弹窗关闭后执行回调
      if (onClose != null) {
        onClose();
      }

      if (url?.isNotEmpty ?? false) {
        File imageFile = File(url!);
        if (imageFile.existsSync()) {
          imageFile.deleteSync();
        }
      }
      // 关闭加载指示器
      DNULoading.dismiss(context);
    });
  }
}
