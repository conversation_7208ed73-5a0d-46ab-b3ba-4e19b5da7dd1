/// FileName: first_popup_dialog
///
/// @Author: ygc
/// @Date: 2025/6/23 15:24
/// @Description:
import 'dart:async';

import 'package:dnurse/app/common/link/deep_link.dart';
import 'package:dnurse/framework/utils/network_utils.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../router/router.dart';
import '../../../router/routes.dart';
import '../api/dto/notice_item_dto/notice_item_dto.dart';
import '../service/banner_service.dart';

class FirstPopupDialogDialog {
  OverlayEntry? _overlayEntry;

  factory FirstPopupDialogDialog() => _instance;

  FirstPopupDialogDialog._internal();

  bool _show = false;
  static final FirstPopupDialogDialog _instance =
      FirstPopupDialogDialog._internal();

  Future<T?> showPopup<T>(
    BuildContext context,
    NoticeItemDTO noticeItem,
  ) async {
    if (_show) {
      return null;
    }

    _show = true;

    final Completer<T?> popCompleter = Completer<T?>();

    void onClose(T? result) {
      BannerService.to.saveFirstPopupShowInfo(noticeItem);

      _overlayEntry?.remove();
      _overlayEntry = null;
      _show = false;
      popCompleter.complete(null);
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: () {
          // 点击外部区域时关闭菜单
          onClose(null);
        },
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.6),
              ),
            ),
            Positioned.fill(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 320.w,
                    child: InkWell(
                      onTap: () {
                        onClose(null);

                        if (noticeItem.noticeLink.isNotEmpty) {
                          DeepLinkResult deepLinkResult =
                              DeepLink.handle(noticeItem.noticeLink);
                          if (deepLinkResult.handled) {
                            return;
                          }
                        }

                        NetworkUtils.hasNetwork().then((value) {
                          if (value) {
                            AppRouter.push(Routes.browser,
                                arguments: noticeItem.noticeLink);
                          } else {
                            // AppRouter.pushNamed(
                            //   AppRouter.webViewPage,
                            //   arguments: {
                            //     'url}
                            // )
                          }
                        });
                      },
                      child: AspectRatio(
                        aspectRatio: 640 / 840,
                        child: DNUImage.network(
                          noticeItem.noticePic,
                          cacheManager: FirstPopupImageCacheManager.instance,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 35.w),
                  InkWell(
                    onTap: () {
                      onClose(null);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        DNUIconFont.fenxiangshanchu,
                        size: 36.w,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );

    if (context.mounted) {
      Overlay.of(context).insert(_overlayEntry!);
    }

    return popCompleter.future;
  }

  static Future<T?> show<T>({
    required NoticeItemDTO noticeItem,
    required BuildContext context,
  }) {
    return _instance.showPopup(
      context,
      noticeItem,
    );
  }
}
