/// FileName: identity
///
/// @Author: ygc
/// @Date: 2024/7/8 18:31
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _typeNo = 1;
const int _typeLow = 2;
const int _typeNormal = 3;
const int _typeHigh = 4;

class ActivityType extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const ActivityType({
    required this.id,
    required this.buildName,
  });

  static ActivityType no = ActivityType(
      id: _typeNo,
      buildName: (context) {
        return S.of(context).activityTypeNo;
      });

  static ActivityType low = ActivityType(
      id: _typeLow,
      buildName: (context) {
        return S.of(context).activityTypeLow;
      });
  static ActivityType normal = ActivityType(
      id: _typeNormal,
      buildName: (context) {
        return S.of(context).activityTypeNormal;
      });
  static ActivityType high = ActivityType(
      id: _typeHigh,
      buildName: (context) {
        return S.of(context).activityTypeHigh;
      });

  static ActivityType? fromId(int id) {
    switch (id) {
      case _typeNo:
        return no;
      case _typeLow:
        return low;
      case _typeNormal:
        return normal;
      case _typeHigh:
        return high;
      default:
        return null;
    }
  }

  static List<ActivityType> getOptions() {
    return [
      ActivityType.no,
      ActivityType.low,
      ActivityType.normal,
      ActivityType.high,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "ActivityType{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
