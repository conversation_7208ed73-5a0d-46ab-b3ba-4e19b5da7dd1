/// FileName: identity
///
/// @Author: ygc
/// @Date: 2024/7/8 18:31
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

class Identity extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const Identity({
    required this.id,
    required this.buildName,
  });

  static Identity patient = Identity(
      id: 1,
      buildName: (context) {
        return S.of(context).identityPatient;
      });

  static Identity family = Identity(
      id: 0,
      buildName: (context) {
        return S.of(context).identityFamily;
      });

  static Identity? fromId(int id) {
    switch (id) {
      case 1:
        return patient;
      case 0:
        return family;
      default:
        return null;
    }
  }

  static List<Identity> getOptions() {
    return [
      Identity.patient,
      Identity.family,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "Identity{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
