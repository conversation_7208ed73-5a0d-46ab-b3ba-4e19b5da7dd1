/// FileName: diabetes_treatment_method
///
/// @Author: ygc
/// @Date: 2024/7/10 09:52
/// @Description:

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _treatMethodNone = 10000;
const int _treatMethodInsulin = 1 << 6;
const int _treatMethodDrug = 1 << 5;
// const int _treatMethodNone = 1 << 2;
const int _treatMethodFoodControl = 1 << 2;
const int _treatMethodSportControl = 1 << 3;

const int _treatMethodInsulinOld = 1;
const int _treatMethodInsulinLikeOld = 2;
const int _treatMethodDrugOld = 3;

class DiabetesTreatmentMethod extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const DiabetesTreatmentMethod({
    required this.id,
    required this.buildName,
  });

  static DiabetesTreatmentMethod insulin = DiabetesTreatmentMethod(
      id: _treatMethodInsulin,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodInsulin;
      });

  static DiabetesTreatmentMethod insulinOld = DiabetesTreatmentMethod(
      id: _treatMethodInsulinOld,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodInsulin;
      });

  static DiabetesTreatmentMethod insulinLikeOld = DiabetesTreatmentMethod(
      id: _treatMethodInsulinLikeOld,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodInsulinLike;
      });

  static DiabetesTreatmentMethod drugOld = DiabetesTreatmentMethod(
      id: _treatMethodDrugOld,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodDrug;
      });

  static DiabetesTreatmentMethod drug = DiabetesTreatmentMethod(
      id: _treatMethodDrug,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodDrug;
      });
  static DiabetesTreatmentMethod none = DiabetesTreatmentMethod(
      id: _treatMethodNone,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodNone;
      });
  static DiabetesTreatmentMethod foodControl = DiabetesTreatmentMethod(
      id: _treatMethodFoodControl,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodFoodControl;
      });

  static DiabetesTreatmentMethod sportControl = DiabetesTreatmentMethod(
      id: _treatMethodSportControl,
      buildName: (context) {
        return S.of(context).diabetesTreatMethodSportControl;
      });

  static DiabetesTreatmentMethod? fromId(int id) {
    switch (id) {
      case _treatMethodInsulin:
        return insulin;
      case _treatMethodDrug:
        return drug;
      case _treatMethodNone:
        return none;
      case _treatMethodFoodControl:
        return foodControl;
      case _treatMethodSportControl:
        return sportControl;

      default:
        return null;
    }
  }

  static List<DiabetesTreatmentMethod> fromValue(int value) {
    List<DiabetesTreatmentMethod> result = [];
    if (value >= _treatMethodInsulin) {
      return result;
    }
    if (value < 4) {
      if (value == insulinOld.id) {
        result.add(insulinOld);
      } else if (value == insulinLikeOld.id) {
        result.add(insulinLikeOld);
      } else if (value == drugOld.id) {
        result.add(drugOld);
      }

      return result;
    }

    for (var item in getOptions()) {
      if ((value & item.id) == item.id) {
        result.add(item);
      }
    }

    return result;
  }

  static int toValue(List<DiabetesTreatmentMethod> list) {
    int result = 0;
    for (var item in list) {
      result |= item.id;
    }

    return result;
  }

  static List<DiabetesTreatmentMethod> getOptions() {
    return [
      none,
      foodControl,
      sportControl,
      drug,
      insulin,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "DiabetesTreatmentMethod{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
