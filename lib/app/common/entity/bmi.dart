/// FileName: bmi
///
/// @Author: ygc
/// @Date: 2022/8/2 18:11
/// @Description:

enum BmiCategory {
  /// 偏瘦
  thin,

  /// 正常
  normal,

  /// 超重
  overWeight,

  /// 肥胖
  obesity,

  /// 重度肥胖
  severeObesity
}

class Bmi {
  static double calculate({required double weight, required double height}) {
    if (height == 0) {
      return 0;
    }

    height = height / 100;

    return weight / (height * height);
  }

  static BmiCategory category(double bmi) {
    if (bmi < 18.6) {
      return BmiCategory.thin;
    } else if (bmi < 24.0) {
      return BmiCategory.normal;
    } else if (bmi < 28.0) {
      return BmiCategory.overWeight;
    } else if (bmi < 32.5) {
      return BmiCategory.obesity;
    } else {
      return BmiCategory.severeObesity;
    }
  }
}
