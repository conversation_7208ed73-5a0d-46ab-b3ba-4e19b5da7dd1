/// FileName: gender
///
/// @Author: ygc
/// @Date: 2024/7/8 18:15
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _male = 1;
const int _female = 2;

class Gender extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const Gender({
    required this.id,
    required this.buildName,
  });

  static Gender male = Gender(
      id: _male,
      buildName: (context) {
        return S.of(context).genderMale;
      });

  static Gender female = Gender(
      id: _female,
      buildName: (context) {
        return S.of(context).genderFemale;
      });

  static Gender? fromId(int id) {
    switch (id) {
      case _male:
        return male;
      case _female:
        return female;
      default:
        return null;
    }
  }

  static List<Gender> getOptions() {
    return [
      Gender.male,
      Gender.female,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "Gender{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
