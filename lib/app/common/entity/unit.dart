/// FileName: unit
///
/// @Author: ygc
/// @Date: 2025/7/1 17:35
/// @Description:
class Unit {
  final int id;
  final String name;

  const Unit(this.id, this.name);

  static const List<Unit> values = [
    defaultUnit,
    gram,
    bowl,
    cup,
    singleton,
    piece,
    stick,
    spoon,
    milliliter,
    unitId1001,
    unitId1002,
    unitId1003,
    unitId1004,
    unitId1005,
    unitId1006,
    unitId1007,
    unitId1008,
    unitId1009,
    unitId1010,
    unitId1011,
    unitId1012,
    unitId1013,
    unitId1014,
    unitId1015,
    unitId1016,
    unitId1017,
    unitId1018,
    unitId1019,
    unitId1020,
    unitId1021,
    unitId1022,
    unitId1023,
    unitId1024,
    unitId1025,
    unitId1026,
    unitId1027,
    unitId1028,
    unitId1029,
    unitId1030,
    unitId1031,
    unitId1032,
    unitId1033,
    unitId1034,
    unitId1035,
    unitId1036,
    unitId1037,
    unitId1038,
    unitId1039,
    unitId1040,
    unitId1041,
    unitId1042,
    unitId1043,
    unitId1044,
    unitId1045,
    unitId1046,
    unitId1047,
    unitId1048,
    unitId1049,
    unitId1050,
    unitId1051,
    unitId1052,
    unitId1053,
    unitId1054,
    unitId1055,
    unitId1056,
    unitId1057,
    unitId1058,
    unitId1059,
    unitId1060,
    unitId1061,
    unitId1062,
    unitId1063,
    unitId1064,
    unitId1065,
    unitId1066,
    unitId1067,
    unitId1068,
    unitId1069,
    unitId1070,
    unitId1071,
    unitId1072,
    unitId1073,
    unitId1074,
    unitId1075,
    unitId1076,
    unitId1077,
    unitId1078,
    unitId1079,
    unitId1080,
    unitId1081,
    unitId1082,
    unitId1083,
    unitId1084,
    unitId1085,
    unitId1086,
    unitId1087,
    unitId1088,
    unitId1089,
    unitId1090,
    unitId1091,
    unitId1092,
    unitId1093,
    unitId1094,
    unitId1095,
    unitId1096,
    unitId1097,
    unitId1098,
    unitId1099,
    unitId1100,
    unitId1101,
    unitId1102,
    unitId1103,
    unitId1104,
    unitId1105,
    unitId1106,
    unitId1107,
    unitId1108,
    unitId1109,
    unitId1110,
    unitId1111,
    unitId1112,
    unitId1113,
    unitId1114,
    unitId1115,
    unitId1116,
    unitId1117,
    unitId1118,
    unitId1119,
    unitId1120,
    unitId1121,
    unitId1122,
    unitId1123,
    unitId1124,
    unitId1125,
    unitId1126,
    unitId1127,
    unitId1128,
    unitId1129,
    unitId1130,
    unitId1131,
    unitId1132,
    unitId1133,
    unitId1134,
    unitId1135,
    unitId1136,
    unitId1137,
    unitId1138,
    unitId1139,
    unitId1140,
    unitId1141,
    unitId1142,
    unitId1143,
  ];

  static const Unit defaultUnit = Unit(0, "克");
  static const Unit gram = Unit(1, "克");
  static const Unit bowl = Unit(2, "碗");
  static const Unit cup = Unit(3, "杯");
  static const Unit singleton = Unit(4, "个");
  static const Unit piece = Unit(5, "块");
  static const Unit stick = Unit(6, "根");
  static const Unit spoon = Unit(7, "勺");
  static const Unit milliliter = Unit(8, "毫升");

  // 自动从这里开始生成 UnitID1001 - UnitID1143
  static const Unit unitId1001 = Unit(1001, "份");
  static const Unit unitId1002 = Unit(1002, "包");
  static const Unit unitId1003 = Unit(1003, "袋");
  static const Unit unitId1004 = Unit(1004, "盒");
  static const Unit unitId1005 = Unit(1005, "瓶");
  static const Unit unitId1006 = Unit(1006, "罐");
  static const Unit unitId1007 = Unit(1007, "盘");
  static const Unit unitId1008 = Unit(1008, "片");
  static const Unit unitId1009 = Unit(1009, "粒");
  static const Unit unitId1010 = Unit(1010, "支");
  static const Unit unitId1011 = Unit(1011, "条");
  static const Unit unitId1012 = Unit(1012, "只");
  static const Unit unitId1013 = Unit(1013, "碗（小）");
  static const Unit unitId1014 = Unit(1014, "颗");
  static const Unit unitId1015 = Unit(1015, "小包");
  static const Unit unitId1016 = Unit(1016, "个（小）");
  static const Unit unitId1017 = Unit(1017, "桶");
  static const Unit unitId1018 = Unit(1018, "袋 ");
  static const Unit unitId1019 = Unit(1019, "个（大）");
  static const Unit unitId1020 = Unit(1020, "枚");
  static const Unit unitId1021 = Unit(1021, "碟");
  static const Unit unitId1022 = Unit(1022, "盘（小）");
  static const Unit unitId1023 = Unit(1023, "个(中)");
  static const Unit unitId1024 = Unit(1024, "大包");
  static const Unit unitId1025 = Unit(1025, "小袋");
  static const Unit unitId1026 = Unit(1026, "把");
  static const Unit unitId1027 = Unit(1027, "大罐");
  static const Unit unitId1028 = Unit(1028, "个（中）");
  static const Unit unitId1029 = Unit(1029, "大袋");
  static const Unit unitId1030 = Unit(1030, "碗（小碗）");
  static const Unit unitId1031 = Unit(1031, "串");
  static const Unit unitId1032 = Unit(1032, "箱");
  static const Unit unitId1033 = Unit(1033, "小罐");
  static const Unit unitId1034 = Unit(1034, "张");
  static const Unit unitId1035 = Unit(1035, "小碗");
  static const Unit unitId1036 = Unit(1036, "根（大）");
  static const Unit unitId1037 = Unit(1037, "大碗");
  static const Unit unitId1038 = Unit(1038, "小瓶");
  static const Unit unitId1039 = Unit(1039, "大瓶");
  static const Unit unitId1040 = Unit(1040, "根（小）");
  static const Unit unitId1041 = Unit(1041, "份（大）");
  static const Unit unitId1042 = Unit(1042, "小个");
  static const Unit unitId1043 = Unit(1043, "小盒");
  static const Unit unitId1044 = Unit(1044, "个 ");
  static const Unit unitId1045 = Unit(1045, "碗（中）");
  static const Unit unitId1046 = Unit(1046, "卷");
  static const Unit unitId1047 = Unit(1047, "两");
  static const Unit unitId1048 = Unit(1048, "大盒");
  static const Unit unitId1049 = Unit(1049, "根（中）");
  static const Unit unitId1050 = Unit(1050, "快餐盒");
  static const Unit unitId1051 = Unit(1051, "份（小）");
  static const Unit unitId1052 = Unit(1052, "包1");
  static const Unit unitId1053 = Unit(1053, "包2");
  static const Unit unitId1054 = Unit(1054, "瓶（中）");
  static const Unit unitId1055 = Unit(1055, "棵");
  static const Unit unitId1056 = Unit(1056, "根 ");
  static const Unit unitId1057 = Unit(1057, "捆");
  static const Unit unitId1058 = Unit(1058, "大个");
  static const Unit unitId1059 = Unit(1059, "小盘");
  static const Unit unitId1060 = Unit(1060, "条（中）");
  static const Unit unitId1061 = Unit(1061, "片（圆）");
  static const Unit unitId1062 = Unit(1062, "小碟");
  static const Unit unitId1063 = Unit(1063, "大块");
  static const Unit unitId1064 = Unit(1064, "只(中)");
  static const Unit unitId1065 = Unit(1065, "标准杯");
  static const Unit unitId1066 = Unit(1066, "颗（大）");
  static const Unit unitId1067 = Unit(1067, "大杯");
  static const Unit unitId1068 = Unit(1068, "杯（中）");
  static const Unit unitId1069 = Unit(1069, "块（小）");
  static const Unit unitId1070 = Unit(1070, "包3");
  static const Unit unitId1071 = Unit(1071, "片（小）");
  static const Unit unitId1072 = Unit(1072, "节（小）");
  static const Unit unitId1073 = Unit(1073, "块(方)");
  static const Unit unitId1074 = Unit(1074, "瓣");
  static const Unit unitId1075 = Unit(1075, "蓝白瓶");
  static const Unit unitId1076 = Unit(1076, "半片");
  static const Unit unitId1077 = Unit(1077, "只（中）");
  static const Unit unitId1078 = Unit(1078, "超大杯");
  static const Unit unitId1079 = Unit(1079, "勺(大黄豆)");
  static const Unit unitId1080 = Unit(1080, "节（中）");
  static const Unit unitId1081 = Unit(1081, "个（球）");
  static const Unit unitId1082 = Unit(1082, "朵");
  static const Unit unitId1083 = Unit(1083, "泡");
  static const Unit unitId1084 = Unit(1084, "张（小）");
  static const Unit unitId1085 = Unit(1085, "撮");
  static const Unit unitId1086 = Unit(1086, "中袋");
  static const Unit unitId1087 = Unit(1087, "小锅");
  static const Unit unitId1088 = Unit(1088, "小勺");
  static const Unit unitId1089 = Unit(1089, "板");
  static const Unit unitId1090 = Unit(1090, "蒸屉");
  static const Unit unitId1091 = Unit(1091, "瓷勺(满)");
  static const Unit unitId1092 = Unit(1092, "杯（塑料）");
  static const Unit unitId1093 = Unit(1093, "瓣（带皮）");
  static const Unit unitId1094 = Unit(1094, "袋装");
  static const Unit unitId1095 = Unit(1095, "大片");
  static const Unit unitId1096 = Unit(1096, "碗(乐扣碗)");
  static const Unit unitId1097 = Unit(1097, "碗(小乐扣)");
  static const Unit unitId1098 = Unit(1098, "勺(小黄豆)");
  static const Unit unitId1099 = Unit(1099, "包(小)");
  static const Unit unitId1100 = Unit(1100, "节（大）");
  static const Unit unitId1101 = Unit(1101, "餐");
  static const Unit unitId1102 = Unit(1102, "碟(小)");
  static const Unit unitId1103 = Unit(1103, "小份");
  static const Unit unitId1104 = Unit(1104, "小条");
  static const Unit unitId1105 = Unit(1105, "中包");
  static const Unit unitId1106 = Unit(1106, "小杯");
  static const Unit unitId1107 = Unit(1107, "1个");
  static const Unit unitId1108 = Unit(1108, "个(圆)");
  static const Unit unitId1109 = Unit(1109, "厚片");
  static const Unit unitId1110 = Unit(1110, "听");
  static const Unit unitId1111 = Unit(1111, "蝶");
  static const Unit unitId1112 = Unit(1112, "碗（乐扣碗）");
  static const Unit unitId1113 = Unit(1113, "勺（瓷勺）");
  static const Unit unitId1114 = Unit(1114, "个（大、方）");
  static const Unit unitId1115 = Unit(1115, "盒1");
  static const Unit unitId1116 = Unit(1116, "盒2");
  static const Unit unitId1117 = Unit(1117, "盒3");
  static const Unit unitId1118 = Unit(1118, "片（大）");
  static const Unit unitId1119 = Unit(1119, "分");
  static const Unit unitId1120 = Unit(1120, "支（小）");
  static const Unit unitId1121 = Unit(1121, "大份");
  static const Unit unitId1122 = Unit(1122, "中瓶");
  static const Unit unitId1123 = Unit(1123, "颗（小）");
  static const Unit unitId1124 = Unit(1124, "两(食堂)");
  static const Unit unitId1125 = Unit(1125, "只（大）");
  static const Unit unitId1126 = Unit(1126, "碗（大）");
  static const Unit unitId1127 = Unit(1127, "包(中)");
  static const Unit unitId1128 = Unit(1128, "中杯");
  static const Unit unitId1129 = Unit(1129, "屉");
  static const Unit unitId1130 = Unit(1130, "筒");
  static const Unit unitId1131 = Unit(1131, "拳头");
  static const Unit unitId1132 = Unit(1132, "片（中）");
  static const Unit unitId1133 = Unit(1133, "个(大、圆)");
  static const Unit unitId1134 = Unit(1134, "小棵");
  static const Unit unitId1135 = Unit(1135, "包(大)");
  static const Unit unitId1136 = Unit(1136, "超大罐");
  static const Unit unitId1137 = Unit(1137, "中碗");
  static const Unit unitId1138 = Unit(1138, "棵（中）");
  static const Unit unitId1139 = Unit(1139, "瓷勺");
  static const Unit unitId1140 = Unit(1140, "杯（小）");
  static const Unit unitId1141 = Unit(1141, "三角包");
  static const Unit unitId1142 = Unit(1142, "封");
  static const Unit unitId1143 = Unit(1143, "盒子");

  // 根据 ID 获取 Unit 对象
  static Unit getUnitById(int id) {
    return values.firstWhere((unit) => unit.id == id, orElse: () => defaultUnit);
  }

  // 根据名称获取 ID
  static int getIdByName(String name) {
    final unit = values.firstWhere((unit) => unit.name == name, orElse: () => defaultUnit);
    return unit.id;
  }
}

