/// FileName: insulin_plan
///
/// @Author: ygc
/// @Date: 2024/7/10 10:08
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const _insulinPlanReinforceFour = 1;
const _insulinPlanPremixTwo = 2;
const _insulinPlanBasicOne = 3;
const _insulinPlanOther = 4;

class InsulinPlan extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const InsulinPlan({
    required this.id,
    required this.buildName,
  });

  static InsulinPlan reinforceFour = InsulinPlan(
      id: _insulinPlanReinforceFour,
      buildName: (context) {
        return S.of(context).insulinPlanReinforceFour;
      });

  static InsulinPlan premixTwo = InsulinPlan(
      id: _insulinPlanPremixTwo,
      buildName: (context) {
        return S.of(context).insulinPlanPremixTwo;
      });
  static InsulinPlan basicOne = InsulinPlan(
      id: _insulinPlanBasicOne,
      buildName: (context) {
        return S.of(context).insulinPlanBasicOne;
      });
  static InsulinPlan other = InsulinPlan(
      id: _insulinPlanOther,
      buildName: (context) {
        return S.of(context).insulinPlanOther;
      });

  static InsulinPlan? fromId(int id) {
    switch (id) {
      case _insulinPlanReinforceFour:
        return reinforceFour;
      case _insulinPlanPremixTwo:
        return premixTwo;
      case _insulinPlanBasicOne:
        return basicOne;
      case _insulinPlanOther:
        return other;
      default:
        return null;
    }
  }

  static List<InsulinPlan> getOptions() {
    return [
      reinforceFour,
      premixTwo,
      basicOne,
      other,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "InsulinPlan{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
