/// FileName: uric_acid_complication
///
/// @Author: ygc
/// @Date: 2024/7/9 17:51
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _complicationNone = 10000;
const int _complicationHighBloodPressure = 1;
const int _complicationHighBloodFat = 2;
const int _complicationDiabetes = 3;
const int _complicationFat = 4;
const int _complicationStroke = 5;
const int _complicationCoronaryHeartDisease = 6;
const int _complicationHeartFailure = 7;
const int _complicationKidneyStone = 8;
const int _complicationRenal = 9;
const int _complicationOther = 10;

class UricAcidComplication extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const UricAcidComplication({
    required this.id,
    required this.buildName,
  });

  static UricAcidComplication none = UricAcidComplication(
      id: _complicationNone,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeNone;
      });

  static UricAcidComplication highBloodPressure = UricAcidComplication(
      id: _complicationHighBloodPressure,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeHighBloodPressure;
      });

  static UricAcidComplication highBloodFat = UricAcidComplication(
      id: _complicationHighBloodFat,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeHighBloodFat;
      });

  static UricAcidComplication diabetes = UricAcidComplication(
      id: _complicationDiabetes,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeDiabetes;
      });
  static UricAcidComplication fat = UricAcidComplication(
      id: _complicationFat,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeFat;
      });

  static UricAcidComplication stroke = UricAcidComplication(
      id: _complicationStroke,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeStroke;
      });
  static UricAcidComplication coronaryHeartDisease = UricAcidComplication(
      id: _complicationCoronaryHeartDisease,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeCoronaryHeartDisease;
      });
  static UricAcidComplication heartFailure = UricAcidComplication(
      id: _complicationHeartFailure,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeHeartFailure;
      });
  static UricAcidComplication kidneyStone = UricAcidComplication(
      id: _complicationKidneyStone,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeKidneyStone;
      });

  static UricAcidComplication renal = UricAcidComplication(
      id: _complicationRenal,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeRenal;
      });

  static UricAcidComplication other = UricAcidComplication(
      id: _complicationOther,
      buildName: (context) {
        return S.of(context).uricAcidComplicationTypeOther;
      });

  static UricAcidComplication? fromId(int id) {
    switch (id) {
      case _complicationNone:
        return UricAcidComplication.none;

      case _complicationHighBloodPressure:
        return UricAcidComplication.highBloodPressure;
      case _complicationHighBloodFat:
        return UricAcidComplication.highBloodFat;
      case _complicationDiabetes:
        return UricAcidComplication.diabetes;
      case _complicationFat:
        return UricAcidComplication.fat;
      case _complicationStroke:
        return UricAcidComplication.stroke;
      case _complicationCoronaryHeartDisease:
        return UricAcidComplication.coronaryHeartDisease;
      case _complicationHeartFailure:
        return UricAcidComplication.heartFailure;
      case _complicationKidneyStone:
        return UricAcidComplication.kidneyStone;
      case _complicationRenal:
        return UricAcidComplication.renal;
      case _complicationOther:
        return UricAcidComplication.other;

      default:
        return null;
    }
  }

  static List<UricAcidComplication> getOptions() {
    return [
      UricAcidComplication.none,
      UricAcidComplication.highBloodPressure,
      UricAcidComplication.highBloodFat,
      UricAcidComplication.diabetes,
      UricAcidComplication.fat,
      UricAcidComplication.stroke,
      UricAcidComplication.coronaryHeartDisease,
      UricAcidComplication.heartFailure,
      UricAcidComplication.kidneyStone,
      UricAcidComplication.renal,
      UricAcidComplication.other,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "UricAcidComplication{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
