/// FileName: diabetes_type
///
/// @Author: ygc
/// @Date: 2024/7/8 18:46
/// @Description:

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

// typedef NS_ENUM(int, DiabetesTypeENUM) {
// ClassIDiebetesType = 1,      //一型糖尿病
// ClassIIDiebetesType,         //二型糖尿病
// ClassGestationDiebetesType,  //妊娠糖尿病
// ClassOtherDiebetesType,      //特殊糖尿病
// ClassNODiebetesTypeF,        //无糖尿病，我是家属
// ClassNODiebetesTypeD,        //无糖尿病，我是医生
// ClassNODiebetesTypeN,        //无糖尿病，我是护士
// ClassOther,                  //其他
// ClassFront                   //糖尿病前期
// };

const _typeOne = 1;
const _typeTwo = 2;
const _typePregnancy = 3;
const _typeOtherDisease = 4;
const _typeFamily = 5;
const _typeDoctor = 6;
const _typeNurse = 7;
const _typeOther = 8;
const _typeEarlier = 9;

class DiabetesType extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const DiabetesType({
    required this.id,
    required this.buildName,
  });

  static DiabetesType one = DiabetesType(
      id: _typeOne,
      buildName: (context) {
        return S.of(context).diabetesTypeOne;
      });

  static DiabetesType two = DiabetesType(
      id: _typeTwo,
      buildName: (context) {
        return S.of(context).diabetesTypeTwo;
      });

  static DiabetesType pregnancy = DiabetesType(
      id: _typePregnancy,
      buildName: (context) {
        return S.of(context).diabetesTypePregnancy;
      });

  static DiabetesType otherDisease = DiabetesType(
      id: _typeOtherDisease,
      buildName: (context) {
        return S.of(context).diabetesTypeOtherDisease;
      });

  static DiabetesType earlier = DiabetesType(
      id: _typeEarlier,
      buildName: (context) {
        return S.of(context).diabetesTypeEarlier;
      });

  static DiabetesType other = DiabetesType(
      id: _typeOther,
      buildName: (context) {
        return S.of(context).diabetesTypeOther;
      });

  static DiabetesType family = DiabetesType(
      id: _typeFamily,
      buildName: (context) {
        return S.of(context).diabetesTypeFamily;
      });

  static DiabetesType doctor = DiabetesType(
      id: _typeDoctor,
      buildName: (context) {
        return S.of(context).diabetesTypeDoctor;
      });

  static DiabetesType nurse = DiabetesType(
      id: _typeNurse,
      buildName: (context) {
        return S.of(context).diabetesTypeNurse;
      });

  static DiabetesType? fromId(int id) {
    switch (id) {
      case _typeOne:
        return one;
      case _typeTwo:
        return two;
      case _typePregnancy:
        return pregnancy;
      case _typeOtherDisease:
        return otherDisease;
      case _typeEarlier:
        return earlier;
      case _typeFamily:
        return family;
      case _typeDoctor:
        return doctor;
      case _typeNurse:
        return nurse;
      case _typeOther:
        return other;

      default:
        return null;
    }
  }

  static List<DiabetesType> getOptions() {
    return [
      DiabetesType.one,
      DiabetesType.two,
      DiabetesType.pregnancy,
      DiabetesType.otherDisease,
      DiabetesType.earlier,
      DiabetesType.other,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "DiabetesType{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
