/// FileName: uric_acid_treatment_method
///
/// @Author: ygc
/// @Date: 2024/7/10 14:14
/// @Description:
// <string name="ua_treat_method_1">饮食控制</string>
// <string name="ua_treat_method_2">运动控制</string>
// <string name="ua_treat_method_3">口服药</string>
// <string name="ua_treat_method_4">手术切石</string>

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _treatMethodNone = 1 << 2;
const int _treatMethodFoodControl = 1 << 3;
const int _treatMethodSportControl = 1 << 4;
const int _treatMethodDrug = 1 << 5;
const int _treatMethodSurgery = 10000;

class UricAcidTreatmentMethod extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const UricAcidTreatmentMethod({
    required this.id,
    required this.buildName,
  });

  static UricAcidTreatmentMethod none = UricAcidTreatmentMethod(
      id: _treatMethodNone,
      buildName: (context) {
        return S.of(context).uricAcidTreatMethodNone;
      });

  static UricAcidTreatmentMethod foodControl = UricAcidTreatmentMethod(
      id: _treatMethodFoodControl,
      buildName: (context) {
        return S.of(context).uricAcidTreatMethodFoodControl;
      });

  static UricAcidTreatmentMethod sportControl = UricAcidTreatmentMethod(
      id: _treatMethodSportControl,
      buildName: (context) {
        return S.of(context).uricAcidTreatMethodSportControl;
      });

  static UricAcidTreatmentMethod drug = UricAcidTreatmentMethod(
      id: _treatMethodDrug,
      buildName: (context) {
        return S.of(context).uricAcidTreatMethodDrug;
      });

  static UricAcidTreatmentMethod surgery = UricAcidTreatmentMethod(
      id: _treatMethodSurgery,
      buildName: (context) {
        return S.of(context).uricAcidTreatMethodSurgery;
      });

  static UricAcidTreatmentMethod? fromId(int id) {
    switch (id) {
      case _treatMethodNone:
        return none;
      case _treatMethodFoodControl:
        return foodControl;
      case _treatMethodSportControl:
        return sportControl;
      case _treatMethodDrug:
        return drug;
      case _treatMethodSurgery:
        return surgery;

      default:
        return null;
    }
  }

  static List<UricAcidTreatmentMethod> fromValue(int value) {
    List<UricAcidTreatmentMethod> result = [];
    for (var item in getOptions()) {
      if ((value & item.id) == item.id) {
        result.add(item);
      }
    }

    return result;
  }

  static int toValue(List<UricAcidTreatmentMethod> options) {
    int result = 0;
    for (var item in options) {
      result |= item.id;
    }
    return result;
  }

  static List<UricAcidTreatmentMethod> getOptions() {
    return [
      none,
      foodControl,
      sportControl,
      surgery,
      drug,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "UricAcidTreatmentMethod{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
