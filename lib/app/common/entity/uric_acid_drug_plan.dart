/// FileName: uric_acid_drug_plan
///
/// @Author: ygc
/// @Date: 2024/7/10 14:08
/// @Description: TODO

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _drugPlan1 = 1;
const int _drugPlan2 = 1 << 1;
const int _drugPlan3 = 1 << 2;
const int _drugPlan4 = 1 << 3;
const int _drugPlanOther = 1 << 4;

class UricAcidDrugPlan extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const UricAcidDrugPlan({
    required this.id,
    required this.buildName,
  });

  static UricAcidDrugPlan plan1 = UricAcidDrugPlan(
      id: _drugPlan1,
      buildName: (context) {
        return S.of(context).uricAcidDrugPlan1;
      });
  static UricAcidDrugPlan plan2 = UricAcidDrugPlan(
      id: _drugPlan2,
      buildName: (context) {
        return S.of(context).uricAcidDrugPlan2;
      });

  static UricAcidDrugPlan plan3 = UricAcidDrugPlan(
      id: _drugPlan3,
      buildName: (context) {
        return S.of(context).uricAcidDrugPlan3;
      });
  static UricAcidDrugPlan plan4 = UricAcidDrugPlan(
      id: _drugPlan4,
      buildName: (context) {
        return S.of(context).uricAcidDrugPlan4;
      });

  static UricAcidDrugPlan planOther = UricAcidDrugPlan(
      id: _drugPlanOther,
      buildName: (context) {
        return S.of(context).uricAcidDrugPlanOther;
      });

  static UricAcidDrugPlan? fromId(int id) {
    switch (id) {
      case _drugPlan1:
        return plan1;
      case _drugPlan2:
        return plan2;
      case _drugPlan3:
        return plan3;
      case _drugPlan4:
        return plan4;
      case _drugPlanOther:
        return planOther;

      default:
        return null;
    }
  }

  static List<UricAcidDrugPlan> fromValue(int value) {
    List<UricAcidDrugPlan> list = [];
    if (value == 0) {
      return list;
    }
    for (var i = 0; i < getOptions().length; i++) {
      if ((value & getOptions()[i].id) == getOptions()[i].id) {
        list.add(getOptions()[i]);
      }
    }
    return list;
  }

  static int toValue(List<UricAcidDrugPlan> list) {
    int value = 0;
    for (var i = 0; i < list.length; i++) {
      value = value | list[i].id;
    }
    return value;
  }

  static List<UricAcidDrugPlan> getOptions() {
    return [
      plan1,
      plan2,
      plan3,
      plan4,
      planOther,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "UricAcidDrugPlan{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
