/// FileName: diabetes_complication
///
/// @Author: ygc
/// @Date: 2024/7/9 16:47
/// @Description:
///
///
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _complicationNone = 10000;
const int _complicationFoot = 1;
const int _complicationEye = 2;
const int _complicationRenal = 3;
const int _complicationCardiovascular = 4;
const int _complicationNerve = 5;
const int _complicationSkin = 6;
const int _complicationHighBloodPressure = 7;
const int _complicationHighBloodFat = 8;
const int _complicationOther = 9;

class DiabetesComplication extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const DiabetesComplication({
    required this.id,
    required this.buildName,
  });

  static DiabetesComplication none = DiabetesComplication(
      id: _complicationNone,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeNone;
      });

  static DiabetesComplication foot = DiabetesComplication(
      id: _complicationFoot,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeFoot;
      });

  static DiabetesComplication eye = DiabetesComplication(
      id: _complicationEye,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeEye;
      });

  static DiabetesComplication renal = DiabetesComplication(
      id: _complicationRenal,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeRenal;
      });
  static DiabetesComplication cardiovascular = DiabetesComplication(
      id: _complicationCardiovascular,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeCardiovascular;
      });

  static DiabetesComplication nerve = DiabetesComplication(
      id: _complicationNerve,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeNerve;
      });

  static DiabetesComplication skin = DiabetesComplication(
      id: _complicationSkin,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeSkin;
      });

  static DiabetesComplication highBloodPressure = DiabetesComplication(
      id: _complicationHighBloodPressure,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeHighBloodPressure;
      });

  static DiabetesComplication highBloodFat = DiabetesComplication(
      id: _complicationHighBloodFat,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeHighBloodFat;
      });

  static DiabetesComplication other = DiabetesComplication(
      id: _complicationOther,
      buildName: (context) {
        return S.of(context).diabetesComplicationTypeOther;
      });

  static DiabetesComplication? fromId(int id) {
    switch (id) {
      case _complicationNone:
        return DiabetesComplication.none;

      case _complicationFoot:
        return DiabetesComplication.foot;

      case _complicationEye:
        return DiabetesComplication.eye;

      case _complicationRenal:
        return DiabetesComplication.renal;

      case _complicationCardiovascular:
        return DiabetesComplication.cardiovascular;

      case _complicationNerve:
        return DiabetesComplication.nerve;

      case _complicationSkin:
        return DiabetesComplication.skin;

      case _complicationHighBloodPressure:
        return DiabetesComplication.highBloodPressure;

      case _complicationHighBloodFat:
        return DiabetesComplication.highBloodFat;

      case _complicationOther:
        return DiabetesComplication.other;

      default:
        return null;
    }
  }

  static List<DiabetesComplication> getOptions() {
    return [
      DiabetesComplication.none,
      DiabetesComplication.foot,
      DiabetesComplication.eye,
      DiabetesComplication.renal,
      DiabetesComplication.cardiovascular,
      DiabetesComplication.nerve,
      DiabetesComplication.skin,
      DiabetesComplication.highBloodPressure,
      DiabetesComplication.highBloodFat,
      DiabetesComplication.other,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "DiabetesComplication{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
