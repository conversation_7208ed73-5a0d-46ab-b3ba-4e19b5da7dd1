/// FileName: blood_state
///
/// @Author: ygc
/// @Date: 2024/7/10 17:56
/// @Description:
///

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _stateNormal = 1;
const int _stateHighEmpty = 1 << 1;
const int _stateHighAfterMeal = 1 << 2;
const int _stateHighOftenLow = 1 << 3;
const int _stateHighUndulate = 1 << 4;

class BloodState extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const BloodState({
    required this.id,
    required this.buildName,
  });

  static BloodState normal = BloodState(
      id: _stateNormal,
      buildName: (context) {
        return S.of(context).bloodStateNormal;
      });

  static BloodState highEmpty = BloodState(
      id: _stateHighEmpty,
      buildName: (context) {
        return S.of(context).bloodStateHighEmpty;
      });
  static BloodState highAfterMeal = BloodState(
      id: _stateHighAfterMeal,
      buildName: (context) {
        return S.of(context).bloodStateHighAfterMeal;
      });

  static BloodState highOftenLow = BloodState(
      id: _stateHighOftenLow,
      buildName: (context) {
        return S.of(context).bloodStateHighOftenLow;
      });
  static BloodState highUndulate = BloodState(
      id: _stateHighUndulate,
      buildName: (context) {
        return S.of(context).bloodStateHighUndulate;
      });

  static BloodState? fromId(int id) {
    switch (id) {
      case _stateNormal:
        return normal;
      case _stateHighEmpty:
        return highEmpty;
      case _stateHighAfterMeal:
        return highAfterMeal;
      case _stateHighOftenLow:
        return highOftenLow;
      case _stateHighUndulate:
        return highUndulate;
      default:
        return null;
    }
  }

  static List<BloodState> fromValue(int value) {
    List<BloodState> result = [];
    for (var item in getOptions()) {
      if ((value & item.id) == item.id) {
        result.add(item);
      }
    }

    return result;
  }

  static int toValue(List<BloodState> list) {
    int result = 0;
    for (var item in list) {
      result |= item.id;
    }
    return result;
  }

  static List<BloodState> getOptions() {
    return [
      normal,
      highEmpty,
      highAfterMeal,
      highOftenLow,
      highUndulate,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "BloodState{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
