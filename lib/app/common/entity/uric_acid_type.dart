/// FileName: uric_acid_type
///
/// @Author: ygc
/// @Date: 2024/7/9 09:36
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

class UricAcidPhrase extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const UricAcidPhrase({
    required this.id,
    required this.buildName,
  });

  static UricAcidPhrase seven = UricAcidPhrase(
      id: 0,
      buildName: (context) {
        return S.of(context).uricAcidSeven;
      });

  static UricAcidPhrase one = UricAcidPhrase(
      id: 1,
      buildName: (context) {
        return S.of(context).uricAcidOne;
      });
  static UricAcidPhrase tow = UricAcidPhrase(
      id: 2,
      buildName: (context) {
        return S.of(context).uricAcidTwo;
      });
  static UricAcidPhrase three = UricAcidPhrase(
      id: 3,
      buildName: (context) {
        return S.of(context).uricAcidThree;
      });
  static UricAcidPhrase four = UricAcidPhrase(
      id: 4,
      buildName: (context) {
        return S.of(context).uricAcidFour;
      });
  static UricAcidPhrase five = UricAcidPhrase(
      id: 5,
      buildName: (context) {
        return S.of(context).uricAcidFive;
      });
  static UricAcidPhrase six = UricAcidPhrase(
      id: 6,
      buildName: (context) {
        return S.of(context).uricAcidSix;
      });

  static UricAcidPhrase? fromId(int id) {
    switch (id) {
      case 0:
        return seven;
      case 1:
        return one;
      case 2:
        return tow;
      case 3:
        return three;
      case 4:
        return four;
      case 5:
        return five;
      case 6:
        return six;
      default:
        return null;
    }
  }

  static List<UricAcidPhrase> getOptions() {
    return [
      UricAcidPhrase.seven,
      UricAcidPhrase.one,
      UricAcidPhrase.tow,
      UricAcidPhrase.three,
      UricAcidPhrase.four,
      UricAcidPhrase.five,
      UricAcidPhrase.six,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "UricAcidType{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
