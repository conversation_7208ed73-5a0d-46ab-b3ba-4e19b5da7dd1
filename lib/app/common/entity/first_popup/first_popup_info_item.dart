/// FileName: first_popup_info_item
///
/// @Author: ygc
/// @Date: 2025/6/23 14:15
/// @Description:
import 'package:freezed_annotation/freezed_annotation.dart';

part 'first_popup_info_item.freezed.dart';

part 'first_popup_info_item.g.dart';

@freezed
class FirstPopupInfoItem with _$FirstPopupInfoItem {
  factory FirstPopupInfoItem({
    @Default(0) int times,
    @Default(0) int lastTime,

  }) = _FirstPopupInfoItem;

  FirstPopupInfoItem._();

  factory FirstPopupInfoItem.fromJson(Map<String, Object?> json) =>
      _$FirstPopupInfoItemFromJson(json);
}