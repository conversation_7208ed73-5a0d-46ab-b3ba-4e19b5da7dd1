/// FileName: pregnancy_diabetes_type
///
/// @Author: ygc
/// @Date: 2024/7/10 16:50
/// @Description:
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';

const int _prePregnancy = 1;
const int _pregnancy = 2;
const int _other = 3;

class PregnancyDiabetesType extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;

  const PregnancyDiabetesType({
    required this.id,
    required this.buildName,
  });

  static PregnancyDiabetesType prePregnancy = PregnancyDiabetesType(
      id: _prePregnancy,
      buildName: (context) {
        return S.of(context).pregnancyDiabetesTypePrePregnancy;
      });

  static PregnancyDiabetesType pregnancy = PregnancyDiabetesType(
      id: _pregnancy,
      buildName: (context) {
        return S.of(context).pregnancyDiabetesTypePregnancy;
      });
  static PregnancyDiabetesType other = PregnancyDiabetesType(
      id: _other,
      buildName: (context) {
        return S.of(context).pregnancyDiabetesTypeOther;
      });

  static PregnancyDiabetesType? fromId(int id) {
    switch (id) {
      case _prePregnancy:
        return prePregnancy;

      case _pregnancy:
        return pregnancy;
      case _other:
        return other;

      default:
        return null;
    }
  }

  static List<PregnancyDiabetesType> getOptions() {
    return [
      prePregnancy,
      pregnancy,
      other,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  @override
  String toString() {
    return "PregnancyDiabetesType{id: $id}";
  }

  @override
  List<Object?> get props => [id];
}
