import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:dnurse/app/common/web/web_utils.dart';
import 'package:dnurse/framework/utils/platform_utils.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../user/event/login_changed.dart';
import '../../web/dto/set_share_data_dto/set_share_data_dto.dart';
import '../../web/entity/webview_title_info.dart';
import '../../web/webview_handler.dart';
import 'browser_request.dart';
import '../../../common/web/webview_interface.dart';

class Browser extends StatefulWidget {
  const Browser({this.title, super.key});

  final String? title;

  @override
  BrowserState createState() {
    return BrowserState();
  }
}

class BrowserState extends State<Browser> {
  late WebViewController _webViewController;

  String _title = '';
  bool _showClose = false;
  bool _appBarHidden = false;
  Widget? titleWidget;
  List<Widget>? titleActions;
  SetShareDataDTO? setShareDataDTO;
  late StreamSubscription loginChangedSubscription;
  double loadProgress = 0.0;

  bool _isLoading = DNUPlatform.isOhos ? false : true;

  late BrowserRequest _browserRequest;

  late WebInterfaceHandler webInterfaceHandler;
  bool _appVerAtHead = false;

  void _initSubscription() {
    loginChangedSubscription =
        EventService.to.bus.on<LoginChanged>().listen((event) {
      Log.i("========= _initSubscription");
      WebUtils.setTokenCookie();
      _webViewController.runJavaScript("location.reload()");
    });
  }

  void _cancelSubscription() {
    loginChangedSubscription.cancel();
  }

  @override
  void initState() {
    super.initState();

    _appVerAtHead = (Get.parameters[RouteParams.appVerAtHead] ?? '') == "1";
    _appVerAtHead = true;

    _initSubscription();

    _title = widget.title ?? '';

    if (Get.parameters.containsKey('title') &&
        Get.parameters['title'] != null) {
      _title = Get.parameters['title']!;
    }

    if (Get.arguments is String) {
      _browserRequest = BrowserRequest(Get.arguments);
    } else {
      _browserRequest = Get.arguments as BrowserRequest;
    }

    _initWebView();
    _initWebHandler();
  }

  void _initWebHandler() {
    webInterfaceHandler = WebInterfaceHandler(
      webViewController: _webViewController,
      onActionClick: _handleActionClick,
      onTitleChanged: _onWebViewTitleChanged,
      onAppBarHidden: _onWebViewAppBarHidden,
      onClosePage: _onWebViewClosePage,
    );

    webInterfaceHandler.init();
  }

  void _onWebViewAppBarHidden() {
    setState(() {
      _appBarHidden = true;
    });
  }

  void _onWebViewClosePage() {
    AppRouter.back();
  }

  void _onWebViewTitleChanged(
      WebViewTitleInfo? titleInfo, Map<String, Object?> appJson) {
    if (titleInfo != null) {
      titleWidget = titleInfo.center;
      titleActions = titleInfo.actions;
      setState(() {});
    }

    _webViewController.canGoBack().then((value) {
      setState(() {
        _showClose = value;
      });
    });
  }

  @override
  void dispose() {
    _cancelSubscription();
    webInterfaceHandler.dispose();

    super.dispose();
  }

  void _initWebView() async {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted);

    _webViewController.setNavigationDelegate(
        WebUtils.getNavigationDelegate(onPageStarted: (String url) {
      setState(() {
        loadProgress = 0.0;
        _isLoading = false;
      });
    }, onProgress: (int progress) {
      setState(() {
        loadProgress = progress * 1.0 / 100;
      });
    }, onPageFinished: (String url) {
      setState(() {
        loadProgress = 1.0;
      });

      Log.d('Page finished loading: $url');
      _webViewController.getTitle().then((String? title) {
        Log.d('_webViewController get Title: $title');
        if (title == '连接家人' || title == '推荐有礼') {
          WebViewInterface.injectDnuApp(_webViewController);
        }

        if (title != null) {
          setState(() {
            _title = title;
          });
        }
      });
      _webViewController.canGoBack().then((value) {
        setState(() {
          _showClose = value;
        });
      });

      _browserRequest.onPageFinished?.call(_webViewController);
    }, onWebResourceError: (WebResourceError error) {
      Log.d('WebResourceError: ${error.description}');
    }, onDeepLinkResult: (url, result) {
      _browserRequest.onDeepLinkResult?.call(_webViewController, url, result);
    }));

    _webViewController.setOnJavaScriptAlertDialog((request) async {
      Log.d("setOnJavaScriptAlertDialog: ${request.message}, ${request.url}");
      webInterfaceHandler.handleMessage(request.message);
      // return handleWebMessage(request);
    });

    WebViewInterface.setInterface(_webViewController);

    await WebUtils.setUserAgent(_webViewController,
        appVerAtHead: _appVerAtHead);
    await WebUtils.setTokenCookie();
    WebUtils.initGrant(_webViewController);

    _webViewController.loadRequest(Uri.parse(_browserRequest.url));
  }

  void _handleActionClick(String action) {
    Log.i("_handleActionClick: $action");
  }

  Widget? _buildLeading(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);
    final bool canPop = parentRoute?.canPop ?? false;
    if (!canPop && !_showClose) {
      return null;
    }

    if (canPop && _showClose) {
      return BrnDoubleLeading(
        first: BrnBackLeading(
          child: const Icon(Icons.arrow_back_ios),
          iconPressed: () {
            Navigator.of(context).maybePop();
          },
        ),
        second: BrnBackLeading(
          child: const Icon(Icons.close),
          iconPressed: () {
            Navigator.pop(context);
          },
        ),
      );
    }

    if (canPop) {
      return BrnBackLeading(
        child: const Icon(Icons.arrow_back_ios),
        iconPressed: () {
          // Navigator.pop(context);
          Navigator.of(context).maybePop();
        },
      );
    }

    if (_showClose) {
      return BrnBackLeading(
        child: const Icon(Icons.close),
        iconPressed: () {
          Navigator.pop(context);
        },
      );
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    webInterfaceHandler.setBuildContext(context);

    return WillPopScope(
      onWillPop: () async {
        await WebUtils.stopAudioPlay(_webViewController);
        if (await _webViewController.canGoBack()) {
          _webViewController.goBack();

          return false;
        }

        return true;
      },
      child: SafeArea(
        top: false,
        child: Scaffold(
          appBar: _appBarHidden
              ? null
              : BrnAppBar(
                  title: titleWidget ?? _title,
                  // themeData: BrnAppBarConfig.dark(),
                  leading: _buildLeading(context),
                  actions: titleActions,
                ),
          body: SafeArea(
            child: Stack(
              children: [
                SizedBox.expand(
                  child: !_isLoading
                      ? WebViewWidget(controller: _webViewController)
                      : const Center(
                          child: CircularProgressIndicator(),
                        ),
                ),
                // 添加水平进度条，在页面添加完成后隐藏
                if (loadProgress >= 0 && loadProgress < 1)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: LinearProgressIndicator(
                      value: loadProgress,
                      minHeight: 4,
                      backgroundColor: Colors.grey,
                      color: Colors.blue,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
