import 'package:webview_flutter/webview_flutter.dart';

/// FileName: browser_request
///
/// @Author: ygc
/// @Date: 2024/10/15 17:33
/// @Description:
class BrowserRequest {
  String url;
  Function(WebViewController webViewController)? onPageFinished;
  void Function(
          WebViewController webViewController, String url, dynamic result)?
      onDeepLinkResult;

  BrowserRequest(
    this.url, {
    this.onPageFinished,
    this.onDeepLinkResult,
  });
}
