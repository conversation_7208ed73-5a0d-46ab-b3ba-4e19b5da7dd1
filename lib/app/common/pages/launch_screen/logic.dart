import 'dart:async';

import 'package:dnurse/framework/event/app_boot_finished.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../bootstrap/app.dart';
import '../../../../framework/route/base_controller.dart';
import '../../../../framework/service/app_config.dart';
import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../../framework/utils/platform_utils.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../user/pages/private/private_dialog.dart';
import '../../api/dto/boot_item_dto/boot_item_dto.dart';
import '../../service/boot_service.dart';
import 'state.dart';

const String kLogExamplePage = '''
<!DOCTYPE html>
<html lang="en">
<head>
<title></title>
</head>
<body >
</body>
</html>
''';

class LaunchScreenLogic extends DNUBaseController {
  final LaunchScreenState state = LaunchScreenState();
  late StreamSubscription appLoadingFinishSubscription;
  late WebViewController webViewController;
  VideoPlayerController? videoController;

  bool _hasStart = false;
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();

    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted);

    webViewController.loadHtmlString(kLogExamplePage);

    appLoadingFinishSubscription =
        EventService.to.bus.on<AppLoadingFinish>().listen((event) {
      if (_hasStart) {
        return;
      }

      _hasStart = true;

      BootItemDTO? bootItemDTO = BootService.to.randomBootItem();
      Log.i("bootItemDTO: $bootItemDTO");
      if (bootItemDTO != null) {
        state.bootItem.value = bootItemDTO;
      }

      _loadAd();
    });

    if (AppContext.to.appFinishBootUp) {
      _hasStart = true;

      _startCountDown();
    }
  }

  void _loadAd() async {
    if (state.bootItem.value == null) {
      _startCountDown();
      return;
    }

    BootItemDTO bootItem = state.bootItem.value!;
    if (bootItem.type == '2') {
      String videoUrl = bootItem.videoUrl;
      // videoUrl =
      //     'https://dnurseimages.blob.core.chinacloudapi.cn/shop/U1548064345559.mp4';

      if (videoUrl.isEmpty) {
        _startCountDown();
        return;
      }

      FileInfo? fileInfo = await BootImageCacheManager.getImage(videoUrl);
      if (fileInfo == null || fileInfo.source != FileSource.Cache) {
        _startCountDown();
      } else {
        videoController = VideoPlayerController.file(fileInfo.file);
        await videoController?.initialize();
        videoController?.setVolume(0.0);
        videoController?.addListener(_videoListener);
        videoController?.play();
        state.isShowAd.value = true;
      }
    } else {
      state.countDown.value = 10;
      _startCountDown();
      state.isShowAd.value = true;
    }
  }

  @override
  void onClose() {
    appLoadingFinishSubscription.cancel();

    if (state.bootItem.value != null && state.isShowAd.value) {
      BootService.to.increaseShowTimes(state.bootItem.value!);
    }

    videoController?.dispose();

    super.onClose();
  }

  void _videoListener() {
    if (!videoController!.value.isPlaying) {
      doAction();
    } else {
      state.countDown.value = videoController!.value.duration.inSeconds -
          videoController!.value.position.inSeconds;
    }
  }

  void _startCountDown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      state.countDown.value--;
      if (state.countDown.value <= 0) {
        _timer?.cancel();

        if (DNUPlatform.isOhos) {
          webViewController
              .runJavaScriptReturningResult("navigator.userAgent")
              .then((value) {
            Log.i("webViewController user agent: $value");

            String ua = value.toString();
            ua = ua.replaceAll("\"", "");
            if (ua.isNotEmpty) {
              AppConfig.to.osWebUserAgent = ua;
            }
          }).catchError((onError) {
            Log.e("webViewController user agent error: $onError");
          });
        }

        if (!AppConfig.to.hasAgreePrivateLicence) {
          PrivateDialog.show(onConfirm: () {
            AppRouter.replace(Routes.initial);

            EventService.to.bus.fire(AppBootFinished());
          });
        } else {
          AppRouter.replace(Routes.initial);

          EventService.to.bus.fire(AppBootFinished());
        }
      }
    });
  }

  void doAction() {
    if (_timer != null) {
      _timer?.cancel();
    }

    AppRouter.replace(Routes.initial);
  }

  void gotoDetail() {
    if (state.bootItem.value == null) {
      return;
    }

    AppRouter.replace(Routes.initial, parameters: {
      RouteParams.action: state.bootItem.value!.action,
    }, arguments: {
      RouteParams.action: state.bootItem.value!.action,
    });
  }
}
