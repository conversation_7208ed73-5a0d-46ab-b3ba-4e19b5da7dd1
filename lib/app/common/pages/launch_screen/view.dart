import 'package:dnurse/framework/utils/platform_utils.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../service/boot_service.dart';
import 'logic.dart';

class LaunchScreenPage extends GetView<LaunchScreenLogic> {
  const LaunchScreenPage({super.key});

  @override
  Widget build(BuildContext context) {
    // controller;
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: Padding(
                  padding: EdgeInsets.only(top: 160.h),
                  child: Image.asset(
                    DNUAssets.to.images.common.launchScreenImg,
                    excludeFromSemantics: true,
                    gaplessPlayback: true,
                    fit: BoxFit.fitHeight,
                    height: 364.h,
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 61.h,
            left: 0,
            right: 0,
            child: Image.asset(
              DNUAssets.to.images.common.launchScreenIcon,
              excludeFromSemantics: true,
              gaplessPlayback: true,
              fit: BoxFit.fitHeight,
              height: 21.h,
            ),
          ),
          if (DNUPlatform.isOhos)
            Positioned(
              left: 0,
              top: 0,
              child: SizedBox(
                height: 10,
                width: 10,
                child: WebViewWidget(
                  controller: controller.webViewController,
                ),
              ),
            ),
          Positioned.fill(
            child: Obx(() {
              if (!controller.state.isShowAd.value) {
                return Container();
              }

              return _buildAd(context);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildAd(BuildContext context) {
    bool isVideo = controller.state.bootItem.value!.type == '2';

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              SizedBox.expand(
                child: !isVideo
                    ? DNUImage.network(
                        controller.state.bootItem.value!.pic,
                        useCache: true,
                        cacheManager: BootImageCacheManager.instance,
                        fit: BoxFit.cover,
                      )
                    : controller.videoController != null
                        ? FittedBox(
                            fit: BoxFit.cover,
                            child: SizedBox(
                              // aspectRatio:
                              //     controller.videoController.value.aspectRatio,
                              width:
                                  controller.videoController!.value.size.width,
                              height:
                                  controller.videoController!.value.size.height,
                              child: VideoPlayer(
                                controller.videoController!,
                              ),
                            ),
                          )
                        : Container(),
              ),
              _buildAdTip(context),
              _buildAdSkip(context),
              _buildDetailButton(context),
            ],
          ),
        ),
        SizedBox(
          height: 103.h,
        ),
      ],
    );
  }

  Widget _buildAdTip(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).viewPadding.top + 15.w,
      left: 15.w,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 10.w,
          vertical: 3.w,
        ),
        decoration: BoxDecoration(
            color: const Color(0x99000000),
            borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(6.r),
              topRight: Radius.circular(6.r),
            ),
            boxShadow: const [
              BoxShadow(
                color: Color(0x80000000),
                offset: Offset(0, 1),
                blurRadius: 3,
                spreadRadius: 0,
              )
            ]),
        child: Text(
          "广告",
          style: TextStyle(
            fontSize: 11.sp,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildAdSkip(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).viewPadding.top + 15.w,
      right: 15.w,
      child: InkWell(
        onTap: () {
          controller.doAction();
        },
        child: Container(
          width: 67.w,
          padding: EdgeInsets.symmetric(
            horizontal: 10.w,
            vertical: 3.w,
          ),
          decoration: BoxDecoration(
            color: const Color(0x99000000),
            borderRadius: BorderRadius.circular(6.r),
          ),
          child: Center(
            child: Obx(
              () {
                return Text(
                  controller.state.countDown.value.toString(),
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: Colors.white,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailButton(BuildContext context) {
    return Positioned(
      right: 0,
      left: 0,
      bottom: 60.w,
      child: Center(
        child: GestureDetector(
          onTap: () {
            controller.gotoDetail();
          },
          child: Container(
            width: 300.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: const Color(0x99000000),
              borderRadius: BorderRadius.circular(100.r),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x80000000),
                  offset: Offset(0, 1),
                  blurRadius: 3,
                  spreadRadius: 0,
                )
              ],
            ),
            child: Center(
              child: Text(
                "点击查看详情",
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
