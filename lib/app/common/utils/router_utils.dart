import '../../../framework/network/api_host.dart';
import '../../../router/router.dart';
import '../../../router/routes.dart';

/// FileName: router_utils
///
/// @Author: ygc
/// @Date: 2024/7/12 16:59
/// @Description:

class RouterUtils {
  RouterUtils._();

  static void gotoHelper() {
    const url = "https://www.dnurse.com/mobile/qa.php";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoPrivacy() {
    var url = "${DNUHost.getWebHost()}/v2/service/privacy_policy";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoUserAgreement() {
    var url = "${DNUHost.getWebHost()}/v2/service/user_agreement";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoHuaweiAgreement() {
    var url = 'https://privacy.consumer.huawei.com/legal/id/authentication-terms.htm?code=CN&language=zh-CN';
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url, parameters: {'title': '华为账号用户认证协议'});
    }
  }

  static void gotoThirdSdk() {
    var url = "${DNUHost.getWebHost()}/v2/service/privacy_policy_third_party";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoYouAnJinHelp() {
    var url = "${DNUHost.getWebHost()}/v2/product/help_youanjin";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoXingInfo() {
    var url = "${DNUHost.getActBaseUrl()}/xing/detail?dev_type=0";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoHeInfo() {
    var url = "${DNUHost.getActBaseUrl()}/xing/detail?dev_type=3";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoHeScan() {
    var url = "${DNUHost.getActBaseUrl()}/xing/select_to_scan?dev_type=3";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoXingScan() {
    var url = "${DNUHost.getActBaseUrl()}/xing/select_to_scan?dev_type=0";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoJiaRen() {
    var url = "${DNUHost.getActBaseUrl()}/bind_family";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }

  static void gotoGlucoseList() {
    var url = "${DNUHost.getWebHost()}/v2/goods/glucose_list";
    if (url.isNotEmpty) {
      AppRouter.push(Routes.browser, arguments: url);
    }
  }
}
