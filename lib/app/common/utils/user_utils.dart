/// FileName: user_utils
///
/// @Author: ygc
/// @Date: 2024/7/12 14:11
/// @Description:
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../framework/network/api_host.dart';

class UserUtils {
  UserUtils._();

  static checkLogin({
    BuildContext? context,
    String? message,
    VoidCallback? onFailed,
    VoidCallback? onSuccess,
  }) {
    if (AppContext.to.isTemp) {
      if (onFailed != null) {
        onFailed.call();
      } else if (context != null && context.mounted) {
        AppRouter.push(Routes.login);

        Future.delayed(
          const Duration(seconds: 1),
          () => Get.snackbar('提示', '您还没有登录，无法使用该功能'),
        );
        // DNUToast.show(message ?? '无法连接网络，请检查网络是否打开', context);
      }
      return false;
    } else {
      onSuccess?.call();
      return true;
    }
  }

  static String getUserAvatar(String userSn) {
    if(userSn.isEmpty) {
      return '';
    }

    return "${DNUHost.getApiHost()}/api/user/get_avatar/$userSn";
  }
}
