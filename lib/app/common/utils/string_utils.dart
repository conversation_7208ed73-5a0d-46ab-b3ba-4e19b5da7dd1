/// FileName: string_utils
///
/// @Author: ygc
/// @Date: 2024/7/16 16:28
/// @Description:
import 'package:lpinyin/lpinyin.dart';

class StringUtils {
  StringUtils._();

  static bool isEmpty(String? str) {
    return str == null || str.isEmpty;
  }

  static bool isMobileNumber(String? mobile) {
    if (isEmpty(mobile)) {
      return false;
    }

    RegExp mobileReg = RegExp(r"^1[3456789]\d{9}$");
    return mobileReg.hasMatch(mobile!);
  }

  // 生成判断是否包含中文的函数
  static bool containChinese(String? str) {
    if (isEmpty(str)) {
      return false;
    }

    RegExp reg = RegExp(r'[\u4e00-\u9fa5]');
    return reg.hasMatch(str!);
  }

  static String chineseToPinyin(String str) {
    if (!containChinese(str)) {
      return "";
    }

    try {
      return PinyinHelper.getPinyin(
          str, separator: " ", format: PinyinFormat.WITHOUT_TONE);
    } catch (e) {
      return "";
    }
  }
}
