import 'package:get/get.dart';

import '../../../config/module_priority.dart';
import '../../../framework/module/build/annotations.dart';
import '../../../framework/module/module.dart';
import '../../../router/routes.dart';
import '../pages/browser/browser.dart';
import '../pages/launch_screen/binding.dart';
import '../pages/launch_screen/view.dart';
import 'common_app_lifecycle.dart';

@AppModule(priority: modulePriorityCommon)
class CommonModule extends Module {
  CommonModule({super.priority})
      : super(
          name: 'common',
          applicationLifecycle: CommonAppLifecycle(),
        );

  @override
  // ignore: long-method
  List<GetPage> getRouters() {
    return [
      GetPage(
        name: Routes.browser,
        page: () => const Browser(),
      ),
      GetPage(
        name: Routes.launch,
        page: () => const LaunchScreenPage(),
        binding: LaunchScreenBinding(),
      ),
    ];
  }
}
