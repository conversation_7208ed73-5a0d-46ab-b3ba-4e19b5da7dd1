import 'dart:async';

import 'package:dnurse/app/common/service/pay_service.dart';
import 'package:dnurse/app/common/service/tencent_service.dart';
import 'package:get/get.dart';

import '../../../framework/module/application_lifecycle.dart';
import '../../../framework/utils/log.dart';
import '../service/banner_service.dart';
import '../service/boot_service.dart';
import '../service/wechat_service.dart';

class CommonAppLifecycle extends ApplicationLifecycle {
  @override
  FutureOr<void> onAfterAppInit() {
    Log.i('CommonAppLifecycle onAfterAppInit');

    // Log.i("user token:" + UserContext.to.token);
  }

  @override
  FutureOr<void> onAppInit() async {
    Log.i('CommonAppLifecycle onAppInit');
    Get.lazyPut<WechatService>(() => WechatService());
    Get.lazyPut<TencentService>(() => TencentService());
    Get.lazyPut<PayService>(() => PayService());
    Get.lazyPut<BootService>(() => BootService());
    Get.lazyPut<BannerService>(() => BannerService());
  }

  @override
  FutureOr<void> onAgreedPrivacy() {

  }
}
