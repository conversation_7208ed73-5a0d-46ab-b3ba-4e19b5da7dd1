import 'dart:convert';

import 'package:dnurse/app/common/pages/browser/browser_request.dart';
import 'package:dnurse/app/common/service/wechat_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/network/api_host.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../framework/utils/log.dart';

/// FileName: deep_link
///
/// @Author: ygc
/// @Date: 2024/6/12 16:49
/// @Description:
const linkActionKey = "act";
const linkActionLoginPrefix = "LOGIN";
const linkActionArticlePrefix = "KL:";
const linkActionUrlPrefix = "URL:";
const linkActionShop = "SHOP";
const linkActionInfoPrefix = "INFO:";
const linkActionXingPrefix = "APRICOT:";
const linkActionScan = "SCAN";
const linkActionUserHealthInfo = "USER_HEALTHG_INFO";
const linkActionTestBlood = "TEST_BLOOD";
const linkActionAddData = "ADD_DATA";
const linkActionDataTarget = "DATA_SETTINGS";
const linkActionResetUserProfile = "RESET_REPORT";
const linkActionFeedback = "FEEDBACK";
const linkActionArticleCategory = "ARTICLE_CATE";
const linkActionBottleMessage = "BOTTLE_MESSAGE:";
const linkActionWallet = "WALLET";
const linkActionWechatMiniProgram = "MINI_PROGRAM";

class DeepLinkResult {
  bool handled;
  String? url;
  Future<dynamic>? pageResult;

  DeepLinkResult(
    this.handled, {
    this.url,
    this.pageResult,
  });
}

class DeepLink {
  static DeepLinkResult handle(
    String link, {
    String? source,
    BuildContext? context,
  }) {
    if (!isUrl(link)) {
      return DeepLinkResult(false);
    }

    Uri uri = Uri.parse(link);
    Map<String, String> queryParams = uri.queryParameters;
    String? action = queryParams[linkActionKey];
    if (action == null) {
      return DeepLinkResult(false);
    }

    if (action == linkActionLoginPrefix) {
      return DeepLinkResult(
        true,
        url: Routes.login,
        pageResult: AppRouter.push(Routes.login),
      );
    } else if (action == linkActionTestBlood) {
      return DeepLinkResult(
        true,
        url: Routes.dataTest,
        pageResult: AppRouter.push(Routes.dataTest),
      );
    } else if (action == linkActionAddData) {
      return DeepLinkResult(
        true,
        url: Routes.dataAddBS,
        pageResult: AppRouter.push(Routes.dataAddBS),
      );
    } else if (action == linkActionDataTarget) {
      return DeepLinkResult(
        true,
        url: Routes.dataSugarTarget,
        pageResult: AppRouter.push(Routes.dataSugarTarget),
      );
    } else if (action == linkActionResetUserProfile) {
      return DeepLinkResult(
        true,
        url: Routes.userHealthGuide,
        pageResult: AppRouter.push(Routes.userHealthGuide),
      );
    } else if (action.startsWith(linkActionArticleCategory)) {
      List<String> params = action.split(":");
      if (params.length != 2) {
        return DeepLinkResult(false);
      }

      String categoryId = params[1];

      AppRouter.push(
        Routes.viewAll,
        parameters: {'categoryId': categoryId},
      );
      return DeepLinkResult(false);
    } else if (action.startsWith(linkActionArticlePrefix)) {
      List<String> params = action.split(":");
      if (params.length < 2) {
        return DeepLinkResult(false);
      }
      bool isReplayNew = false;

      List<String> queryParam = params[1].split("&");
      String articleId = queryParam[0];
      if (articleId.endsWith('#news')) {
        isReplayNew = true;
        articleId = articleId.replaceAll('#news', '');
      }

      return DeepLinkResult(
        true,
        url: Routes.getKnowledgeDetail(articleId),
        pageResult: AppRouter.push(Routes.getKnowledgeDetail(articleId),
            parameters: {"isReplayNew": isReplayNew ? "true" : "false"}),
      );
    } else if (action.startsWith(linkActionWechatMiniProgram)) {
      List<String> params = action.split(":");
      if (params.length < 2) {
        return DeepLinkResult(false);
      }
      String findString = "$linkActionWechatMiniProgram:";
      String paramString =
          link.substring(link.indexOf(findString) + findString.length);

      try {
        dynamic paramMap = jsonDecode(paramString);
        String originalId = paramMap['original_id'];
        String path = paramMap['path'];
        int type = paramMap['type'];
        Log.i("originalId: $originalId, path: $path, type: $type");

        WechatService.to.launchMiniProgram(
          userName: originalId,
          path: path,
          type: type,
        );
        return DeepLinkResult(true);
      } catch (e) {
        return DeepLinkResult(false);
      }
    } else if (action.startsWith(linkActionUrlPrefix) ||
        action.startsWith(linkActionUrlPrefix.toLowerCase())) {
      String url;
      if (action.startsWith(linkActionUrlPrefix)) {
        url = action.replaceFirst(linkActionUrlPrefix, "");
      } else {
        url = action.replaceFirst(linkActionUrlPrefix.toLowerCase(), "");
      }

      return DeepLinkResult(
        true,
        url: Routes.browser,
        pageResult: AppRouter.push(
          Routes.browser,
          arguments: url,
        ),
      );
    } else if (action == linkActionShop) {
      AppRouter.switchTab(path: Routes.homeShop);

      return DeepLinkResult(
        true,
      );
    } else if (action == linkActionFeedback) {
      if (Get.context != null) {
        DNUToast.show(
          "当前版本不支持微信客服",
          Get.context!,
          gravity: DNUToastGravity.top,
        );
      }
      return DeepLinkResult(
        true,
      );
    } else if (action.startsWith(linkActionInfoPrefix) ||
        action.startsWith(linkActionInfoPrefix.toLowerCase())) {
      String uid = action.replaceFirst(linkActionInfoPrefix, "");

      return DeepLinkResult(
        true,
        url: Routes.getUserInfo(uid),
        pageResult: AppRouter.push(
          Routes.getUserInfo(uid),
          // arguments: url,
        ),
      );
    } else if (action.startsWith(linkActionXingPrefix) ||
        action.startsWith(linkActionXingPrefix.toLowerCase())) {
      String xingCode = action.replaceFirst(linkActionXingPrefix, "");
      // String xing_code = action.replace("APRICOT:", "");
      // bundle.putString("xing_code", xing_code);

      String url = "${DNUHost.getActBaseUrl()}/xing?no_nav=1&from=android";
      if (source == 'web') {
        AppRouter.back(result: xingCode);
        return DeepLinkResult(true);
      }
      return DeepLinkResult(
        true,
        url: Routes.browser,
        pageResult: AppRouter.push(
          Routes.browser,
          arguments: BrowserRequest(url, onPageFinished: (controller) {
            Log.i("handle xing action: $action, code:$xingCode");
            if (xingCode.isEmpty) {
              return;
            }
            controller.runJavaScript("get_xing_data('$xingCode')");

            xingCode = '';
          }, onDeepLinkResult: (controller, url, result) {
            if (url == DeepLink.addUrlPrefix("SCAN")) {
              try {
                String scanCode = result as String;
                controller.runJavaScript("get_xing_data('$scanCode')");
              } catch (e) {
                Log.e(
                    " APRICOT onDeepLinkResult url:$url, result:$result, error",
                    e);
              }
            }
          }),
        ),
      );
    } else if (action == linkActionScan) {
      if (AppContext.to.isTemp) {
        Get.snackbar('提示', '请先登录');
        return DeepLinkResult(
          true,
        );
      } else {
        return DeepLinkResult(
          true,
          url: Routes.scanCode,
          pageResult: AppRouter.push(
            Routes.scanCode,
            parameters: {
              "source": source ?? '',
            },
            preventDuplicates: true,
          ),
        );
      }
    } else if (action == linkActionUserHealthInfo) {
      return DeepLinkResult(
        true,
        url: Routes.userHealth,
        pageResult: AppRouter.push(
          Routes.userHealth,
          parameters: {
            "source": source ?? '',
          },
          preventDuplicates: true,
        ),
      );
    } else if (action.startsWith(linkActionBottleMessage)) {
      List<String> params = action.split(":");
      if (params.length < 2) {
        return DeepLinkResult(false);
      }

      String bottleId = params[1];
      return DeepLinkResult(
        true,
        url: Routes.getBottleMessage(bottleId),
        pageResult: AppRouter.push(Routes.getBottleMessage(bottleId)),
      );
    } else if (action == (linkActionWallet)) {
      AppRouter.push(
        Routes.wallet,
        parameters: {'from': 'invite'},
      );
      return DeepLinkResult(false);
    } else {
      Log.i("DeepLink not handle link: $link");
    }

    return DeepLinkResult(false);
  }

  static DeepLinkResult handleWidthParameter(
    String link, {
    String? source,
  }) {
    if (!isUrl(link)) {
      Log.i("handleWidthParameter link1: $link");
      link = Uri.encodeComponent(link);
      Log.i("handleWidthParameter link2: $link");
      link = addUrlPrefix(link);
    }

    return handle(
      link,
      source: source,
    );
  }

  static bool isUrl(String link) {
    return link.startsWith("dnurseapp://com.dnurse/openwith?");
  }

  static String addUrlPrefix(String link) {
    return "dnurseapp://com.dnurse/openwith?act=$link";
  }
}
