/// FileName: boot_item_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-18 13:23:15
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'boot_item_dto.freezed.dart';

part 'boot_item_dto.g.dart';

@freezed
class BootItemDTO with _$BootItemDTO {
  factory BootItemDTO({
    @Default('') String action,
    @Default('') String backTime,
    @Default('') @JsonKey(name: 'end_time') String endTime,
    @Default('') @<PERSON>son<PERSON>ey(name: 'end_time_stamp') String endTimeStamp,
    @Default('') String frequency,
    @Default('') String id,
    @Default('') String pic,
    @Default('') @JsonKey(name: 'start_time') String startTime,
    @Default('') @JsonKey(name: 'start_time_stamp') String startTimestamp,
    @Default('') String title,
    @Default('') String type,
    @Default('') String version,
    @Default('') @JsonKey(name: 'video_url') String videoUrl,
  }) = _BootItemDTO;

  BootItemDTO._();

  factory BootItemDTO.fromJson(Map<String, Object?> json) =>
      _$BootItemDTOFromJson(json);
}
