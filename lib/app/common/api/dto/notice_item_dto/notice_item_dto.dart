/// FileName: banner_item_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-23 10:44:56
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'notice_item_dto.freezed.dart';
part 'notice_item_dto.g.dart';

@freezed
class NoticeItemDTO with _$NoticeItemDTO {
  factory NoticeItemDTO({
    @Default('') @Json<PERSON>ey(name: 'create_time') String createTime,
    @Default('') @JsonKey(name: 'end_time') String endTime,
    @Default('') @<PERSON>son<PERSON>ey(name: 'is_app_top') String isAppTop,
    @Default('') @J<PERSON><PERSON><PERSON>(name: 'notice_action') String noticeAction,
    @Default('') @<PERSON>son<PERSON><PERSON>(name: 'notice_channel_name') String noticeChannelName,
    @Default('') @JsonKey(name: 'notice_class') String noticeClass,
    @Default('') @JsonKey(name: 'notice_description') String noticeDescription,
    @Default('') @JsonKey(name: 'notice_detail_position') String noticeDetailPosition,
    @Default('') @JsonKey(name: 'notice_good') String noticeGood,
    @Default('') @JsonKey(name: 'notice_good_true') String noticeGoodTrue,
    @Default('') @JsonKey(name: 'notice_id') String noticeId,
    @Default('') @JsonKey(name: 'notice_link') String noticeLink,
    @Default('') @JsonKey(name: 'notice_location') String noticeLocation,
    @Default('') @JsonKey(name: 'notice_order') String noticeOrder,
    @Default('') @JsonKey(name: 'notice_pic') String noticePic,
    @Default('') @JsonKey(name: 'notice_read') String noticeRead,
    @Default('') @JsonKey(name: 'notice_read_true') String noticeReadTrue,
    @Default('') @JsonKey(name: 'notice_save') String noticeSave,
    @Default('') @JsonKey(name: 'notice_save_true') String noticeSaveTrue,
    @Default('') @JsonKey(name: 'notice_target') String noticeTarget,
    @Default('') @JsonKey(name: 'notice_template') String noticeTemplate,
    @Default('') @JsonKey(name: 'notice_text') String noticeText,
    @Default('') @JsonKey(name: 'notice_title') String noticeTitle,
    @Default('') @JsonKey(name: 'pic_list') String picList,
    @Default('') @JsonKey(name: 'show_type') String showType,
    @Default('') @JsonKey(name: 'small_pic') String smallPic,
    @Default('') String source,
    @Default('') @JsonKey(name: 'start_time') String startTime,
    @Default('') String tag,
    @Default('') @JsonKey(name: 'test_data_status') String testDataStatus,
    @Default('') @JsonKey(name: 'test_type') String testType,
    @Default('') @JsonKey(name: 'time_interval') String timeInterval,
    @Default('') @JsonKey(name: 'top_end_time') String topEndTime,
    @Default('') @JsonKey(name: 'top_start_time') String topStartTime,
    @Default('') @JsonKey(name: 'total_times') String totalTimes,
    @Default('') @JsonKey(name: 'video_pic') String videoPic,
    @Default('') @JsonKey(name: 'video_url') String videoUrl,
  }) = _NoticeItemDTO;

  NoticeItemDTO._();

  factory NoticeItemDTO.fromJson(Map<String, Object?> json) =>
      _$NoticeItemDTOFromJson(json);
}
