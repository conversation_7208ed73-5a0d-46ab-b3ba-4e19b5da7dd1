import 'package:bruno/bruno.dart';
import 'package:dnurse/app/user/pages/health/sugar_solution/sugar_solution_theme.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../router/routes.dart';
import '../../../../../ui/icons/icon_strings.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'logic.dart';
import 'utils/chart_pdf_util.dart';
import 'widget/sugar_solution_guide.dart';
import 'widget/sugar_solution_report_page.dart';

class SugarSolutionPage extends StatelessWidget {
  const SugarSolutionPage({super.key});

  // Future<void> generatePdf() async {
  //   pw.Font? cjkFont;
  //   try {
  //     const fontAssetPath = 'assets/fonts/NotoSansSC-Regular.ttf';
  //     final fontData = await rootBundle.load(fontAssetPath);
  //     cjkFont = pw.Font.ttf(fontData);
  //     print('Successfully loaded font: $fontAssetPath');
  //   } catch (e) {
  //     print('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
  //     print('pdf字体加载报错: $e');
  //     print('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
  //   }

  //   final pdf = pw.Document(
  //     theme: cjkFont != null
  //         ? pw.ThemeData.withFont(
  //             base: cjkFont,
  //             bold: cjkFont,
  //             italic: cjkFont,
  //           )
  //         : pw.ThemeData.base(),
  //   );

  //   final baseTextStyle =
  //       cjkFont != null ? pw.TextStyle(font: cjkFont) : const pw.TextStyle();
  //   final headerStyle = cjkFont != null
  //       ? pw.TextStyle(
  //           font: cjkFont, fontSize: 18, fontWeight: pw.FontWeight.bold)
  //       : pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold);
  //   final boldTextStyle = cjkFont != null
  //       ? pw.TextStyle(font: cjkFont, fontWeight: pw.FontWeight.bold)
  //       : pw.TextStyle(fontWeight: pw.FontWeight.bold);
  //   // 添加页面并创建表格
  //   pdf.addPage(
  //     pw.Page(
  //       build: (pw.Context context) {
  //         return pw.Center(
  //             child: pw.Column(
  //           children: [
  //             pw.Header(
  //                 level: 1, child: pw.Text('控糖方案数据表', style: headerStyle)),
  //             pw.SizedBox(height: 10),
  //             pw.Container(
  //               width: 400,
  //               child: pw.Table(
  //                 border: pw.TableBorder.all(),
  //                 defaultColumnWidth: const pw.FixedColumnWidth(100),
  //                 children: [
  //                   // 表头行
  //                   pw.TableRow(
  //                     children: [
  //                       pw.Padding(
  //                         padding: const pw.EdgeInsets.all(5),
  //                         child: pw.Text('姓名', style: boldTextStyle),
  //                       ),
  //                       pw.Padding(
  //                         padding: const pw.EdgeInsets.all(5),
  //                         child: pw.Text('年龄', style: boldTextStyle),
  //                       ),
  //                       pw.Padding(
  //                         padding: const pw.EdgeInsets.all(5),
  //                         child: pw.Text('血糖', style: boldTextStyle),
  //                       ),
  //                     ],
  //                   ),
  //                   // 数据行
  //                   pw.TableRow(
  //                     children: [
  //                       pw.Padding(
  //                           padding: const pw.EdgeInsets.all(5),
  //                           child: pw.Text('张三 ', style: baseTextStyle)),
  //                       pw.Padding(
  //                           padding: const pw.EdgeInsets.all(5),
  //                           child: pw.Text('45岁', style: baseTextStyle)),
  //                       pw.Padding(
  //                           padding: const pw.EdgeInsets.all(5),
  //                           child: pw.Text('5.6mmol/L', style: baseTextStyle)),
  //                     ],
  //                   ),
  //                 ],
  //               ),
  //             ),
  //             pw.SizedBox(height: 30),
  //             pw.Header(
  //                 level: 1, child: pw.Text('血糖趋势图--', style: headerStyle)),
  //             pw.SizedBox(height: 10),
  //             pw.Container(
  //               height: 200,
  //               child: pw.Chart(
  //                 grid: pw.CartesianGrid(
  //                   xAxis: pw.FixedAxis([0, 1, 2, 3, 4, 5, 6]),
  //                   yAxis: pw.FixedAxis([0, 20, 40, 60, 80, 100]),
  //                 ),
  //                 datasets: [
  //                   pw.LineDataSet(
  //                     legend:
  //                         '血糖值', // Legend text should now use the themed font
  //                     drawPoints: true,
  //                     isCurved: true,
  //                     data: [
  //                       const pw.PointChartValue(0, 50),
  //                       const pw.PointChartValue(1, 42),
  //                       const pw.PointChartValue(2, 55),
  //                       const pw.PointChartValue(3, 70),
  //                       const pw.PointChartValue(4, 60),
  //                       const pw.PointChartValue(5, 55),
  //                       const pw.PointChartValue(6, 62),
  //                     ],
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ],
  //         ));
  //       },
  //     ),
  //   );

  //   // 根据平台选择正确的路径
  //   Directory? outputDir;
  //   if (Platform.isAndroid || Platform.isIOS) {
  //     // 移动端：使用应用私有目录
  //     outputDir = await getApplicationDocumentsDirectory();
  //   } else {
  //     // 桌面端/开发测试：使用当前工作目录（即项目根目录）
  //     outputDir = Directory.current;
  //   }

  //   final file = File('${outputDir.path}/sugar_solution_report.pdf');

  //   print(file.path);

  //   try {
  //     await file.writeAsBytes(await pdf.save());
  //     print('PDF生成成功');
  //   } catch (e) {
  //     print(e);
  //   }
  // }

  Future<void> exportChartPdf(BuildContext context) async {
    if (!context.mounted) return;

    final file = await ChartPdfUtil.generateChartPdf(
      chartWidget: const SugarSolutionReportPage(), // 使用完整报告页面
      pdfTitle: '控糖方案报告',
      pdfFileName: 'sugar_solution_report.pdf',
      isFullPage: true, // 启用完整页面模式
      contextForShow: context,
    );

    if (!context.mounted) return;

    if (file != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('PDF已生成: ${file.path}')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('PDF生成失败')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SugarSolutionLogic>();

    return Obx(() {
      // 构建主页面内容
      final mainPageContent = Scaffold(
        backgroundColor: AppColor.background1,
        appBar: getAppBar(
            backgroundColor: Colors.white,
            title: "控糖方案",
            context: context,
            elevation: 0,
            actions: [
              InkWell(
                onTap: () async {
                  // await exportChartPdf(context); // 调用生成 PDF 的方法
                  _showDownloadDialog(context);
                },
                child: const Center(
                  child: Text(
                    '方案下载',
                    style: TextStyle(
                      fontSize: 15,
                    ),
                  ),
                ),
              )
            ]),
        // ignore: deprecated_member_use
        body: WillPopScope(
        onWillPop: () async {
          if (await logic.checkChanged()) {
            // ignore: use_build_context_synchronously
            logic.showWarnDialog(context);
            return false;
          }
          return true;
        },
        child: Column(
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              padding: EdgeInsets.only(bottom: 6.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20.w),
                  bottomRight: Radius.circular(20.w),
                ),
                boxShadow: [
                  BoxShadow(
                    color: SugarSolutionTheme.shadowColor.withOpacity(0.06),
                    spreadRadius: 0,
                    blurRadius: 10.w,
                    offset: const Offset(0, 4), // changes position of shadow
                  ),
                ],
              ),
              child: BrnTabBar(
                controller: logic.tabController,
                // mode: BrnTabBarBadgeMode.origin,
                // indicatorWeight: 2,
                indicatorColor: AppColor.primary,
                tabs:
                    logic.pageList.map((e) => BadgeTab(text: e.title)).toList(),
                onTap: (state, index) {
                  logic.onTabClick(context, index);
                },
                themeData: BrnTabBarConfig(
                  indicatorWidth: 20,
                  indicatorHeight: 4,
                  labelStyle: BrnTextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textPrimary,
                    fontWeight: FontWeight.w500,
                    height: 1.1,
                  ),
                  unselectedLabelStyle: BrnTextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textSecondary,
                    fontWeight: FontWeight.w400,
                    height: 1.1,
                  ),
                ),
              ),
            ),
            Expanded(
              child: PageView(
                // physics: const NeverScrollableScrollPhysics(),
                controller: logic.pageController,
                children: logic.pageList.map((e) => e.page()).toList(),
                onPageChanged: (index) {
                  logic.onPageChanged(context, index);
                },
              ),
            ),
            Container(
              padding: EdgeInsets.only(
                left: 30.w,
                right: 30.w,
                top: 16.w,
                bottom: 16.w + MediaQuery.of(context).viewPadding.bottom,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.w),
                  topRight: Radius.circular(20.w),
                ),
                boxShadow: [
                  BoxShadow(
                    color: SugarSolutionTheme.shadowColor.withOpacity(0.08),
                    blurRadius: 10.w,
                    spreadRadius: 0.w,
                    offset: Offset(0, 4.w),
                  )
                ],
              ),
              child: DNUPrimaryButton(
                title: '每日任务',
                onPressed: () async {},
              ),
            )
          ],
        ),
      ),
    );

    // 如果需要显示引导页面，返回覆盖式引导
    if (logic.showGuide.value) {
      return SugarSolutionGuide(
        onComplete: logic.completeGuide,
        backgroundPage: mainPageContent,
      );
    }

    // 否则直接返回主页面
    return mainPageContent;
    });
  }

  void _showDownloadDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            left: 15.w,
            right: 15.w,
            top: 24.w,
            bottom: 24.w + MediaQuery.of(context).padding.bottom,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.w),
              topRight: Radius.circular(20.w),
            ),
          ),
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            _buildDialogRow(
              icon: DNUIconString.wodejiankangziliao,
              title: '健康档案',
              onPressed: () {
                AppRouter.push(Routes.userHealth, parameters: {
                  RouteParams.source: 'sugar_solution',
                });
              },
            ),
            Divider(
              height: 1.w,
              color: AppColor.textDisable,
            ),
            _buildDialogRow(
              icon: DNUIconString.xiazai,
              title: '下载到本地',
              onPressed: () {
                exportChartPdf(context);
              },
            ),
            SizedBox(
              height: 16.w,
            ),
            _buildDialogButton("取消", () {
              AppRouter.back();
            }),
          ]),
        );
      },
    );
  }

  Widget _buildDialogRow({
    required String icon,
    required String title,
    VoidCallback? onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: Ink(
        height: 64.w,
        width: double.infinity,
        child: InkWell(
          splashColor: AppColor.splashColor,
          onTap: () {
            AppRouter.back();
            onPressed?.call();
          },
          child: Center(
            child: Text.rich(
              TextSpan(children: [
                TextSpan(
                  text: icon,
                  style: TextStyle(
                    color: AppColor.textSecondary,
                    fontSize: 20.sp,
                    fontFamily: AppFont.icon,
                  ),
                ),
                TextSpan(text: title),
              ]),
              style: TextStyle(
                fontSize: 18.sp,
                color: AppColor.textPrimary,
                // height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogButton(String text, VoidCallback onPressed) {
    return Material(
      color: Colors.transparent,
      clipBehavior: Clip.antiAlias,
      borderRadius: BorderRadius.circular(24.w),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
        ),
        child: Ink(
          height: 48.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.w),
            color: AppColor.background3,
            // color: Colors.red,
          ),
          child: InkWell(
            splashColor: AppColor.splashColor,
            onTap: onPressed,
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: AppColor.textSecondary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
