import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart' show rootBundle;

/// 工具类：将任意Flutter页面转图片并生成PDF
class ChartPdfUtil {
  /// [chartWidget] 需要截图的Widget
  /// [pdfTitle] PDF主标题
  /// [pdfFileName] 保存PDF的文件名
  /// [fontAssetPath] 字体路径，支持中文（如assets/fonts/NotoSansSC-Regular.ttf）
  /// [isFullPage] 是否为完整页面截图（默认false为图表截图）
  static Future<File?> generateChartPdf({
    required Widget chartWidget,
    required String pdfTitle,
    String pdfFileName = 'chart_export.pdf',
    String fontAssetPath = 'assets/fonts/NotoSansSC-Regular.ttf',
    double imageWidth = 300,
    double imageHeight = 200,
    bool isFullPage = false, // 新增参数
    BuildContext? contextForShow, // 可选：用于弹窗提示
  }) async {
    // 1. 截图
    final boundaryKey = GlobalKey();
    Widget repaint;

    if (isFullPage) {
      // 完整页面截图：不限制高度，让内容自然展开
      final screenSize = MediaQuery.of(contextForShow ?? navigatorKey.currentContext!).size;
      repaint = RepaintBoundary(
        key: boundaryKey,
        child: SizedBox(
          width: screenSize.width,
          child: chartWidget, // 移除高度限制，让内容自然展开
        ),
      );
    } else {
      // 图表截图：使用指定尺寸
      repaint = RepaintBoundary(
        key: boundaryKey,
        child: SizedBox(
          width: imageWidth,
          height: imageHeight,
          child: chartWidget,
        ),
      );
    }

    // 2. 将RepaintBoundary临时插入Overlay进行渲染
    final overlay = OverlayEntry(builder: (_) => Center(child: repaint));
    final navigatorContext = contextForShow ?? navigatorKey.currentContext;
    if (navigatorContext == null) {
      debugPrint('No context for overlay');
      return null;
    }
    Overlay.of(navigatorContext).insert(overlay);

    await Future.delayed(const Duration(milliseconds: 400)); // 确保渲染

    Uint8List? chartImageBytes;
    try {
      RenderRepaintBoundary boundary = boundaryKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        chartImageBytes = byteData.buffer.asUint8List();
      }
    } catch (e) {
      debugPrint('图表截图失败: $e');
    }
    overlay.remove();

    if (chartImageBytes == null) return null;

    // 3. PDF生成
    pw.Font? cjkFont;
    try {
      final fontData = await rootBundle.load(fontAssetPath);
      cjkFont = pw.Font.ttf(fontData);
    } catch (e) {
      debugPrint('pdf字体加载报错: $e');
    }

    final pdf = pw.Document(
      theme: cjkFont != null
          ? pw.ThemeData.withFont(
              base: cjkFont,
              bold: cjkFont,
              italic: cjkFont,
            )
          : pw.ThemeData.base(),
    );

    if (isFullPage) {
      // 完整页面PDF：图片填满整页
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Container(
              width: double.infinity,
              height: double.infinity,
              child: pw.Image(
                pw.MemoryImage(chartImageBytes!),
                fit: pw.BoxFit.contain,
              ),
            );
          },
        ),
      );
    } else {
      // 图表PDF：传统布局
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              children: [
                pw.Text(pdfTitle,
                    style: pw.TextStyle(font: cjkFont, fontSize: 20)),
                pw.SizedBox(height: 20),
                pw.Image(pw.MemoryImage(chartImageBytes!),
                    width: imageWidth, height: imageHeight),
              ],
            );
          },
        ),
      );
    }

    // 根据平台选择合适的目录
    Directory output;
    if (Platform.isAndroid) {
      // Android: 使用Downloads目录，用户更容易找到
      output = Directory('/storage/emulated/0/Download');
      // 如果Downloads目录不存在，回退到Documents目录
      if (!await output.exists()) {
        output = await getApplicationDocumentsDirectory();
      }
    } else {
      // iOS: 使用Documents目录
      output = await getApplicationDocumentsDirectory();
    }
    final file = File('${output.path}/$pdfFileName');
    await file.writeAsBytes(await pdf.save());
    debugPrint('PDF已保存: ${file.path}');
    return file;
  }
}

// 需要全局context时可用
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
