import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart' show rootBundle;

/// 工具类：将任意Flutter页面转图片并生成PDF
class ChartPdfUtil {
  /// [chartWidget] 需要截图的Widget
  /// [pdfTitle] PDF主标题
  /// [pdfFileName] 保存PDF的文件名
  /// [fontAssetPath] 字体路径，支持中文（如assets/fonts/NotoSansSC-Regular.ttf）
  /// [isFullPage] 是否为完整页面截图（默认false为图表截图）
  static Future<File?> generateChartPdf({
    required Widget chartWidget,
    required String pdfTitle,
    String pdfFileName = 'chart_export.pdf',
    String fontAssetPath = 'assets/fonts/NotoSansSC-Regular.ttf',
    double imageWidth = 300,
    double imageHeight = 200,
    bool isFullPage = false, // 新增参数
    BuildContext? contextForShow, // 可选：用于弹窗提示
  }) async {
    debugPrint('🔧 ChartPdfUtil.generateChartPdf 开始');
    debugPrint('📄 PDF标题: $pdfTitle');
    debugPrint('📁 文件名: $pdfFileName');
    debugPrint('📏 完整页面模式: $isFullPage');

    // 1. 截图
    debugPrint('📸 开始准备截图...');
    final boundaryKey = GlobalKey();
    Widget repaint;

    if (isFullPage) {
      // 完整页面截图：不限制高度，让内容自然展开
      final screenSize = MediaQuery.of(contextForShow ?? navigatorKey.currentContext!).size;
      debugPrint('📱 屏幕尺寸: ${screenSize.width} x ${screenSize.height}');
      debugPrint('🖼️ 使用完整页面模式，宽度: ${screenSize.width}，高度: 自适应');

      repaint = RepaintBoundary(
        key: boundaryKey,
        child: SizedBox(
          width: screenSize.width,
          child: chartWidget, // 移除高度限制，让内容自然展开
        ),
      );
    } else {
      // 图表截图：使用指定尺寸
      debugPrint('🖼️ 使用图表模式，尺寸: $imageWidth x $imageHeight');

      repaint = RepaintBoundary(
        key: boundaryKey,
        child: SizedBox(
          width: imageWidth,
          height: imageHeight,
          child: chartWidget,
        ),
      );
    }

    // 2. 将RepaintBoundary临时插入Overlay进行渲染
    debugPrint('🎭 准备插入Overlay进行渲染...');
    final overlay = OverlayEntry(builder: (_) => Center(child: repaint));
    final navigatorContext = contextForShow ?? navigatorKey.currentContext;
    if (navigatorContext == null) {
      debugPrint('❌ 无法获取Navigator Context');
      return null;
    }

    try {
      Overlay.of(navigatorContext).insert(overlay);
      debugPrint('✅ Overlay插入成功');
    } catch (e) {
      debugPrint('❌ Overlay插入失败: $e');
      return null;
    }

    debugPrint('⏳ 等待渲染完成...');
    await Future.delayed(const Duration(milliseconds: 800)); // 增加等待时间

    Uint8List? chartImageBytes;

    // 多次尝试截图，直到渲染完成
    for (int attempt = 1; attempt <= 3; attempt++) {
      try {
        debugPrint('📸 开始第$attempt次截图尝试...');

        final context = boundaryKey.currentContext;
        if (context == null) {
          debugPrint('❌ Context为空，等待下次尝试');
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        }

        final renderObject = context.findRenderObject();
        if (renderObject == null) {
          debugPrint('❌ RenderObject为空，等待下次尝试');
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        }

        if (renderObject is! RenderRepaintBoundary) {
          debugPrint('❌ RenderObject不是RenderRepaintBoundary类型');
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        }

        final boundary = renderObject;

        // 检查是否需要重绘
        if (boundary.debugNeedsPaint) {
          debugPrint('⚠️ Widget还需要重绘，等待...');
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        }

        debugPrint('✅ Widget渲染完成，开始截图');
        final ui.Image image = await boundary.toImage(pixelRatio: 2.0); // 降低像素比减少内存使用
        debugPrint('🖼️ 图片尺寸: ${image.width} x ${image.height}');

        final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        if (byteData != null) {
          chartImageBytes = byteData.buffer.asUint8List();
          debugPrint('✅ 截图成功，大小: ${chartImageBytes.length} bytes');
          break; // 成功截图，退出循环
        } else {
          debugPrint('❌ 截图数据为空');
        }
      } catch (e) {
        debugPrint('❌ 第$attempt次截图失败: $e');
        if (attempt < 3) {
          debugPrint('⏳ 等待${500 * attempt}ms后重试...');
          await Future.delayed(Duration(milliseconds: 500 * attempt));
        }
      }
    }

    try {
      overlay.remove();
      debugPrint('🗑️ Overlay移除成功');
    } catch (e) {
      debugPrint('⚠️ Overlay移除失败: $e');
    }

    if (chartImageBytes == null) {
      debugPrint('❌ 截图数据为空，无法生成PDF');
      return null;
    }

    // 3. PDF生成
    debugPrint('📄 开始生成PDF...');
    pw.Font? cjkFont;
    try {
      debugPrint('🔤 加载中文字体: $fontAssetPath');
      final fontData = await rootBundle.load(fontAssetPath);
      cjkFont = pw.Font.ttf(fontData);
      debugPrint('✅ 字体加载成功');
    } catch (e) {
      debugPrint('⚠️ pdf字体加载报错: $e');
    }

    final pdf = pw.Document(
      theme: cjkFont != null
          ? pw.ThemeData.withFont(
              base: cjkFont,
              bold: cjkFont,
              italic: cjkFont,
            )
          : pw.ThemeData.base(),
    );

    if (isFullPage) {
      // 完整页面PDF：图片填满整页
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Container(
              width: double.infinity,
              height: double.infinity,
              child: pw.Image(
                pw.MemoryImage(chartImageBytes!),
                fit: pw.BoxFit.contain,
              ),
            );
          },
        ),
      );
    } else {
      // 图表PDF：传统布局
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              children: [
                pw.Text(pdfTitle,
                    style: pw.TextStyle(font: cjkFont, fontSize: 20)),
                pw.SizedBox(height: 20),
                pw.Image(pw.MemoryImage(chartImageBytes!),
                    width: imageWidth, height: imageHeight),
              ],
            );
          },
        ),
      );
    }

    // 根据平台选择合适的目录
    debugPrint('💾 开始选择保存目录...');
    Directory output;
    if (Platform.isAndroid) {
      debugPrint('🤖 Android平台，优先使用Downloads目录');
      // Android: 使用Downloads目录，用户更容易找到
      output = Directory('/storage/emulated/0/Download');
      debugPrint('📁 检查Downloads目录是否存在: ${output.path}');

      // 如果Downloads目录不存在，回退到Documents目录
      if (!await output.exists()) {
        debugPrint('⚠️ Downloads目录不存在，回退到Documents目录');
        output = await getApplicationDocumentsDirectory();
      } else {
        debugPrint('✅ Downloads目录存在');
      }
    } else {
      debugPrint('🍎 iOS平台，使用Documents目录');
      // iOS: 使用Documents目录
      output = await getApplicationDocumentsDirectory();
    }

    debugPrint('📂 最终保存目录: ${output.path}');
    final file = File('${output.path}/$pdfFileName');
    debugPrint('📄 完整文件路径: ${file.path}');

    try {
      final pdfBytes = await pdf.save();
      debugPrint('💾 PDF字节大小: ${pdfBytes.length} bytes');

      await file.writeAsBytes(pdfBytes);
      debugPrint('✅ PDF已保存: ${file.path}');

      // 验证文件是否真的存在
      if (await file.exists()) {
        final fileSize = await file.length();
        debugPrint('✅ 文件验证成功，大小: $fileSize bytes');
      } else {
        debugPrint('❌ 文件保存后不存在！');
      }

      return file;
    } catch (e) {
      debugPrint('❌ 文件保存失败: $e');
      return null;
    }
  }
}

// 需要全局context时可用
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
