import 'dart:ui';
import 'package:dnurse/app/user/pages/health/sugar_solution/widget/charts/test_charts.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/gradient_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../logic.dart';

/// 控糖方案报告页面 - 用于PDF导出
class SugarSolutionReportPage extends StatelessWidget {
  const SugarSolutionReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SugarSolutionLogic>();
    
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      color: Colors.white,
      child: Stack(
        children: [
          // 背景效果
          _buildBackgroundEffect(),
          
          // 主要内容
          SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  _buildReportTitle(),
                  SizedBox(height: 30.h),
                  
                  // 用户信息卡片
                  _buildUserInfoCard(logic),
                  SizedBox(height: 20.h),
                  
                  // 控糖方案概览
                  _buildSolutionOverview(logic),
                  SizedBox(height: 20.h),
                  
                  // 血糖趋势图表
                  _buildChartSection(),
                  SizedBox(height: 20.h),
                  
                  // 方案详情
                  _buildSolutionDetails(logic),
                  SizedBox(height: 20.h),
                  
                  // 底部信息
                  _buildFooterInfo(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundEffect() {
    return Container(
      decoration: BoxDecoration(color: const Color(0xFFFAF2D0).withOpacity(0.1)),
      child: Stack(
        children: [
          // 装饰性渐变圆圈 - 右上角
          Positioned(
            right: -77.w,
            top: -116.h,
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
              child: Container(
                width: 336.w,
                height: 336.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFC79C28).withOpacity(.3),
                      const Color(0xFFFFDE24).withOpacity(.3),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
              ),
            ),
          ),
          
          // 装饰性渐变圆圈 - 左下角
          Positioned(
            bottom: -50.h,
            left: -74.w,
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
              child: Container(
                width: 218.w,
                height: 218.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFFAF2D0).withOpacity(.3),
                      const Color(0xFFFAF2D0).withOpacity(.3),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.6, 1.0],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTitle() {
    return Center(
      child: Column(
        children: [
          GradientText(
            gradient: const LinearGradient(
              colors: [Color(0xFF2C2319), Color(0xFFB88D23)],
            ),
            '我的控糖方案报告',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '生成时间：${DateTime.now().toString().substring(0, 16)}',
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColor.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoCard(SugarSolutionLogic logic) {
    return Obx(() {
      final userInfo = logic.state.userInfo.value;
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: const Color(0xFFE0E0E0)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本信息',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.textPrimary,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('姓名', userInfo.realName.isEmpty ? '未设置' : userInfo.realName),
                ),
                Expanded(
                  child: _buildInfoItem('年龄', userInfo.birthYear != null ? '${DateTime.now().year - userInfo.birthYear!.year}岁' : '未设置'),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('身高', userInfo.height > 0 ? '${userInfo.height}cm' : '未设置'),
                ),
                Expanded(
                  child: _buildInfoItem('体重', userInfo.weight > 0 ? '${userInfo.weight}kg' : '未设置'),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColor.textSecondary,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColor.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildSolutionOverview(SugarSolutionLogic logic) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFAF2D0),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE0E0E0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '控糖方案概览',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          _buildOverviewItem('目标血糖', '≤ 6.5 mmol/L'),
          _buildOverviewItem('方案周期', '3个月'),
          _buildOverviewItem('监测频率', '每日2-4次'),
          _buildOverviewItem('预期效果', '血糖稳定在正常范围'),
        ],
      ),
    );
  }

  Widget _buildOverviewItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Container(
            width: 4.w,
            height: 4.w,
            decoration: const BoxDecoration(
              color: Color(0xFF85621D),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            '$label：',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF85621D),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE0E0E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '血糖趋势分析',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.textPrimary,
            ),
          ),
          SizedBox(height: 16.h),
          SizedBox(
            height: 200.h,
            child: const PieChartSample(),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionDetails(SugarSolutionLogic logic) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE0E0E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '方案详情',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          _buildDetailSection('饮食建议', [
            '控制总热量摄入，合理分配三餐',
            '增加膳食纤维摄入，选择低GI食物',
            '定时定量，避免暴饮暴食',
          ]),
          SizedBox(height: 12.h),
          _buildDetailSection('运动建议', [
            '每周至少150分钟中等强度有氧运动',
            '餐后30分钟进行轻度运动',
            '结合力量训练，增强肌肉代谢',
          ]),
          SizedBox(height: 12.h),
          _buildDetailSection('监测建议', [
            '空腹血糖：每日晨起测量',
            '餐后血糖：餐后2小时测量',
            '记录血糖变化趋势',
          ]),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF85621D),
          ),
        ),
        SizedBox(height: 8.h),
        ...items.map((item) => Padding(
          padding: EdgeInsets.only(bottom: 4.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 4.w,
                height: 4.w,
                margin: EdgeInsets.only(top: 6.h),
                decoration: BoxDecoration(
                  color: AppColor.textSecondary,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  item,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColor.textSecondary,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildFooterInfo() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFAF2D0).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Text(
            '温馨提示',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF85621D),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '本方案仅供参考，具体治疗方案请遵医嘱。如有不适，请及时就医。',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColor.textSecondary,
              height: 1.4,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '糖护士 - 您的血糖管理专家',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF85621D),
            ),
          ),
        ],
      ),
    );
  }
}
