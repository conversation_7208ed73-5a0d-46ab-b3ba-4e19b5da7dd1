import 'dart:ui';

import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SugarSolutionGuide extends StatefulWidget {
  final VoidCallback onComplete;
  final Widget backgroundPage; // 背景页面（主页面内容）

  const SugarSolutionGuide({
    super.key,
    required this.onComplete,
    required this.backgroundPage,
  });

  @override
  State<SugarSolutionGuide> createState() => _SugarSolutionGuideState();
}

class _SugarSolutionGuideState extends State<SugarSolutionGuide>
    with TickerProviderStateMixin {
  late AnimationController _loadingController;
  late AnimationController _dragController; // 用于控制滑动进度
  late Animation<double> _progressAnimation;

  bool _showGuide = false;
  final PageController _pageController = PageController();
  
  // 缓存计算结果
  late double _screenWidth;
  late double _screenHeight;
  
  // 防止重复操作的标志
  bool _isAnimating = false;
  bool _isDisposed = false;
  
  // 滑动阻尼和速度曲线优化
  static const double _dragSensitivity = 1.8;
  static const double _snapThreshold = 0.25;

  @override
  void initState() {
    super.initState();

    _loadingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _dragController = AnimationController(
      duration: Duration.zero, // 手势控制，不需要时间
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeInOut,
    ));

    _startLoading();
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _cacheScreenDimensions();
  }
  
  void _cacheScreenDimensions() {
    final size = MediaQuery.of(context).size;
    _screenWidth = size.width;
    _screenHeight = size.height;
  }

  void _startLoading() {
    if (_isDisposed) return;
    
    _loadingController.forward();
    
    // 使用一次性监听器避免重复添加
    _loadingController.addStatusListener(_onLoadingStatusChange);
  }
  
  void _onLoadingStatusChange(AnimationStatus status) {
    if (_isDisposed) return;
    
    if (status == AnimationStatus.completed) {
      _loadingController.removeStatusListener(_onLoadingStatusChange);
      _showGuideContent();
    }
  }

  void _showGuideContent() {
    if (_isDisposed || !mounted) return;
    
    setState(() {
      _showGuide = true;
    });
  }

  /// 动画完成滑动
  void _animateToComplete() {
    if (_isDisposed || !mounted || _isAnimating) return;
    
    _isAnimating = true;
    _dragController.animateTo(
      1.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
    ).then((_) {
      if (_isDisposed || !mounted) return;
      _isAnimating = false;
      widget.onComplete();
    }).catchError((error) {
      if (!_isDisposed && mounted) {
        _isAnimating = false;
      }
    });
  }

  /// 动画重置滑动
  void _animateToReset() {
    if (_isDisposed || !mounted || _isAnimating) return;
    
    _isAnimating = true;
    _dragController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
    ).then((_) {
      if (!_isDisposed && mounted) {
        _isAnimating = false;
      }
    }).catchError((error) {
      if (!_isDisposed && mounted) {
        _isAnimating = false;
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    
    // 移除所有监听器
    _loadingController.removeStatusListener(_onLoadingStatusChange);
    
    // 停止所有动画
    _loadingController.stop();
    _dragController.stop();
    
    // 释放资源
    _loadingController.dispose();
    _dragController.dispose();
    _pageController.dispose();
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // 背景主页面（始终存在）
          if (_showGuide) widget.backgroundPage,

          // 加载页面
          if (!_showGuide) _buildLoadingPage(),

          // 引导卡片覆盖层
          if (_showGuide) _buildOverlayGuide(),
        ],
      ),
    );
  }

  Widget _buildLoadingPage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 进度条区域
          Container(
            padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 60.h),
            child: Column(
              children: [
                // 图片和渐变背景组合
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // 模糊渐变背景
                    Container(
                      width: 191.w,
                      height: 188.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(150.r),
                        gradient: const LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Color.fromRGBO(66, 132, 255, 0.1), // rgba(66, 132, 255, 1) 调整为 0.1 透明度
                            Color.fromRGBO(57, 71, 229, 0.1),  // rgba(57, 71, 229, 1) 调整为 0.1 透明度
                          ],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 40.0, sigmaY: 40.0),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(150.r),
                            color: Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                    // 图片
                    Image.asset(
                      DNUAssets.to.images.task.notebookBg,
                      width: 100.w,
                      fit: BoxFit.fitWidth,
                    ),
                  ],
                ),
                
                SizedBox(height: 54.h),
                
                // 进度条
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return Column(
                      children: [
                        // 进度条背景
                        Container(
                          width: double.infinity,
                          height: 4.h,
                          decoration: BoxDecoration(
                            color: AppColor.background3,
                            borderRadius: BorderRadius.circular(2.h),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: _progressAnimation.value,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [Color(0xFF3947E5), Color(0xFF4284FF)],
                                ),
                                borderRadius: BorderRadius.circular(2.h),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),

                SizedBox(height: 20.h),

                Text(
                  '正在为您生成专属控糖方案...',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建覆盖式引导页面 - 高性能版本
  Widget _buildOverlayGuide() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onHorizontalDragStart: (details) {
        if (_isDisposed || !mounted) return;
        _dragController.stop();
      },
            onHorizontalDragUpdate: (details) {
              if (_isDisposed || !mounted || _isAnimating) return;
              
              final deltaX = details.delta.dx;
              final currentProgress = _dragController.value;
              double newProgress;
              
              if (deltaX < 0) {
                // 左滑：增加进度
                newProgress = (currentProgress - deltaX / _screenWidth * _dragSensitivity).clamp(0.0, 1.0);
              } else {
                // 右滑：减少进度，支持返回
                newProgress = (currentProgress - deltaX / _screenWidth * _dragSensitivity).clamp(0.0, 1.0);
              }
              
              // 直接更新 AnimationController 的值，无需 setState
              _dragController.value = newProgress;
            },
            onHorizontalDragEnd: (details) {
              if (_isDisposed || !mounted || _isAnimating) return;
              
              final velocity = details.primaryVelocity ?? 0;
              final currentProgress = _dragController.value;
              
              if (velocity < -400) {
                // 快速左滑：完成
                _animateToComplete();
              } else if (velocity > 400) {
                // 快速右滑：重置
                _animateToReset();
              } else if (currentProgress > _snapThreshold) {
                // 进度超过阈值：完成
                _animateToComplete();
              } else {
                // 其他情况：重置
                _animateToReset();
              }
            },
            child: AnimatedBuilder(
              animation: _dragController,
              builder: (context, child) {
                if (_isDisposed) return const SizedBox.shrink();
                
                final progress = _dragController.value;
                return Stack(
                  children: [
                    // 增加背景图片DNUAssets.to.images.user.
                    

                    _buildGuideCard(progress),
                    _buildSwipeIndicator(progress),
                  ],
                );
              },
            ),
    );
  }

  /// 构建引导卡片 - 使用 Transform 优化性能
  Widget _buildGuideCard(double progress) {
    return Transform.translate(
      offset: Offset(-progress * _screenWidth, 0),
      child: Container(
        width: _screenWidth,
        height: _screenHeight,
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24.w),
            topRight: Radius.circular(24.w),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1 * (1.0 - progress)),
            ),
          ],
        ),
        child: _buildGuidePageContent(progress),
      ),
    );
  }

  Widget _buildGuidePageContent(double progress) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
            image: AssetImage(DNUAssets.to.images.user.sugarPlanCover),
            fit: BoxFit.cover,
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 顶部返回按钮
            _buildTopBar(),
            // 中间空白区域，让背景图显示
            const Spacer(),
            // 底部操作区域
            _buildBottomSection(progress),
          ],
        ),
      ),
    );
  }

  /// 构建顶部导航栏
  Widget _buildTopBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 44.w,
              height: 44.h,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(22.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8.r,
                    offset: Offset(0, 2.h),
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 20.sp,
                color: AppColor.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildBottomSection(double progress) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          _buildActionButton(progress),
        ],
      ),
    );
  }
  
  Widget _buildActionButton(double progress) {
    return GestureDetector(
      onTap: widget.onComplete,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        child: _buildChargingButton(progress),
      ),
    );
  }

  /// 构建充能按钮效果
  Widget _buildChargingButton(double progress) {
    // 计算充能进度 (0.0 - 1.0) - 简化为更温和的反馈
    double chargeProgress = (progress * 1.5).clamp(0.0, 1.0);
    
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: chargeProgress),
      builder: (context, animatedProgress, child) {
        return Transform.scale(
          scale: 1.0 + animatedProgress * 0.02, // 非常轻微的缩放
          child: _buildMainButton(animatedProgress),
        );
      },
    );
  }

  /// 构建主按钮
  Widget _buildMainButton(double chargeProgress) {
    return Container(
      width: double.infinity,
      height: 48.h,
      decoration: BoxDecoration(
        gradient: AppColor.primaryBackgroundDarkLight10,
        borderRadius: BorderRadius.circular(24.h),
        // 简化：渐进式阴影过渡
        boxShadow: chargeProgress > 0.3 ? [
          BoxShadow(
            color: const Color(0xFF4284FF).withOpacity((chargeProgress - 0.3) * 0.3),
            blurRadius: (chargeProgress - 0.3) * 12.r,
            spreadRadius: (chargeProgress - 0.3) * 1.5.r,
          ),
        ] : null,
      ),
      child: Center(
        child: ShaderMask(
          shaderCallback: (bounds) => AppColor.primaryGradientHorizontal2.createShader(bounds),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildAnimatedArrow(40, 0),
                SizedBox(width: 4.w),
                _buildAnimatedArrow(60, 200),
                SizedBox(width: 8.w),
              // 静态文字
              _buildAnimatedText(chargeProgress),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建带滑动动画的箭头
  Widget _buildAnimatedArrow(double opacity, int delayMs) {
    final image = opacity == 40 ? DNUAssets.to.images.user.leftArrowBlue40 : DNUAssets.to.images.user.leftArrowBlue60;
    
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1500),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        // 添加延迟效果
        double adjustedValue = (animationValue * 1500 - delayMs).clamp(0.0, 1500.0) / 1500.0;
        
        // 创建丝滑的往复滑动效果
        double slideProgress = (adjustedValue * 2) % 2;
        if (slideProgress > 1) slideProgress = 2 - slideProgress; // 往复效果
        
        double slideOffset = slideProgress * 6.w - 3.w; // 左右各3w的滑动范围
        
        return Transform.translate(
          offset: Offset(slideOffset, 0),
          child: Image.asset(
            image,
            width: 16.w,
            fit: BoxFit.fitWidth,
          ),
        );
      },
    );
  }

  /// 构建静态文字 - 简化版本
  Widget _buildAnimatedText(double chargeProgress) {
    return Text(
      '左滑查看详细内容',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: Colors.white,
      ),
    );
  }


  Widget _buildSwipeIndicator(double progress) {
    if (progress <= 0.05) return const SizedBox.shrink();

    return Positioned(
      right: 20.w - (progress * _screenWidth * 0.6),
      top: _screenHeight * 0.5 - 25.w,
      child: Transform.scale(
        scale: 0.8 + (progress * 0.4),
        child: Container(
          width: 50.w,
          height: 50.w,
          decoration: BoxDecoration(
            color: const Color(0xFF4284FF).withOpacity(0.15 + progress * 0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: const Color(0xFF4284FF).withOpacity(0.4 + progress * 0.3),
              width: 1.5.w,
            ),
          ),
          child: Icon(
            Icons.arrow_forward_ios,
            size: (18 + progress * 4).w,
            color: const Color(0xFF4284FF),
          ),
        ),
      ),
    );
  }
}
