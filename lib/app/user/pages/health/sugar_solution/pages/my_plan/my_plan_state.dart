import 'package:get/get.dart';
import 'package:dnurse/app/user/api/dto/sugar_plan/sugar_plan_info_dto.dart';

class MyPlanState {
  // 加载状态
  final isLoading = true.obs;

  // 控糖计划信息
  final sugarPlanInfo = SugarPlanInfoDTO().obs;

  // 展开的分类索引列表 (使用 "stageIndex_categoryIndex" 格式)
  final expandedCategories = <String>[].obs;

  // 是否有控糖方案
  bool get hasSugarPlan => sugarPlanInfo.value.isHasSugarPlan > 0;

  // 获取控糖方案基本信息
  Map<String, dynamic>? get sugarPlan => sugarPlanInfo.value.sugarPlan;

  // 获取测血糖天数
  String get bloodDays => sugarPlanInfo.value.bloodDays;

  // 获取连续测血糖天数
  String get continuousBloodDays => sugarPlanInfo.value.continuousBloodDays;

  // 获取方案开始时间
  String get planStartTime {
    final plan = sugarPlan;
    if (plan == null) return '';
    final startTime = plan['start_time'] as String?;
    if (startTime == null || startTime.isEmpty) return '';

    try {
      final timestamp = int.parse(startTime);
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  // 获取方案结束时间
  String get planEndTime {
    final plan = sugarPlan;
    if (plan == null) return '';
    final endTime = plan['end_time'] as String?;
    if (endTime == null || endTime.isEmpty) return '';

    try {
      final timestamp = int.parse(endTime);
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  // 获取糖化血红蛋白值
  String get saccharifyValue {
    final plan = sugarPlan;
    if (plan == null) return '';
    return plan['saccharify'] as String? ?? '';
  }

  // 获取方案ID
  String get planId {
    final plan = sugarPlan;
    if (plan == null) return '';
    return plan['id'] as String? ?? '';
  }

  // 获取所有阶段数据
  List<Map<String, dynamic>> get allStages {
    final message = sugarPlanInfo.value.sugarPlanMessage;
    if (message == null) return [];

    final stages = <Map<String, dynamic>>[];

    // 添加第一阶段
    final stageOne = message['stage_one'] as Map<String, dynamic>?;
    if (stageOne != null) stages.add(stageOne);

    // 添加第二阶段
    final stageTwo = message['stage_two'] as Map<String, dynamic>?;
    if (stageTwo != null) stages.add(stageTwo);

    // 添加第三阶段
    final stageThree = message['stage_three'] as Map<String, dynamic>?;
    if (stageThree != null) stages.add(stageThree);

    return stages;
  }

  // 切换分类展开状态
  void toggleCategory(int stageIndex, int categoryIndex) {
    final key = '${stageIndex}_$categoryIndex';
    if (expandedCategories.contains(key)) {
      expandedCategories.remove(key);
    } else {
      expandedCategories.add(key);
    }
  }

  // 检查分类是否展开
  bool isCategoryExpanded(int stageIndex, int categoryIndex) {
    final key = '${stageIndex}_$categoryIndex';
    return expandedCategories.contains(key);
  }
}
