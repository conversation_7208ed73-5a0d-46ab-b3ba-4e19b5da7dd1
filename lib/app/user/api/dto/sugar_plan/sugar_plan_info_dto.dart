/// FileName: sugar_plan_info_dto.dart
///
/// @Author: ygc
/// @Date: 2025-01-30 10:00:00
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'sugar_plan_info_dto.freezed.dart';
part 'sugar_plan_info_dto.g.dart';

// 自定义转换器，处理后端返回空数组的情况
class ObjectOrArrayConverter implements JsonConverter<Map<String, dynamic>?, dynamic> {
  const ObjectOrArrayConverter();

  @override
  Map<String, dynamic>? fromJson(dynamic json) {
    if (json == null) return null;
    if (json is Map<String, dynamic>) return json;
    if (json is List && json.isEmpty) return null; // 空数组转为 null
    return null; // 其他情况也转为 null
  }

  @override
  dynamic toJson(Map<String, dynamic>? object) => object;
}

@freezed
class SugarPlanInfoDTO with _$SugarPlanInfoDTO {
  factory SugarPlanInfoDTO({
    @Default(0) int isHasSugarPlan,
    @JsonKey(name: 'blood_days') @Default('0') String bloodDays,
    @JsonKey(name: 'continuous_blood_days') @Default('0') String continuousBloodDays,
    @ObjectOrArrayConverter() Map<String, dynamic>? sugarPlan,
    @ObjectOrArrayConverter() Map<String, dynamic>? sugarPlanMessage,
  }) = _SugarPlanInfoDTO;

  SugarPlanInfoDTO._();

  factory SugarPlanInfoDTO.fromJson(Map<String, Object?> json) =>
      _$SugarPlanInfoDTOFromJson(json);
}
