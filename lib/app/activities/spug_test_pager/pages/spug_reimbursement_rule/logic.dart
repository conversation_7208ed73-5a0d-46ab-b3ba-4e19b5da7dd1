import '../../../../../framework/exception/dnu_error.dart';
import '../../../../../framework/route/base_controller.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import '../../api/dto/spug_rule_dto/spug_rule_dto.dart';
import '../../services/SpugReimbursementService.dart';
import 'state.dart';

class SpugReimbursementRuleLogic extends DNUBaseController {
  final SpugReimbursementRuleState state = SpugReimbursementRuleState();

  @override
  void onResume() {
    super.onResume();

    _loadData();
  }

  void _loadData() async {
    try {
      SpugRuleDTO spugRuleDTO =
          await SpugReimbursementService.to.getRightRule();
      state.pageState.value = LoadingPageState.success;
      state.ruleDTO.value = spugRuleDTO;
    } on DNUError catch (e) {
      state.pageState.value = LoadingPageState.error;
    } catch (e) {
      // Log.e("_getUserPlanInfo: $e");
      state.pageState.value = LoadingPageState.error;
    }
  }
}
