import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../ui/widgets/page/loading_page.dart';
import '../../api/dto/spug_rule_dto/content_item_dto.dart';
import 'logic.dart';

class SpugReimbursementRulePage extends StatelessWidget {
  const SpugReimbursementRulePage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SpugReimbursementRuleLogic>();

    return Obx(() {
      List<ContentItemDTO> items = logic.state.ruleDTO.value?.content ?? [];
      List<Widget> children = items.map((item) => _buildContent(item)).toList();

      return Scaffold(
        backgroundColor: Colors.white,
        // extendBodyBehindAppBar: true,
        appBar: getAppBar(
          title: logic.state.ruleDTO.value?.pageTitle ?? "规则",
          context: context,
          // darkMode: true,
          backgroundColor: Colors.transparent,
          // elevation: 0,
        ),
        body: LoadingPage(
          pageState: logic.state.pageState.value,
          child: SingleChildScrollView(
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ...children,
                  Container(
                    margin: EdgeInsets.only(
                      top: 100.w,
                      bottom: 60.w,
                    ),
                    child: Text(
                      logic.state.ruleDTO.value?.statement ?? "",
                      style: TextStyle(
                        color: const Color(0xff434a54),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildContent(ContentItemDTO item) {
    return Container(
      margin: EdgeInsets.only(
        top: 10.w,
        left: 20.w,
        right: 20.w,
        bottom: 10.w,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.mainText,
            style: TextStyle(
              color: const Color(0xFF202123),
              fontSize: 18.sp,
            ),
          ),
          Text(
            item.subContent.join("\n"),
            style: TextStyle(
              color: const Color(0xFF202123),
              fontSize: 16.sp,
            ),
          ),
        ],
      ),
    );
  }
}
