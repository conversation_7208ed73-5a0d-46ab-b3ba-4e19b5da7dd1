import 'package:dnurse/ui/style/font.dart';
import 'package:dnurse/ui/widgets/avatar.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../resource/dnu_assets.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../../ui/widgets/skeleton/simple_keleton_widget.dart';
import 'logic.dart';

class SpugSharePosterPage extends StatelessWidget {
  const SpugSharePosterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();

    return Scaffold(
      backgroundColor: const Color(0xFF433DB4),
      extendBodyBehindAppBar: true,
      appBar: getAppBar(
        title: "权益海报",
        context: context,
        darkMode: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Obx(() {
        return Stack(
          children: [
            if (logic.state.posterBackground.isNotEmpty)
              SizedBox.expand(
                child: DNUImage.network(
                  logic.state.posterBackground,
                  useCache: true,
                  fit: BoxFit.cover,
                ),
              ),
            SizedBox.expand(
              child: _buildPoster(context),
            ),
            _buildShareMenu(context),
            _buildRefreshButton(context),
            if (logic.state.loading.value) _buildLoading(context),
          ],
        );
      }),
    );
  }

  // WidgetsToImage
  Widget _buildPoster(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();

    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + AppBar().preferredSize.height,
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 20.w),
            WidgetsToImage(
              controller: logic.imageController,
              child: SizedBox(
                width: 320.w,
                child: AspectRatio(
                  aspectRatio: 640 / 976,
                  child: Stack(
                    children: [
                      // SizedBox.expand(
                      //   child: DNUImage.network(url),
                      // ),
                      Container(
                        clipBehavior: Clip.hardEdge,
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(26.w),
                          image: DecorationImage(
                            image: AssetImage(
                              DNUAssets.to.images.activities
                                  .spugReimbursementShareDefaultPoster,
                            ),
                            fit: BoxFit.cover,
                          ),
                        ),
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            if (logic.state.cardBackground.isNotEmpty)
                              SizedBox.expand(
                                child: DNUImage.network(
                                  logic.state.cardBackground.value,
                                  useCache: true,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            Padding(
                              padding: EdgeInsets.only(
                                left: 20.w,
                                right: 20.w,
                                bottom: 35.w,
                                top: 20.w,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildTestDays(context),
                                  const Spacer(),
                                  _buildTestInfo(context),
                                  if (logic.state.isDrawQrcode)
                                    _buildQrcode(context),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRefreshButton(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();

    return Positioned(
      top: 80.w + AppBar().preferredSize.height,
      right: 50.w,
      child: InkWell(
        onTap: () {
          logic.randomCardBackground();
        },
        child: Image.asset(
          DNUAssets.to.images.activities.spugReimbursementPosterRefresh,
          width: 30.w,
          height: 30.w,
        ),
      ),
    );
  }

  Widget _buildTestDays(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();
    return Padding(
      padding: EdgeInsets.only(
        left: 20.w,
        top: 30.w,
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          "坚持测量血糖",
          style: TextStyle(
            fontSize: 20.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Obx(() {
          return Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: logic.state.testDays,
                  style: TextStyle(
                    fontSize: 60.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: AppFont.ranyBold,
                  ),
                ),
                const TextSpan(
                  text: " 天",
                )
              ],
            ),
            style: TextStyle(
              fontSize: 20.sp,
              color: Colors.white,
            ),
          );
        }),
      ]),
    );
  }

  Widget _buildShareMenu(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: 15.w,
          bottom: 15.w + MediaQuery.of(context).padding.bottom,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.w),
            topRight: Radius.circular(20.w),
          ),
        ),
        child: Column(
          children: [
            Text(
              logic.state.sharePanelText1,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xff434a54),
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              logic.state.sharePanelText2,
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xff202123),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 20.w,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 100.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildMenuItem(
                    title: "微信",
                    image: DNUAssets
                        .to.images.activities.spugReimbursementShareWechat,
                    onTap: () {
                      logic.onShare(context, true);
                    },
                  ),
                  SizedBox(width: 20.w),
                  _buildMenuItem(
                    title: "朋友圈",
                    image: DNUAssets
                        .to.images.activities.spugReimbursementShareMoments,
                    onTap: () {
                      logic.onShare(context, false);
                    },
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required String title,
    required String image,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(children: [
        Image.asset(
          image,
          width: 50.w,
          height: 50.w,
          fit: BoxFit.fitWidth,
        ),
        SizedBox(height: 4.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xff434a54),
          ),
        ),
      ]),
    );
  }

  Widget _buildLoading(BuildContext context) {
    return Positioned.fill(
      child: Center(
        child: SimpleSkeleton(
          height: 600.h,
          message: "加载中",
        ),
      ),
    );
  }

  Widget _buildCard({Widget? child}) {
    return Container(
      margin: EdgeInsets.only(
        bottom: 15.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 15.w,
      ),
      decoration: BoxDecoration(
        color: const Color(0xd4ffffff),
        borderRadius: BorderRadius.circular(20.w),
      ),
      child: child,
    );
  }

  Widget _buildTestInfo(BuildContext context) {
    return _buildCard(
      child: Column(
        children: [
          _buildAvatar(context),
          _buildTestNum(context),
          _buildShareInfo(context),
        ],
      ),
    );
  }

  Widget _buildTestNum(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();

    return Padding(
      padding: EdgeInsets.only(top: 10.w),
      child: Text.rich(
          TextSpan(
            children: [
              const TextSpan(
                text: "已测量血糖 ",
              ),
              TextSpan(
                text: logic.state.testNum,
                style: TextStyle(
                  fontSize: 20.sp,
                  color: const Color(0xff1a12a2),
                  fontWeight: FontWeight.bold,
                  fontFamily: AppFont.ranyBold,
                ),
              ),
              const TextSpan(
                text: " 次",
              ),
            ],
          ),
          style: TextStyle(
            fontSize: 13.sp,
            color: const Color(0xff202123),
          )),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();
    return Padding(
      padding: EdgeInsets.only(
        left: 10.w,
        right: 10.w,
      ),
      child: Row(
        children: [
          Avatar(
            url: logic.state.userAvatar,
            size: 30.w,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              logic.state.userName,
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0xff202123),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareItem({
    required String num,
    required String unit,
    required String title,
    IconData? icon,
  }) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(mainAxisSize: MainAxisSize.min, children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0xff202123),
              ),
            ),
            if (icon != null)
              Padding(
                padding: const EdgeInsets.only(left: 3.0),
                child: Icon(
                  icon,
                  size: 18.w,
                  color: Colors.white,
                ),
              ),
          ]),
          Text.rich(
            TextSpan(children: [
              TextSpan(
                text: num,
                style: TextStyle(
                  fontSize: 20.sp,
                  color: const Color(0xff1a12a2),
                  fontFamily: AppFont.ranyBold,
                ),
              ),
              TextSpan(text: unit)
            ]),
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xff202123),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareInfo(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();
    return Padding(
      padding: EdgeInsets.only(
        top: 14.w,
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            _buildShareItem(
              num: logic.state.allGetPaperNum,
              unit: "条",
              title: "已领取试纸",
            ),
            _buildVerticalDivider(padding: EdgeInsets.zero),
            _buildShareItem(
              num: logic.state.allSaveMoney,
              unit: "元",
              title: "节省开销",
            ),
            _buildVerticalDivider(padding: EdgeInsets.zero),
            _buildShareItem(
              num: logic.state.nextBoxNeedTestTimes,
              unit: "次",
              title: "再领一盒还差",
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerticalDivider({EdgeInsetsGeometry? padding}) {
    return Container(
      height: double.infinity,
      // color: Colors.red,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 15.w),
      child: VerticalDivider(
        width: 1.w,
        color: const Color(0xFF92969B),
      ),
    );
  }

  Widget _buildQrcode(BuildContext context) {
    final logic = Get.find<SpugSharePosterLogic>();
    Log.i("_buildQrcode: ${logic.state.isDrawQrcode}");
    if (logic.state.qrcodeUrl.isEmpty) {
      return Container();
    }

    return _buildCard(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 6.w,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 75.w,
                    child: AspectRatio(
                      aspectRatio: 150 / 34,
                      child: Image.asset(
                        DNUAssets.to.images.activities
                            .spugReimbursementPosterLogoBlue,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 8.w,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 6.w,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30.w),
                      color: const Color(0xffff903e),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "长按扫码立即参与活动",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.white,
                            height: 1,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: 4.w,
                          ),
                        ),
                        Image.asset(
                          DNUAssets
                              .to.images.activities.spugReimbursementPosterGoto,
                          width: 13.w,
                          height: 9.w,
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              width: 60.w,
              height: 60.w,
              child: PrettyQrView(
                qrImage: QrImage(
                  QrCode.fromData(
                    data: logic.state.qrcodeUrl,
                    errorCorrectLevel: QrErrorCorrectLevel.H,
                  ),
                ),
                decoration: const PrettyQrDecoration(),
              ),
            )
          ],
        ),
      ),
    );
  }
}
