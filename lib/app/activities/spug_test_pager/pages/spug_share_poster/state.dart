import 'package:get/get.dart';

import '../../api/dto/spug_share_poster_data_dto/spug_share_poster_data_dto.dart';

class SpugSharePosterState {
  SpugSharePosterState() {
    ///Initialize variables
  }

  final posterDTO = Rx<SpugSharePosterDataDTO?>(null);
  final loading = true.obs;
  final cardBackground = "".obs;

  String get testDays => "${posterDTO.value?.testDays ?? 0}";

  String get testNum => "${posterDTO.value?.testNum ?? 0}";

  String get sharePanelText1 => posterDTO.value?.sharePanelText1 ?? "";

  String get sharePanelText2 => posterDTO.value?.sharePanelText2 ?? "";

  String get posterBackground => posterDTO.value?.posterBackground ?? "";

  String get userAvatar => posterDTO.value?.userPic ?? "";

  String get userName => posterDTO.value?.userName ?? "";

  String get allGetPaperNum => posterDTO.value?.allGetPaperNum ?? "*";

  String get allSaveMoney => posterDTO.value?.allSaveMoney ?? "*";

  String get nextBoxNeedTestTimes =>
      posterDTO.value?.nextBoxNeedTestTimes != null
          ? "${posterDTO.value?.nextBoxNeedTestTimes}"
          : "*";

  bool get isDrawQrcode => posterDTO.value?.isDrawQrcode ?? false;
  String get qrcodeUrl => posterDTO.value?.qrcodeUrl ?? "";

}
