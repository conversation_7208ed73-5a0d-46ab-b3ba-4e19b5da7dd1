import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import '../../../../../framework/exception/dnu_error.dart';
import '../../../../../framework/route/base_controller.dart';
import '../../../../../framework/utils/log.dart';
import '../../../../../ui/widgets/dnu_loading.dart';
import '../../../../../ui/widgets/dnu_toast.dart';
import '../../api/dto/spug_share_poster_data_dto/spug_share_poster_data_dto.dart';
import '../../api/dto/spug_share_result_dto/spug_share_result_dto.dart';
import '../../services/SpugReimbursementService.dart';
import 'state.dart';

class SpugSharePosterLogic extends DNUBaseController {
  final SpugSharePosterState state = SpugSharePosterState();
  final imageController = WidgetsToImageController();

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      try {
        SpugSharePosterDataDTO posterDTO =
            Get.arguments as SpugSharePosterDataDTO;
        state.posterDTO.value = posterDTO;
        randomCardBackground();
        state.loading.value = false;
      } catch (e) {
        print(e);
        Log.e("SpugSharePosterLogic init error:", e);
      }
    }
  }

  @override
  void onResume() {
    super.onResume();

    if (state.posterDTO.value == null) {
      _loadData();
    }
  }

  void _loadData() async {
    state.loading.value = true;
    try {
      SpugSharePosterDataDTO posterDTO =
          await SpugReimbursementService.to.getSharePosterData();
      // state.pageState.value = LoadingPageState.success;
      state.posterDTO.value = posterDTO;
      randomCardBackground();
      state.loading.value = false;
    } on DNUError catch (e) {
      // state.pageState.value = LoadingPageState.error;
    } catch (e) {
      Log.e("_getUserPlanInfo: $e");
      // state.pageState.value = LoadingPageState.error;
    }
  }

  void randomCardBackground() {
    List<String> images = state.posterDTO.value?.posterList ?? [];
    if (images.isEmpty) {
      state.cardBackground.value = '';
    }
    final random = Random();
    state.cardBackground.value = images[random.nextInt(images.length)];
  }

  void saveToLocal(BuildContext context) async {
    // showNewRed();
    // return;
    if (context.mounted) {
      DNULoading.show(context);
    }
    final imageData = await imageController.capture();

    if (imageData == null) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show("保存二维码失败", context);
        return;
      }
    }

    final result = await ImageGallerySaver.saveImage(imageData!);
    // Log.i('result----${result}');
    final resultMap = result as Map<Object?, Object?>;
    final success = (resultMap['isSuccess'] ?? false) == true;
    if (context.mounted) {
      DNULoading.dismiss(context);
      DNUToast.show(success ? "保存成功,可在相册中查看" : "保存失败", context);
    }
  }

  void onShare(BuildContext context, bool isWechat) {
    // saveToLocal(context);
    getShareResult(context, isWechat);
  }

  void getShareResult(BuildContext context, bool isWechat) async {
    try {
      SpugShareResultDTO resultDTO =
          await SpugReimbursementService.to.shareResult(isWechat ? "1" : "2");
      if (context.mounted) {
        DNULoading.dismiss(context);
        if (resultDTO.tip.isNotEmpty) {
          DNUToast.show(resultDTO.tip, context);
        }
      }
    } on DNUError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show(e.message, context);
        return;
      }
    } catch (e) {
      Log.e("getShareResult: $e");
      // state.pageState.value = LoadingPageState.error;
    }
  }
}
