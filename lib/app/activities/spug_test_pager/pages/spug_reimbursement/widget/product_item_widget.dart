/// FileName: product_item_widget
///
/// @Author: ygc
/// @Date: 2025/6/26 15:56
/// @Description:
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../api/dto/spug_user_type_dto/production_item_dto.dart';

class ProductItemWidget extends StatelessWidget {
  const ProductItemWidget({
    super.key,
    required this.item,
    this.onBuyTap,
    this.joined = false,
  });

  final ProductionItemDTO item;
  final VoidCallback? onBuyTap;

  /// 立即领取: true, 立即购买: false
  final bool joined;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(children: [
        DNUImage.network(
          item.imageUrl,
          width: 100.w,
          height: 114.w,
          useCache: true,
          fit: BoxFit.cover,
        ),
        SizedBox(
          width: 10.w,
        ),
        Expanded(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.title,
              style: TextStyle(
                color: const Color(0xff202123),
                fontSize: 16.sp,
              ),
            ),
            SizedBox(
              height: 5.w,
            ),
            Text(
              item.desc,
              style: TextStyle(
                color: const Color(0xff434a54),
                fontSize: 12.sp,
              ),
            ),
            SizedBox(
              height: 4.w,
            ),
            _buildPriceRow(context),
            SizedBox(
              height: 4.w,
            ),
            Text(
              item.evaText,
              style: TextStyle(
                color: const Color(0xff434a54),
                fontSize: 12.sp,
              ),
            ),
            SizedBox(
              height: 5.w,
            ),
            _buildButtonRow(context),
          ],
        ))
      ]),
    );
  }

  Widget _buildPriceRow(BuildContext context) {
    double oldPrice = double.tryParse(item.oldPrice) ?? 0;
    double price = double.tryParse(item.price) ?? 0;

    return Row(
      children: [
        if (price > 0)
          Text.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text: "￥ ",
                ),
                TextSpan(
                  text: item.price,
                ),
                if (!joined)
                  TextSpan(
                    text: "起",
                    style: TextStyle(
                      fontSize: 12.sp,
                    ),
                  ),
              ],
            ),
            style: TextStyle(
              color: const Color(0xffe9573f),
              fontSize: 18.sp,
              height: 1.0,
            ),
          ),
        if (oldPrice > 0)
          Text(
            "  ￥ ${item.oldPrice}",
            style: TextStyle(
              color: const Color(0xff92969b),
              fontSize: 12.sp,
            ),
          ),
      ],
    );
  }

  Widget _buildButtonRow(BuildContext context) {
    double discountPrice = double.tryParse(item.discountPrice) ?? 0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (discountPrice > 0 && !joined)
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(
                    text: "券后折扣价: ",
                  ),
                  TextSpan(
                    text: item.discountPrice,
                    style: TextStyle(
                      color: const Color(0xffe9573f),
                      fontSize: 18.sp,
                    ),
                  ),
                ],
              ),
              style: TextStyle(
                color: const Color(0xff656d78),
                fontSize: 12.sp,
                height: 1.0,
              ),
            ),
          ),
        const SizedBox(
          width: 2,
        ),
        DNUPrimaryButton(
          title: joined ? "立即领取" : "立即购买",
          width: 84.w,
          height: 30.w,
          fontSize: 16.sp,
          onPressed: () {
            if (onBuyTap != null) {
              onBuyTap!();
            } else {
              if (item.toUrl.isNotEmpty) {
                AppRouter.push(Routes.browser, arguments: item.toUrl);
              }
            }
          },
        )
      ],
    );
  }
}
