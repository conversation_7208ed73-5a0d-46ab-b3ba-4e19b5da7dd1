import 'package:dnurse/app/activities/spug_test_pager/pages/spug_reimbursement/widget/product_item_widget.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/widgets/page/loading_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../ui/style/font.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../api/dto/spug_user_type_dto/production_item_dto.dart';
import 'logic.dart';

class SpugReimbursementPage extends StatelessWidget {
  const SpugReimbursementPage({super.key});

  @override
  Widget build(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();

    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      appBar: getAppBar(
          title: "试纸报销",
          context: context,
          darkMode: true,
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            _buildAction(),
          ]),
      body: Obx(() {
        return LoadingPage(
          pageState: logic.state.pageState.value,
          child: Stack(
            children: [
              SizedBox.expand(
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF5F6FED),
                        Color(0xff71a4ff),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Image.asset(
                  DNUAssets.to.images.activities.spugReimbursementSky,
                  fit: BoxFit.fitWidth,
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Image.asset(
                  DNUAssets.to.images.activities.spugReimbursementPaper,
                  fit: BoxFit.fitWidth,
                ),
              ),
              _buildContent(context),
              _buildRule(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildAction() {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return Obx(() {
      if (logic.state.planInfo?.isAuth != true) {
        return Container();
      }

      return getAppBarAction(
        title: "查看规则",
        darkMode: true,
        onPressed: () {
          AppRouter.push(Routes.activitySpugPaperReimbursementRule);
        },
      );
    });
  }

  Widget _buildRule() {
    return Positioned(
      top: 260.w,
      right: 0.w,
      child: InkWell(
        onTap: () {
          AppRouter.push(Routes.activitySpugPaperReimbursementRule);
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 8.w,
            vertical: 6.w,
          ),
          decoration: BoxDecoration(
            color: const Color(0xff5041ef),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(100.w),
              bottomLeft: Radius.circular(100.w),
            ),
          ),
          child: Text(
            "查看规则",
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();

    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + AppBar().preferredSize.height,
      ),
      child: SingleChildScrollView(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Obx(() {
            return Column(
              children: [
                _buildHead(context),
                logic.state.planInfo!.isAuth
                    ? _buildHasJoin(context)
                    : _buildNotJoin(context),
                Text(
                  "本活动最终解释权归北京糖护科技有限公司所有",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildHead(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return Column(mainAxisSize: MainAxisSize.min, children: [
      SizedBox(
        width: 256.w,
        child: AspectRatio(
          aspectRatio: 620 / 170,
          child: Image.asset(
            DNUAssets.to.images.activities.spugReimbursementTitle,
            fit: BoxFit.fitWidth,
          ),
        ),
      ),
      Container(
        margin: EdgeInsets.only(
          top: 16.w,
          bottom: 10.w,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
          vertical: 5.w,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30.w),
          color: const Color(0xFF180FA8),
        ),
        child: Obx(() {
          return Text(
            logic.state.isAuth
                ? logic.state.yearRange
                : "3 年 6 0 0 条 试 纸 免 费 用",
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xffe1ecff),
            ),
          );
        }),
      )
    ]);
  }

  Widget _buildCard({
    Widget? child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? marginTop,
  }) {
    return Container(
      margin: margin ??
          EdgeInsets.only(
            left: 20.w,
            right: 20.w,
            top: marginTop ?? 20.w,
          ),
      width: double.infinity,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: const Color(0x882a1795),
        borderRadius: BorderRadius.circular(20.w),
      ),
      child: child,
    );
  }

  Widget _buildVerticalDivider({EdgeInsetsGeometry? padding}) {
    return Container(
      height: double.infinity,
      // color: Colors.red,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 15.w),
      child: VerticalDivider(
        width: 1.w,
        color: Colors.white,
      ),
    );
  }

  Widget _buildStatistic(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return _buildCard(
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
        horizontal: 20.w,
      ),
      child: Column(
        children: [
          IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatisticTestCount(context),
                _buildVerticalDivider(),
                _buildStatisticHistory(context),
              ],
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            logic.state.planInfo?.tipText ?? "",
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticTestCount(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Obx(() {
        int testCount = logic.state.planInfo?.hasTestTimes ?? 0;
        return Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: "$testCount",
                style: TextStyle(
                  fontSize: 24.sp,
                  color: const Color(0xffffce54),
                  fontWeight: FontWeight.w700,
                  fontFamily: AppFont.ranyBold,
                ),
              ),
              const TextSpan(
                text: "次",
              ),
            ],
          ),
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        );
      }),
      Padding(
        padding: EdgeInsets.only(
          top: 10.w,
        ),
        child: Text(
          "已测量血糖",
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
      ),
    ]);
  }

  Widget _buildStatisticHistory(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    final yearGetPaperNum = logic.state.planInfo?.yearGetPaperNum ?? 0;
    final yearRemainderGetPaperNum =
        logic.state.planInfo?.yearRemainderGetPaperNum ?? 0;

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text.rich(
        TextSpan(
          children: [
            const TextSpan(text: "已领取试纸"),
            TextSpan(
              text: "$yearGetPaperNum",
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w700,
                fontFamily: AppFont.ranyBold,
              ),
            ),
            const TextSpan(
              text: "条",
            ),
          ],
        ),
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.white,
        ),
      ),
      Text.rich(
        TextSpan(
          children: [
            const TextSpan(text: "年度剩余可领取"),
            TextSpan(
              text: "$yearRemainderGetPaperNum",
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w700,
                fontFamily: AppFont.ranyBold,
              ),
            ),
            const TextSpan(
              text: "条",
            ),
          ],
        ),
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.white,
        ),
      ),
    ]);
  }

  Widget _buildShopList(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    final isCanGet = logic.state.planInfo?.isCanGet ?? false;
    if (!isCanGet) {
      return Container();
    }

    return _buildCard(
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
        horizontal: 20.w,
      ),
      child: Column(
        children: [
          Text(
            "领取试纸",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardTitle(title, {Widget? other}) {
    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Image.asset(
        DNUAssets.to.images.activities.spugReimbursementIconLeft,
        width: 18.w,
        height: 18.w,
        fit: BoxFit.cover,
      ),
      Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          color: Colors.white,
        ),
      ),
      other ?? const SizedBox(),
      Image.asset(
        DNUAssets.to.images.activities.spugReimbursementIconRight,
        width: 18.w,
        height: 18.w,
        fit: BoxFit.cover,
      ),
    ]);
  }

  Widget _buildShareInfo(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    logic.state.planInfo?.sharePaperData?.shareGiveSaveMoney ?? '0';
    return _buildCard(
      marginTop: 20.w,
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
      ),
      child: Column(
        children: [
          _buildCardTitle("分享权益海报得试纸"),
          Padding(
            padding: EdgeInsets.only(top: 14.w, left: 27.w, right: 27.w),
            child: IntrinsicHeight(
              child: Row(children: [
                _buildShareItem(
                    num: logic.state.planInfo?.sharePaperData
                            ?.shareGiveSaveMoney ??
                        "0",
                    unit: "元",
                    title: "节省了"),
                _buildVerticalDivider(padding: EdgeInsets.zero),
                _buildShareItem(
                    num: logic.state.planInfo?.sharePaperData
                            ?.shareGivePaperNum ??
                        "0",
                    unit: "张试纸",
                    title: "累计获得"),
                _buildVerticalDivider(padding: EdgeInsets.zero),
                _buildShareItem(
                  num:
                      "${logic.state.planInfo?.sharePaperData?.shareValidCount ?? 0}",
                  unit: "次",
                  title: "有效分享",
                  icon: DNUIconFont.guanyutanghushi,
                ),
              ]),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 12.w),
            child: _buildButton("生成权益海报", () {
              logic.onShareTap(context);
            }),
          ),
          Padding(
            padding: EdgeInsets.only(top: 12.w),
            child: Text(
              logic.state.planInfo?.sharePaperData?.shareGetTip ?? "",
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareItem({
    required String num,
    required String unit,
    required String title,
    IconData? icon,
  }) {
    return Expanded(
      child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text.rich(
              TextSpan(children: [
                TextSpan(
                  text: num,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontFamily: AppFont.ranyBold,
                  ),
                ),
                TextSpan(text: unit)
              ]),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
            Row(mainAxisSize: MainAxisSize.min, children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
              if (icon != null)
                Padding(
                  padding: const EdgeInsets.only(left: 3.0),
                  child: Icon(
                    icon,
                    size: 18.w,
                    color: Colors.white,
                  ),
                ),
            ])
          ]),
    );
  }

  Widget _buildButton(title, VoidCallback onTap) {
    return SizedBox(
      width: 188.w,
      child: AspectRatio(
        aspectRatio: 376 / 80,
        child: InkWell(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  DNUAssets.to.images.activities.spugReimbursementButtonBg,
                ),
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF0F2282),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSharePaper(BuildContext context) {
    return _buildCard(
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
      ),
      child: Column(
        children: [
          Text(
            "分享权益海报",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightIcon(String icon) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: 50.w,
          child: Center(
            child: Image.asset(
              icon,
              width: 13.w,
              height: 13.w,
              fit: BoxFit.fitWidth,
            ),
          ),
        )
      ],
    );
  }

  Widget _buildRightDesc(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();

    return _buildCard(
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
        horizontal: 20.w,
      ),
      child: Column(
        children: [
          _buildCardTitle("权益介绍"),
          SizedBox(
            height: 20.w,
          ),
          Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildRightItem(
                  image:
                      DNUAssets.to.images.activities.spugReimbursementSpugIcon,
                  title:
                      logic.state.planInfo?.authDesc[0] ?? "第一步\n购买SPUG",
                ),
                _buildRightIcon(
                    DNUAssets.to.images.activities.spugReimbursementPlus),
                _buildRightItem(
                  image:
                      DNUAssets.to.images.activities.spugReimbursementTestBlood,
                  title:
                      logic.state.planInfo?.authDesc[1] ?? "第二步\n血糖测满45次",
                ),
                _buildRightIcon(
                    DNUAssets.to.images.activities.spugReimbursementEq),
                _buildRightItem(
                  image:
                      DNUAssets.to.images.activities.spugReimbursementTestBox,
                  title:
                      logic.state.planInfo?.authDesc[2] ?? "第三步\n免费兑换试纸",
                ),
              ]),
        ],
      ),
    );
  }

  Widget _buildRightItem({
    required String image,
    required String title,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          image,
          width: 50.w,
          height: 50.w,
          fit: BoxFit.fitWidth,
        ),
        SizedBox(height: 5.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBindOrder(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return _buildCard(
      padding: EdgeInsets.symmetric(
        vertical: 20.w,
        horizontal: 20.w,
      ),
      child: Column(
        children: [
          _buildCardTitle("绑定订单号 立即激活"),
          SizedBox(
            height: 5.w,
          ),
          Text(
            "仅限各电商平台“糖护士”官方店铺已发货订单号有效",
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 17.w),
            decoration: BoxDecoration(
              color: const Color(0xFF5041EF),
              borderRadius: BorderRadius.circular(30.w),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: "请输入订单号",
                hintStyle: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF91B4FF),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 10.w,
                  vertical: 10.w,
                ),
              ),
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0xFF709DFE),
              ),
              onChanged: (value) {
                logic.state.orderNum.value = value;
              },
            ),
          ),
          SizedBox(
            height: 16.w,
          ),
          _buildButton(
            "激活试纸报销",
            () {
              logic.onBindOrder(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProduction(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    String tip = logic.state.userType.value?.typeTipText ?? "";
    return Container(
      margin: EdgeInsets.symmetric(vertical: 20.w),
      child: Column(
        children: [
          if (tip.isNotEmpty)
            Text(
              tip,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
          _buildCardTitle(
            "购买以下商品立即加入",
            other: Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: Image.asset(
                DNUAssets.to.images.activities.spugReimbursementText,
                width: 88.w,
                height: 20.w,
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(
            height: 5.w,
          ),
          _buildCoupon(context),
          _buildProductionList(context),
        ],
      ),
    );
  }

  Widget _buildCouponLeft(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();

    return SizedBox(
      width: 100.w,
      height: double.infinity,
      // color: Colors.red.withOpacity(0.5),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text.rich(
            TextSpan(children: [
              TextSpan(
                text: logic.state.userType.value?.couponConfig?.discount ?? "",
                style: TextStyle(
                  fontSize: 40.sp,
                ),
              ),
              TextSpan(
                text: logic.state.userType.value?.couponConfig?.unit ?? "",
              )
            ]),
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xffd24b58),
              height: 1,
            ),
          ),
          Text(
            "专属商品使用",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xffd24b58),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponRight(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    String expireTime = '';
    if (logic.state.userType.value?.couponConfig?.endTime != null) {
      DateTime endTime = DateTime.fromMillisecondsSinceEpoch(
          logic.state.userType.value!.couponConfig!.endTime * 1000);
      expireTime = '有效期至：${endTime.formatDate('yyyy-MM-dd')}';
    }
    return Expanded(
      child: Container(
        height: double.infinity,
        // color: Colors.blue.withOpacity(0.5),
        padding: EdgeInsets.only(
          left: 10.w,
          // top: 10.w,
        ),
        child: Stack(
          children: [
            Column(
              // mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  logic.state.userType.value?.couponConfig?.title ?? "",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xff434a54),
                  ),
                ),
                Text(
                  logic.state.userType.value?.couponConfig?.desc ?? "",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xff434a54),
                  ),
                ),
                if (expireTime.isNotEmpty)
                  Text(
                    expireTime,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xff92969b),
                    ),
                  ),
              ],
            ),
            Positioned(
              bottom: 8.w,
              right: 10.w,
              child: InkWell(
                onTap: () {
                  logic.onGetCoupon(context);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 1.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.w),
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xffe9573f),
                        Color(0xffff7c1c),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "立即领取",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoupon(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return Obx(() {
      if (!logic.state.showCoupon.value) {
        return Container();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        child: AspectRatio(
          aspectRatio: 756 / 160,
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  DNUAssets.to.images.activities.spugReimbursementCouponBg,
                ),
                fit: BoxFit.cover,
              ),
            ),
            child: Row(
              children: [
                _buildCouponLeft(context),
                _buildCouponRight(context),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildProductionListInner(
    BuildContext context,
    List<ProductionItemDTO> productList, {
    bool joined = true,
  }) {
    if (productList.isEmpty) {
      return Container();
    }

    return Container(
      margin: EdgeInsets.only(
        left: 20.w,
        right: 20.w,
        top: 20.w,
      ),
      child: Column(
        children: productList
            .map(
              (e) => ProductItemWidget(
                item: e,
                joined: joined,
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildProductionList(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    return Obx(() {
      return _buildProductionListInner(
        context,
        logic.state.userType.value?.productList ?? [],
        joined: false,
      );
    });
  }

  Widget _buildNotJoin(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();

    return Column(
      children: [
        _buildRightDesc(context),
        _buildBindOrder(context),
        Obx(() {
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: logic.state.userType.value == null
                ? Container()
                : _buildProduction(context),
          );
        }),
      ],
    );
  }

  Widget _buildHasJoin(BuildContext context) {
    final SpugReimbursementLogic logic = Get.find<SpugReimbursementLogic>();
    bool canGetSharePaper =
        logic.state.planInfo?.sharePaperData?.canGetSharePaper ?? false;
    return Column(
      children: [
        _buildStatistic(context),
        _buildShopList(context),
        _buildProductionListInner(
          context,
          logic.state.planInfo?.paperData ?? [],
          joined: true,
        ),
        _buildShareInfo(context),
        // _buildSharePaper(context),
        if (canGetSharePaper)
          _buildProductionListInner(
            context,
            logic.state.planInfo?.sharePaperData?.paperData ?? [],
            joined: true,
          ),
      ],
    );
  }
}
