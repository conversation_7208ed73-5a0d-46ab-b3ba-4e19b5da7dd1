import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../../../ui/widgets/page/loading_page.dart';
import '../../api/dto/spug_plan_info_dto/spug_plan_info_dto.dart';
import '../../api/dto/spug_user_type_dto/spug_user_type_dto.dart';
import '../../services/SpugReimbursementService.dart';

class SpugReimbursementState {
  SpugReimbursementState() {
    ///Initialize variables
  }

  // final planInfo = Rx<SpugPlanInfoDTO?>(null);
  final userType = Rx<SpugUserTypeDTO?>(null);
  final loading = true.obs;
  final pageState = LoadingPageState.loading.obs;
  final orderNum = ''.obs;
  final showCoupon = true.obs;

  bool get isAuth => planInfo?.isAuth ?? false;

  String get yearRange {
    final start = planInfo?.yearStartTime;
    final end = planInfo?.yearEndTime;
    if (start == null || end == null) {
      return '';
    }

    final startDate = DateTime.fromMillisecondsSinceEpoch(start * 1000);
    final endDate = DateTime.fromMillisecondsSinceEpoch(end * 1000);

    return '当前报销年度: ${startDate.formatDate('yyyy-MM-dd')}-${endDate.formatDate('yyyy-MM-dd')}';
  }

  SpugPlanInfoDTO? get planInfo =>
      SpugReimbursementService.to.currentPlanInfo.value;
}
