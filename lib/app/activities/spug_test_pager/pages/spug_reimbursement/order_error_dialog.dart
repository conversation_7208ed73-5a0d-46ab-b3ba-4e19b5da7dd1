/// FileName: order_error_dialog
///
/// @Author: ygc
/// @Date: 2025/6/26 13:17
/// @Description:
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../config/constants.dart';

class OrderErrorDialog {
  factory OrderErrorDialog() => _instance;

  OrderErrorDialog._internal();

  static const String pageName =
      '/activity/spug/paper-reimbursement/order-error-dialog';
  bool _show = false;
  static final OrderErrorDialog _instance = OrderErrorDialog._internal();

  void showModeDialog({String? error}) {
    if (_show) {
      return;
    }

    _show = true;

    if (Get.currentRoute == pageName) {
      return;
    }

    DNUDialog.showGeneralDialog(
      _buildWidget(error),
      useSafeArea: false,
      // barrierDismissible: false,
      routeSettings: const RouteSettings(
        name: pageName,
      ),
    ).whenComplete(() {
      _show = false;
    });
  }

  static void show({
    required String? error,
  }) {
    _instance.showModeDialog(
      error: error,
    );
  }

  Widget _buildWidget(String? error) {
    const String phone = AppConstants.serviceTelephone;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDialogContainer(
          child: Padding(
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHead(),
                Text(
                  "订单异常，请联系客服",
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: const Color(0xff202123),
                  ),
                ),
                SizedBox(
                  height: 8.w,
                ),
                Text.rich(
                  TextSpan(children: [
                    const TextSpan(
                      text: "客服电话:",
                    ),
                    TextSpan(
                      text: ' $phone',
                      style: const TextStyle(
                        color: AppColor.primary,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          launchUrl(Uri(
                            scheme: 'tel',
                            path: phone,
                          ));
                        },
                    ),
                    const TextSpan(
                      text: "\n客服微信：tangxiaohu101",
                    ),
                  ]),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xff202123),
                  ),
                ),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  "温馨提示：客服工作时间9:00-21:00",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColor.textDesc,
                  ),
                ),
                SizedBox(
                  height: 15.w,
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 30.w,
        ),
        InkWell(
          onTap: () {
            AppRouter.back();
          },
          child: Icon(
            DNUIconFont.svg_fenhuaban_235,
            size: 40.w,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildHead() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 70.w,
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24.w),
                topRight: Radius.circular(24.w),
              ),
              gradient: const LinearGradient(
                colors: [
                  Color(0xff3d8cf2),
                  Color(0xff3548ee),
                ],
              )),
        ),
        Positioned(
          bottom: -4,
          left: 0,
          right: 0,
          child: Image.asset(
            DNUAssets.to.images.activities.spugReimbursementErrorHeadBg,
            fit: BoxFit.fill,
            height: 30.w,
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Center(
            child: SizedBox(
              // color: Colors.red.withOpacity(0.3),
              width: 115.w,
              child: AspectRatio(
                aspectRatio: 270 / 288,
                child: Image.asset(
                  DNUAssets.to.images.activities.spugReimbursementErrorHeadIcon,
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDialogContainer({
    required Widget child,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 280.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: child,
        ),
      ],
    );
  }
}
