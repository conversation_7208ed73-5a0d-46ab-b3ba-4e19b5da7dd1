import 'package:dnurse/app/activities/spug_test_pager/api/dto/spug_plan_info_dto/spug_plan_info_dto.dart';
import 'package:dnurse/framework/exception/dnu_error.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import '../../api/dto/spug_share_poster_data_dto/spug_share_poster_data_dto.dart';
import '../../api/dto/spug_user_type_dto/spug_user_type_dto.dart';
import '../../services/SpugReimbursementService.dart';
import 'order_error_dialog.dart';
import 'state.dart';

class SpugReimbursementLogic extends DNUBaseController {
  final SpugReimbursementState state = SpugReimbursementState();

  @override
  void onInit() {
    super.onInit();

    // _loadData();

    if (SpugReimbursementService.to.currentPlanInfo.value != null) {
      state.pageState.value = LoadingPageState.success;
    }
  }

  @override
  void onResume() {
    super.onResume();

    _loadData();
  }

  void _loadData() async {
    await _getUserPlanInfo();
    await _getUserType();
  }

  Future<void> _getUserPlanInfo() async {
    try {
      SpugPlanInfoDTO spugPlanInfoDTO =
          await SpugReimbursementService.to.getPlanInfo();
      state.pageState.value = LoadingPageState.success;
      SpugReimbursementService.to.currentPlanInfo.value = spugPlanInfoDTO;
    } on DNUError catch (e) {
      state.pageState.value = LoadingPageState.error;
    } catch (e) {
      // Log.e("_getUserPlanInfo: $e");
      state.pageState.value = LoadingPageState.error;
    }
  }

  Future<void> _getUserType() async {
    if (state.planInfo == null || state.planInfo!.isAuth) {
      return;
    }

    try {
      SpugUserTypeDTO spugUserTypeDTO =
          await SpugReimbursementService.to.getUserType();
      state.userType.value = spugUserTypeDTO;
    } on DNUError catch (e) {
    } catch (e) {}
  }

  // getUserType
  void onBindOrder(BuildContext context) async {
    String orderNum = state.orderNum.value.trim();

    if (orderNum.isEmpty) {
      DNUToast.show("请输入订单号", context);
      return;
    }

    bool success = false;
    try {
      DNULoading.show(context);
      await SpugReimbursementService.to.applyBind(orderNum);

      if (context.mounted) {
        DNULoading.dismiss(context);
        // DNUToast.show("绑定成功", context);
      }

      success = true;
    } on DNUError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        // DNUToast.show(e.message, context);
      }
    } on DNUSpugOrderError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        // DNUToast.show(e.message, context);
      }
      OrderErrorDialog.show(error: e.message);
      return;
    } catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        // DNUToast.show("绑定失败", context);
      }
    }

    AppRouter.push(
      Routes.activitySpugPaperReimbursementJoinResult,
      parameters: {
        "success": success.toString(),
        "order": orderNum,
      },
    );
  }

  void onGetCoupon(BuildContext context) async {
    if (state.userType.value?.couponConfig == null) {
      return;
    }

    DNULoading.show(context);
    try {
      String message = await SpugReimbursementService.to.getCoupon();
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show(message, context);
      }
      state.showCoupon.value = false;
    } on DNUError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show(e.message, context);
      }
    } catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show("领取失败", context);
      }
    }
  }

  void onShareTap(BuildContext context) {
    bool shareOpenState =
        state.planInfo?.sharePaperData?.shareOpenState ?? false;
    if (!shareOpenState) {
      DNUToast.show("抱歉活动已经结束", context);
      return;
    }

    _loadPosterData(context);
  }

  void _loadPosterData(BuildContext context) async {
    if (context.mounted) {
      DNULoading.show(context);
    }

    try {
      SpugSharePosterDataDTO posterDTO =
          await SpugReimbursementService.to.getSharePosterData();
      if (context.mounted) {
        DNULoading.dismiss(context);
      }
      if (posterDTO.testNum > 0) {
        AppRouter.push(Routes.activitySpugPaperReimbursementShare,
            arguments: posterDTO);
      }
    } on DNUError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show(e.message, context);
      }
    } catch (e) {
      Log.e("_getUserPlanInfo: $e");

      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show("发生错误，请稍后再试", context);
      }
    }
  }
}
