/// FileName: spug_plan_card_widget
///
/// @Author: ygc
/// @Date: 2025/6/27 19:51
/// @Description:

import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../router/router.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/style/color.dart';
import '../../api/dto/spug_plan_info_dto/spug_plan_info_dto.dart';
import '../../services/SpugReimbursementService.dart';

class SpugPlanCardWidget extends StatelessWidget {
  const SpugPlanCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      SpugPlanInfoDTO? planInfo =
          SpugReimbursementService.to.currentPlanInfo.value;
      if (planInfo == null || !planInfo.actOpen) {
        return Container();
      }

      return Padding(
        padding: EdgeInsets.only(top: 8.w, left: 8.w, right: 8.w),
        child: InkWell(
          onTap: () {
            AppRouter.push(Routes.activitySpugPaperReimbursement);
          },
          child: Container(
            padding: EdgeInsets.only(
                top: 10.w, right: 16.w, bottom: 10.w, left: 14.w),
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0x1a3947e5),
                    Color(0x1a4284ff),
                  ],
                ),
                borderRadius: BorderRadius.circular(32.w)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  // padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    gradient: AppColor.primaryGradientVertical2,
                    borderRadius: BorderRadius.circular(16.w),
                  ),
                  child: DNUImage.network(
                    planInfo.imageUrl,
                    width: 40.w,
                    height: 40.w,
                    fit: BoxFit.fitWidth,
                    useCache: true,
                  ),
                ),
                SizedBox(
                  width: 6.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        planInfo.tipTitle.map((e) => e.text).join(''),
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 15.sp,
                          color: AppColor.textPrimary,
                        ),
                      ),
                      Text(
                        planInfo.tipDesc.map((e) => e.text).join(''),
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 13.sp,
                          color: AppColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!planInfo.isAuth)
                  SizedBox(
                    width: 44.w,
                    height: 44.w,
                    child: Image.asset(
                      DNUAssets.to.images.activities.spugReimbursementGotoVip,
                      fit: BoxFit.cover,
                    ),
                  ),

                if (planInfo.isAuth)
                  Container(
                    margin: EdgeInsets.only(left: 5.w, right: 5.w),
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 4.w,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF6BB42),
                      borderRadius: BorderRadius.circular(8.w),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFFDAA5),
                          offset: Offset(0, 2.w),
                          blurRadius: 15.w,
                        ),
                      ],
                    ),
                    child: Text(
                      '去看看',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                // Icon(
                //   DNUIconFont.zhankai,
                //   size: 16.w,
                //   color: AppColor.textDesc,
                // ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
