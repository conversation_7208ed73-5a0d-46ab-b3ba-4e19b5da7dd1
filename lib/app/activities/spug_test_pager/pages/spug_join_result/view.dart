import 'package:dnurse/ui/widgets/button/dnu_outline_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../ui/icons/icons.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'logic.dart';
import 'state.dart';

class SpugJoinResultPage extends StatelessWidget {
  const SpugJoinResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    final SpugJoinResultLogic logic = Get.put(SpugJoinResultLogic());
    final SpugJoinResultState state = Get.find<SpugJoinResultLogic>().state;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: getAppBar(
          title: '',
          context: context,
          elevation: 0,
          backgroundColor: Colors.transparent),
      body: SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            SizedBox(
              height: 30.w,
            ),
            Icon(
              DNUIconFont.svg_fenhuaban_234,
              size: 140.w,
              color: const Color(0xFF73C04E),
            ),
            SizedBox(
              height: 8.w,
            ),
            Text(
              '加入成功',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColor.textPrimary,
              ),
            ),
            SizedBox(
              height: 8.w,
            ),
            Text(
              '恭喜您，已获得试纸报销资格',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.textDesc,
              ),
            ),
            SizedBox(
              height: 30.w,
            ),
            SizedBox(
              width: 182.w,
              child: DNUOutlineButton(
                title: '返回',
                height: 40.w,
                onPressed: () {
                  Get.back();
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
