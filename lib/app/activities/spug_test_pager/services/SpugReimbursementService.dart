/// FileName: SpugReimbursementService
///
/// @Author: ygc
/// @Date: 2025/6/25 19:14
/// @Description:
import 'package:dnurse/bootstrap/app.dart';
import 'package:get/get.dart';

import '../../../../framework/event/main_has_show.dart';
import '../../../../framework/exception/dnu_error.dart';
import '../../../../framework/network/response.dart';
import '../../../../framework/utils/log.dart';
import '../../../common/service/base_service.dart';
import '../../../user/event/login_changed.dart';
import '../api/dto/spug_plan_info_dto/spug_plan_info_dto.dart';
import '../api/dto/spug_rule_dto/spug_rule_dto.dart';
import '../api/dto/spug_share_poster_data_dto/spug_share_poster_data_dto.dart';
import '../api/dto/spug_share_poster_data_dto/spug_share_poster_result_dto.dart';
import '../api/dto/spug_share_result_dto/spug_share_result_dto.dart';
import '../api/dto/spug_user_type_dto/spug_user_type_dto.dart';
import '../api/spug_reimbursement_api.dart';

class DNUSpugOrderError extends Error {
  DNUSpugOrderError(this.message);

  final String message;

  @override
  String toString() {
    return 'DNUSpugOrderError{message: $message}';
  }
}

class SpugReimbursementService extends BaseService {
  SpugReimbursementService()
      : super(
          needLoginChanged: true,
          needMainHasShow: true,
        );

  final currentPlanInfo = Rx<SpugPlanInfoDTO?>(null);

  static SpugReimbursementService get to => Get.find();

  @override
  Future<void> onLoginChanged(LoginChanged event) async {
    if (AppContext.to.isTemp) {
      currentPlanInfo.value = null;
    } else {
      try {
        currentPlanInfo.value = await loadPlanInfo();
      } catch (e) {
        Log.e(
          "SpugReimbursementService loadPlanInfo error:",
          e,
        );
      }
    }
  }

  @override
  Future<void> onMainHasShow(MainHasShow event) async {
    if (AppContext.to.isTemp) {
      try {
        currentPlanInfo.value = await loadPlanInfo();
      } catch (e) {
        Log.e(
          "SpugReimbursementService onMainHasShow error:",
          e,
        );
      }
    }
  }

  Future<SpugPlanInfoDTO> loadPlanInfo() async {
    final planInfo = await getPlanInfo();
    currentPlanInfo.value = planInfo;
    return planInfo;
  }

  Future<SpugPlanInfoDTO> getPlanInfo() async {
    final JsonResponse response = await SpugReimbursementApi.getPlanInfo();
    Log.i('getPlanInfo response: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }

    if (!response.hasData) {
      throw DNUError("请求数据错误");
    }

    return SpugPlanInfoDTO.fromJson(response.data);
  }

  Future<void> applyBind(String order) async {
    final JsonResponse response = await SpugReimbursementApi.applyBind(order);
    Log.i('applyBind response: $response');
    if (!response.successful) {
      if (response.status == -204) {
        throw DNUSpugOrderError(response.message);
      }
      throw DNUError(response.message);
    }
  }

  Future<SpugUserTypeDTO> getUserType() async {
    final JsonResponse response = await SpugReimbursementApi.getUserType();
    Log.i('getUserType response: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }

    if (!response.hasData) {
      throw DNUError("请求数据错误");
    }

    return SpugUserTypeDTO.fromJson(response.data);
  }

  Future<SpugRuleDTO> getRightRule() async {
    final JsonResponse response = await SpugReimbursementApi.getRightRule();
    Log.i('getRightRule response: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }

    if (!response.hasData) {
      throw DNUError("请求数据错误");
    }

    return SpugRuleDTO.fromJson(response.data);
  }

  Future<String> getCoupon() async {
    final JsonResponse response = await SpugReimbursementApi.getCoupon();
    Log.i('getCoupon response: $response');

    if (!response.successful) {
      throw DNUError(response.message);
    }

    return response.message;
  }

  Future<SpugSharePosterDataDTO> getSharePosterData() async {
    final JsonResponse response =
        await SpugReimbursementApi.getSharePosterData();
    Log.i('getCoupon getSharePosterData: $response');

    if (!response.successful) {
      throw DNUError(response.message);
    }

    SpugSharePosterResultDTO result =
        SpugSharePosterResultDTO.fromJson(response.data);
    if (result.sharePosterData == null) {
      throw DNUError("请求数据错误");
    }
    return result.sharePosterData!;
  }

  Future<SpugShareResultDTO> shareResult(String channel) async {
    final JsonResponse response =
        await SpugReimbursementApi.shareResult(channel);
    Log.i('getCoupon shareResult: $response');

    if (!response.successful) {
      throw DNUError(response.message);
    }

    return SpugShareResultDTO.fromJson(response.data);
    // {tip: 分享成功，试纸+0.5}
    //     {"s":-204,"m":"请再测一次SPUG血糖再分享"}
  }
}
