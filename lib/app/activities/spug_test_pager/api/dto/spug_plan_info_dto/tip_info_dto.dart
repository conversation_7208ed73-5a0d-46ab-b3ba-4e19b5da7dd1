/// FileName: tip_info_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-25 19:57:50
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'tip_info_dto.freezed.dart';
part 'tip_info_dto.g.dart';

@freezed
class TipInfoDTO with _$TipInfoDTO {
  factory TipInfoDTO({
    @Default('') String color,
    @Default('') String text,
  }) = _TipInfoDTO;

  TipInfoDTO._();

  factory TipInfoDTO.fromJson(Map<String, Object?> json) =>
      _$TipInfoDTOFromJson(json);
}
