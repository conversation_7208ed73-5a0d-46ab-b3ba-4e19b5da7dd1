/// FileName: spug_invite_share_data_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 18:06:28
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'spug_invite_share_data_dto.freezed.dart';
part 'spug_invite_share_data_dto.g.dart';

@freezed
class SpugInviteShareDataDTO with _$SpugInviteShareDataDTO {
  factory SpugInviteShareDataDTO({
    @Default('')  @<PERSON>son<PERSON>ey(name: 'share_desc')  String shareDesc,
    @Default('')  @<PERSON><PERSON><PERSON><PERSON>(name: 'share_image')  String shareImage,
    @Default('')  @Json<PERSON>ey(name: 'share_title')  String shareTitle,
    @Default('')  @JsonKey(name: 'share_url')  String shareUrl,
  }) = _SpugInviteShareDataDTO;

  SpugInviteShareDataDTO._();

  factory SpugInviteShareDataDTO.fromJson(Map<String, Object?> json) =>
      _$SpugInviteShareDataDTOFromJson(json);
}
