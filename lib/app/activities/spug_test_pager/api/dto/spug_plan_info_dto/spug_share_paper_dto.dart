/// FileName: spug_share_paper_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 18:20:00
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

import '../spug_user_type_dto/production_item_dto.dart';

part 'spug_share_paper_dto.freezed.dart';
part 'spug_share_paper_dto.g.dart';

@freezed
class SpugSharePaperDTO with _$SpugSharePaperDTO {
  factory SpugSharePaperDTO({
    @Default(0) @<PERSON><PERSON><PERSON><PERSON>(name: 'can_get_box') int canGetBox,
    @Default(false) @Json<PERSON>ey(name: 'can_get_share_paper') bool canGetSharePaper,
    @Default('') @Json<PERSON>ey(name: 'share_get_tip') String shareGetTip,
    @Default("")
    @JsonKey(name: 'share_give_paper_num')
    String shareGivePaperNum,
    @Default('')
    @JsonKey(name: 'share_give_save_money')
    String shareGiveSaveMoney,
    @Default(false) @JsonKey(name: 'share_open_state') bool shareOpenState,
    @Default(0) @JsonKey(name: 'share_valid_count') int shareValidCount,
    @Default(false) @JsonKey(name: 'show_share_data') bool showShareData,
    @Default([]) @JsonKey(name: 'paper_data') List<ProductionItemDTO> paperData,
  }) = _SpugSharePaperDTO;

  SpugSharePaperDTO._();

  factory SpugSharePaperDTO.fromJson(Map<String, Object?> json) =>
      _$SpugSharePaperDTOFromJson(json);
}
