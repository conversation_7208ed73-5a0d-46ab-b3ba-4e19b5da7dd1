/// FileName: spug_plan_info_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-25 19:51:23
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:dnurse/app/activities/spug_test_pager/api/dto/spug_plan_info_dto/tip_info_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../spug_user_type_dto/production_item_dto.dart';
import './spug_invite_share_data_dto.dart';
import './spug_share_paper_dto.dart';

part 'spug_plan_info_dto.freezed.dart';
part 'spug_plan_info_dto.g.dart';

@freezed
class SpugPlanInfoDTO with _$SpugPlanInfoDTO {
  factory SpugPlanInfoDTO({
    @Default(false) @JsonKey(name: 'act_open') bool actOpen,
    @Default([]) @JsonKey(name: 'auth_desc') List<String> authDesc,
    @Default('') @JsonKey(name: 'image_url') String imageUrl,
    @Default(false) @JsonKey(name: 'is_auth') bool isAuth,
    @Default([]) @JsonKey(name: 'tip_desc') List<TipInfoDTO> tipDesc,
    @Default([]) @JsonKey(name: 'tip_title') List<TipInfoDTO> tipTitle,
    @Default(0) @JsonKey(name: 'bind_time') int bindTime,
    @Default(0) @JsonKey(name: 'bind_year') int bindYear,
    @Default(0) @JsonKey(name: 'can_get_box') int canGetBox,
    @Default(0) @JsonKey(name: 'expire_time') int expireTime,
    @Default(0) @JsonKey(name: 'has_test_times') int hasTestTimes,
    @Default(0) @JsonKey(name: 'invite_num') int inviteNum,
    @Default(0) @JsonKey(name: 'invite_paper_quota') int invitePaperQuota,
    @Default('') @JsonKey(name: 'invite_qrcode') String inviteQrcode,
    @JsonKey(name: 'invite_share_data') SpugInviteShareDataDTO? inviteShareData,
    @Default(false) @JsonKey(name: 'is_can_get') bool isCanGet,
    @Default(0) @JsonKey(name: 'need_times') int needTimes,
    @Default(false) @JsonKey(name: 'page_can_share') bool pageCanShare,
    @Default(0) @JsonKey(name: 'per_box_paper_num') int perBoxPaperNum,
    @Default(0) @JsonKey(name: 'per_get_need_times') int perGetNeedTimes,
    @JsonKey(name: 'share_paper_data') SpugSharePaperDTO? sharePaperData,
    @Default(false) @JsonKey(name: 'show_invite_data') bool showInviteData,
    @Default('') @JsonKey(name: 'tip_text') String tipText,
    @Default(0) @JsonKey(name: 'year_end_time') int yearEndTime,
    @Default(0) @JsonKey(name: 'year_get_paper_box') int yearGetPaperBox,
    @Default(0) @JsonKey(name: 'year_get_paper_num') int yearGetPaperNum,
    @Default(0)
    @JsonKey(name: 'year_remainder_get_paper_box')
    int yearRemainderGetPaperBox,
    @Default(0)
    @JsonKey(name: 'year_remainder_get_paper_num')
    int yearRemainderGetPaperNum,
    @Default(0) @JsonKey(name: 'year_start_time') int yearStartTime,
    @Default([]) @JsonKey(name: 'paper_data') List<ProductionItemDTO> paperData,
  }) = _SpugPlanInfoDTO;

  SpugPlanInfoDTO._();

  factory SpugPlanInfoDTO.fromJson(Map<String, Object?> json) =>
      _$SpugPlanInfoDTOFromJson(json);
}
