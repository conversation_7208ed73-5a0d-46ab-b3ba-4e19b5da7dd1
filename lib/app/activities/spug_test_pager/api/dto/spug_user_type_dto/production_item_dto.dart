/// FileName: production_item_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 14:30:18
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'production_item_dto.freezed.dart';
part 'production_item_dto.g.dart';

@freezed
class ProductionItemDTO with _$ProductionItemDTO {
  factory ProductionItemDTO({
    @Default('') String desc,
    @Default('') @JsonKey(name: 'discount_price') String discountPrice,
    @Default('') @Json<PERSON>ey(name: 'eva_text') String evaText,
    @Default('') @<PERSON><PERSON><PERSON>ey(name: 'image_url') String imageUrl,
    @Default('') @JsonKey(name: 'old_price') String oldPrice,
    @Default('') String price,
    @Default('') String title,
    @Default('') @JsonKey(name: 'to_url') String toUrl,
  }) = _ProductionItemDTO;

  ProductionItemDTO._();

  factory ProductionItemDTO.fromJson(Map<String, Object?> json) =>
      _$ProductionItemDTOFromJson(json);
}
