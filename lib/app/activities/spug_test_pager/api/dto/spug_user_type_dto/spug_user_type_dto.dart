/// FileName: spug_user_type_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 14:28:04
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:dnurse/app/activities/spug_test_pager/api/dto/spug_user_type_dto/production_item_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'coupon_config_dto.dart';

part 'spug_user_type_dto.freezed.dart';
part 'spug_user_type_dto.g.dart';

@freezed
class SpugUserTypeDTO with _$SpugUserTypeDTO {
  factory SpugUserTypeDTO({
    @JsonKey(name: 'coupon_config') CouponConfigDTO? couponConfig,
    @Default([])
    @JsonKey(name: 'product_list')
    List<ProductionItemDTO> productList,
    @Default(false) @JsonKey(name: 'show_coupon') bool showCoupon,
    @Default('') @JsonKey(name: 'type_tip_text') String typeTipText,
    @Default(0) @JsonKey(name: 'user_type') int userType,
  }) = _SpugUserTypeDTO;

  SpugUserTypeDTO._();

  factory SpugUserTypeDTO.fromJson(Map<String, Object?> json) =>
      _$SpugUserTypeDTOFromJson(json);
}
