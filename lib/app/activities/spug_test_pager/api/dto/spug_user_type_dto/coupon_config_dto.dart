/// FileName: coupon_config_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 14:29:15
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'coupon_config_dto.freezed.dart';
part 'coupon_config_dto.g.dart';

@freezed
class CouponConfigDTO with _$CouponConfigDTO {
  factory CouponConfigDTO({
    @Default(0) int coupon_type,
    @Default('') String desc,
    @Default('') String discount,
    @Default(0) @JsonKey(name: 'end_time') int endTime,
    @Default('') String title,
    @Default('') String unit,
  }) = _CouponConfigDTO;

  CouponConfigDTO._();

  factory CouponConfigDTO.fromJson(Map<String, Object?> json) =>
      _$CouponConfigDTOFromJson(json);
}
