/// FileName: spug_share_poster_data_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-27 13:05:03
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'spug_share_poster_data_dto.freezed.dart';

part 'spug_share_poster_data_dto.g.dart';

@freezed
class SpugSharePosterDataDTO with _$SpugSharePosterDataDTO {
  factory SpugSharePosterDataDTO({
    @Default('0') @<PERSON>son<PERSON>ey(name: 'all_get_paper_num') String allGetPaperNum,
    @Default('0') @Json<PERSON>ey(name: 'all_save_money') String allSaveMoney,
    @Default('') @Json<PERSON>ey(name: 'community_publish') String communityPublish,
    @Default(false) @JsonKey(name: 'is_draw_qrcode') bool isDrawQrcode,
    @Default(0) @JsonKey(
        name: 'next_box_need_test_times') int nextBoxNeedTestTimes,
    @Default('') @JsonKey(name: 'per_share_give_num') String perShareGiveNum,
    @Default('') @JsonKey(name: 'poster_background') String posterBackground,
    @Default([]) @JsonKey(name: 'poster_list') List<String> posterList,
    @Default('') @JsonKey(name: 'qrcode_url') String qrcodeUrl,
    @Default('') @JsonKey(name: 'share_panel_text_1') String sharePanelText1,
    @Default('') @JsonKey(name: 'share_panel_text_2') String sharePanelText2,
    @Default(0) @JsonKey(name: 'test_days') int testDays,
    @Default(0) @JsonKey(name: 'test_num') int testNum,
    @Default('') @JsonKey(name: 'user_name') String userName,
    @Default('') @JsonKey(name: 'user_pic') String userPic,
  }) = _SpugSharePosterDataDTO;

  SpugSharePosterDataDTO._();

  factory SpugSharePosterDataDTO.fromJson(Map<String, Object?> json) =>
      _$SpugSharePosterDataDTOFromJson(json);
}
