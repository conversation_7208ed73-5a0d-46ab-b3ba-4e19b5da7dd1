/// FileName: content_item_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 20:23:30
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'content_item_dto.freezed.dart';
part 'content_item_dto.g.dart';

@freezed
class ContentItemDTO with _$ContentItemDTO {
  factory ContentItemDTO({
    @Default('')  @JsonKey(name: 'main_text')  String mainText,
    @Default([])  @JsonKey(name: 'sub_content')  List<String> subContent,
  }) = _ContentItemDTO;

  ContentItemDTO._();

  factory ContentItemDTO.fromJson(Map<String, Object?> json) =>
      _$ContentItemDTOFromJson(json);
}
