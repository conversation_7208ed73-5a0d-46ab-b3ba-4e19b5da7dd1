/// FileName: spug_rule_dto.dart
///
/// @Author: ygc
/// @Date: 2025-06-26 20:21:49
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';
import './content_item_dto.dart';

part 'spug_rule_dto.freezed.dart';
part 'spug_rule_dto.g.dart';

@freezed
class SpugRuleDTO with _$SpugRuleDTO {
  factory SpugRuleDTO({
    @Default([])  List<ContentItemDTO> content,
    @Default('')  @<PERSON><PERSON><PERSON><PERSON>(name: 'page_title')  String pageTitle,
    @Default('')  String statement,
  }) = _SpugRuleDTO;

  SpugRuleDTO._();

  factory SpugRuleDTO.fromJson(Map<String, Object?> json) =>
      _$SpugRuleDTOFromJson(json);
}
