/// FileName: spug_reimbursement_api
///
/// @Author: ygc
/// @Date: 2025/6/25 19:12
/// @Description:
import '../../../../framework/network/http_client.dart';
import '../../../../framework/network/response.dart';

class SpugReimbursementApi {
  /// 获取用户参与计划
  static Future<JsonResponse> getPlanInfo() {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/get_plan_info',
      data: {},
      needAuth: true,
    );
  }

  /// 申请绑定
  static Future<JsonResponse> applyBind(String order) {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/apply_bind',
      data: {"order_code": order},
      needAuth: true,
    );
  }

  /// 未加入时获取购买商品
  static Future<JsonResponse> getUserType() {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/get_user_type',
      data: {},
      needAuth: true,
    );
  }

  /// 领取优惠卷
  static Future<JsonResponse> getCoupon() {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/user_get_coupon',
      data: {},
      needAuth: true,
    );
  }

  /// 规则页
  static Future<JsonResponse> getRightRule() {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/get_right_rule',
      data: {},
      needAuth: true,
    );
  }

  /// 取生成海报的数据-分享相关
  static Future<JsonResponse> getSharePosterData() {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/get_share_poster_data',
      data: {},
      needAuth: true,
    );
  }

  /// 记录分享相关的结果
  static Future<JsonResponse> shareResult(String channel) {
    return HttpClient().postSignData(
      '/api/spug_paper_plan/share_result',
      data: {"share_channel": channel},
      needAuth: true,
    );
  }
}
