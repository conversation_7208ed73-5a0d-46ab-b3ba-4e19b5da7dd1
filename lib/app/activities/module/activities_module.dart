/// FileName: activities_module
///
/// @Author: ygc
/// @Date: 2025/6/25 13:56
/// @Description:
import 'package:get/get.dart';

import '../../../config/module_priority.dart';
import '../../../framework/middlewares/router_auth.dart';
import '../../../framework/module/build/annotations.dart';
import '../../../framework/module/module.dart';
import '../../../router/routes.dart';
import '../spug_test_pager/pages/spug_join_result/binding.dart';
import '../spug_test_pager/pages/spug_join_result/view.dart';
import '../spug_test_pager/pages/spug_reimbursement/binding.dart';
import '../spug_test_pager/pages/spug_reimbursement/view.dart';
import '../spug_test_pager/pages/spug_reimbursement_rule/binding.dart';
import '../spug_test_pager/pages/spug_reimbursement_rule/view.dart';
import '../spug_test_pager/pages/spug_share_poster/binding.dart';
import '../spug_test_pager/pages/spug_share_poster/view.dart';
import 'activities_app_lifecycle.dart';

@AppModule(priority: modulePriorityDefault)
class ActivitiesModule extends Module {
  ActivitiesModule({super.priority})
      : super(
          name: 'activities',
          applicationLifecycle: ActivitiesAppLifecycle(),
        );

  @override
  // ignore: long-method
  List<GetPage> getRouters() {
    return [
      // spug 试纸报销
      GetPage(
        name: Routes.activitySpugPaperReimbursement,
        page: () => const SpugReimbursementPage(),
        binding: SpugReimbursementBinding(),
        middlewares: [
          RouteAuthMiddleware(priority: 1),
        ],
      ),
      GetPage(
        name: Routes.activitySpugPaperReimbursementJoinResult,
        page: () => const SpugJoinResultPage(),
        binding: SpugJoinResultBinding(),
      ),
      GetPage(
        name: Routes.activitySpugPaperReimbursementRule,
        page: () => const SpugReimbursementRulePage(),
        binding: SpugReimbursementRuleBinding(),
      ),
       GetPage(
        name: Routes.activitySpugPaperReimbursementShare,
        page: () => const SpugSharePosterPage(),
        binding: SpugSharePosterBinding(),
      ),

    ];
  }
}
