/// FileName: activities_app_lifecycle
///
/// @Author: ygc
/// @Date: 2025/6/25 13:57
/// @Description:
import 'dart:async';

import 'package:get/get.dart';

import '../../../framework/module/application_lifecycle.dart';
import '../spug_test_pager/services/SpugReimbursementService.dart';

class ActivitiesAppLifecycle extends ApplicationLifecycle {
  @override
  FutureOr<void> onAfterAppInit() {
    // Log.i('ActivitiesAppLifecycle onAfterAppInit');

    // Log.i("user token:" + UserContext.to.token);
  }

  @override
  FutureOr<void> onAppInit() async {
    // Log.i('ActivitiesAppLifecycle onAppInit');
    Get.lazyPut<SpugReimbursementService>(() => SpugReimbursementService());
  }

  @override
  FutureOr<void> onAgreedPrivacy() {
    SpugReimbursementService.to;
  }
}
