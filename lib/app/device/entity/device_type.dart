/// FileName: device_type
///
/// @Author: ygc
/// @Date: 2024/9/24 14:28
/// @Description:

const _typeXing = 0;
const _typeHe = 3;
const _typeContour = 6;
const _typeSpug = 10;
const _typeShunGan = 11;
const _typeGuiJi = 12;
const _typeICan = 13;
const _typeWeiTai = 14;

class DeviceType {
  final int id;

  const DeviceType._(this.id);

  static const DeviceType xing = DeviceType._(_typeXing);
  static const DeviceType he = DeviceType._(_typeHe);
  static const DeviceType contour = DeviceType._(_typeContour);
  static const DeviceType spug = DeviceType._(_typeSpug);
  static const DeviceType shunGan = DeviceType._(_typeShunGan);
  static const DeviceType guiJi = DeviceType._(_typeGuiJi);
  static const DeviceType iCan = DeviceType._(_typeICan);
  static const DeviceType weiTai = DeviceType._(_typeWeiTai);

  static DeviceType? getById(int id) {
    switch (id) {
      case _typeXing:
        return xing;
      case _typeHe:
        return he;
      case _typeContour:
        return contour;
      case _typeSpug:
        return spug;
      case _typeShunGan:
        return shunGan;
      case _typeGuiJi:
        return guiJi;
      case _typeICan:
        return iCan;
      case _typeWeiTai:
        return weiTai;
      default:
        return null;
    }
  }

  @override
  String toString() {
    return "DeviceType{id: $id}";
  }
}
