import '../../../resource/dnu_assets.dart';
import 'my_device.dart';

/// FileName: xing_device
///
/// @Author: ygc
/// @Date: 2024/9/27 11:16
/// @Description:

class XingDevice implements MyDevice {
  @override
  doBind() {
    // TODO: implement doBind
    throw UnimplementedError();
  }

  @override
  doUnbind() {
    // TODO: implement doUnbind
    throw UnimplementedError();
  }

  @override
  String getImage() {
    // TODO: implement getImage
    return DNUAssets.to.images.user.devcieXing;
  }

  @override
  bool isBounded() {
    // TODO: implement isBounded
    throw UnimplementedError();
  }

}