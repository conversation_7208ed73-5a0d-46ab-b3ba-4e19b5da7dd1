import 'package:dnurse/app/device/entity/my_device.dart';

import '../../../resource/dnu_assets.dart';

/// FileName: he_device
///
/// @Author: ygc
/// @Date: 2024/9/27 11:14
/// @Description:

class HeDevice implements MyDevice {
  @override
  doBind() {
    // TODO: implement doBind
    throw UnimplementedError();
  }

  @override
  doUnbind() {
    // TODO: implement doUnbind
    throw UnimplementedError();
  }

  @override
  String getImage() {
    return DNUAssets.to.images.user.deviceHe;
  }

  @override
  bool isBounded() {
    // TODO: implement isBounded
    throw UnimplementedError();
  }

}