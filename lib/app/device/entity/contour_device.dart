import 'package:dnurse/app/device/entity/my_device.dart';

import '../../../resource/dnu_assets.dart';

/// FileName: contour_device
///
/// @Author: ygc
/// @Date: 2024/9/27 11:14
/// @Description:

class ContourDevice implements MyDevice {
  final String sn;
  DateTime? syncTime;
  DateTime bindTime;

  ContourDevice(
    this.sn,
    this.bindTime, {
    this.syncTime,
  });

  @override
  String getImage() {
    return DNUAssets.to.images.user.deviceYouAnJi;
  }

  @override
  bool isBounded() {
    return true;
  }

  @override
  doBind() {
    // TODO: implement doBind
    throw UnimplementedError();
  }

  @override
  doUnbind() {
    // TODO: implement doUnbind
    throw UnimplementedError();
  }

  @override
  String toString() {
    return "ContourDevice{sn: $sn, syncTime: $syncTime, bindTime: $bindTime}";
  }
}
