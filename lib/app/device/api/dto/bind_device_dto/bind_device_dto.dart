/// FileName: bind_device_dto.dart
///
/// @Author: ygc
/// @Date: 2024-07-01 18:20:46
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'bind_device_dto.freezed.dart';
part 'bind_device_dto.g.dart';

@freezed
class BindDeviceDTO with _$BindDeviceDTO {
  factory BindDeviceDTO({
    @Default('') String device_has_bind_img,
    @Default('') String device_name,
    @Default('') String device_sn,
    @Default(0) int device_type,
    @Default('') String device_unbind_img,
    @Default(false) bool is_bind,
    @Default(0) int is_hidden,
    @Default('') String open_url,
  }) = _BindDeviceDTO;

  BindDeviceDTO._();

  factory BindDeviceDTO.fromJson(Map<String, Object?> json) =>
      _$BindDeviceDTOFromJson(json);
}
