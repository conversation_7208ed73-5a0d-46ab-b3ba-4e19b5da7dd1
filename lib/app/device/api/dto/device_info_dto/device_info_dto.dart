/// FileName: device_info_dto.dart
///
/// @Author: ygc
/// @Date: 2024-09-27 14:47:16
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'device_info_dto.freezed.dart';
part 'device_info_dto.g.dart';

@freezed
class DeviceInfoDTO with _$DeviceInfoDTO {
  factory DeviceInfoDTO({
    @Default('') String activated_time,
    @Default('') String bind_time,
    @Default('') String dev_sn,
    @Default('') String dev_type,
    @Default('') String id,
    @Default('') String last_act_time,
  }) = _DeviceInfoDTO;

  DeviceInfoDTO._();

  factory DeviceInfoDTO.fromJson(Map<String, Object?> json) =>
      _$DeviceInfoDTOFromJson(json);
}
