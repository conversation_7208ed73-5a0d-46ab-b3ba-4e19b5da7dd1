import '../../../framework/network/http_client.dart';
import '../../../framework/network/response.dart';

class DeviceApi {
  static Future<JsonResponse> getAllDeviceList() {
    return HttpClient().postSignData(
      '/api/device/getAllDeviceList',
      data: {},
      needAuth: true,
    );
  }

  static Future<JsonResponse> bindDevice(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/dnurse_device/user_bind_device',
      data: data,
      needAuth: true,
    );
  }
  static Future<JsonResponse> unbindingDevice(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/dnurse_device/user_unbind_device',
      data: data,
      needAuth: true,
    );
  }

  static Future<JsonResponse> getBoundedDevice(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/dnurse_device/get_device_bind_state',
      data: data,
      needAuth: true,
    );
  }

  static Future<JsonResponse> getSinoIcanAuthUrl() {
    return HttpClient().postSignData(
      '/api/cgm_sino_ican/user_get_sino_auth_url',
      needAuth: true,
    );
  }
}
