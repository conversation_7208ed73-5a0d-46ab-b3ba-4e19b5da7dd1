import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../ui/icons/icons.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'logic.dart';

class GuiJiUseHelpPage extends StatelessWidget {
  GuiJiUseHelpPage({Key? key}) : super(key: key);
  final logic = Get.find<GuiJiUseHelpLogic>();
  final state = Get.find<GuiJiUseHelpLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
          context: context,
          title: "使用帮助",
          backgroundColor: AppColor.backgroundWhite,
          actions: [
            InkWell(
              onTap: () {},
              child: Icon(
                DNUIconFont.gengduo,
                size: 25.w,
              ),
            )
          ]),
      body: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '使用步骤如下：',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 20.w),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 10.w),
              decoration: BoxDecoration(
                color: AppColor.background3,
                borderRadius: BorderRadius.all(Radius.circular(20.w)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: BoxDecoration(
                      color: AppColor.primaryLight,
                      borderRadius: BorderRadius.all(Radius.circular(20.w)),
                    ),
                    child: Center(
                      child: Text(
                        '1',
                        style: TextStyle(
                          fontSize: 16.w,
                          color: AppColor.backgroundWhite,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  SizedBox(
                    width: 280.w,
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '若您还未下载 硅基动感 APP，请',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                            ),
                          ),
                          TextSpan(
                            text: '复制链接',
                            style: TextStyle(
                              color: AppColor.primary,
                              fontSize: 16.sp,
                            ),
                          ),
                          TextSpan(
                            text: '至浏览器下载即可',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(height: 20.w),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 10.w),
              decoration: BoxDecoration(
                color: AppColor.background3,
                borderRadius: BorderRadius.all(Radius.circular(20.w)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: BoxDecoration(
                      color: AppColor.primaryLight,
                      borderRadius: BorderRadius.all(Radius.circular(20.w)),
                    ),
                    child: Center(
                      child: Text(
                        '2',
                        style: TextStyle(
                          fontSize: 16.w,
                          color: AppColor.backgroundWhite,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  SizedBox(
                    width: 280.w,
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '下载动感APP后请参考说明书进行设备绑定 ',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                            ),
                          ),
                          TextSpan(
                            text: '查看说明书',
                            style: TextStyle(
                              color: AppColor.primary,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.w),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 10.w),
              decoration: BoxDecoration(
                color: AppColor.background3,
                borderRadius: BorderRadius.all(Radius.circular(20.w)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: BoxDecoration(
                      color: AppColor.primaryLight,
                      borderRadius: BorderRadius.all(Radius.circular(20.w)),
                    ),
                    child: Center(
                      child: Text(
                        '3',
                        style: TextStyle(
                          fontSize: 16.w,
                          color: AppColor.backgroundWhite,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  SizedBox(
                    width: 280.w,
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '首次激活设备需要等待1小时，数据同步成功后返回',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                            ),
                          ),
                          TextSpan(
                            text: '【糖护士】',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextSpan(
                            text: '应用可查看您的血糖数据及相关服务',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
