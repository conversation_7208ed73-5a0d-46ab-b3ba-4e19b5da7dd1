import 'package:dnurse/router/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../resource/dnu_assets.dart';
import '../../../../router/routes.dart';
import '../../../../ui/icons/icons.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../ui/widgets/button/dnu_primary_button.dart';
import 'logic.dart';

class BindGuiJiPage extends StatelessWidget {
  BindGuiJiPage({Key? key}) : super(key: key);
  final logic = Get.find<BindGuiJiLogic>();
  final state = Get.find<BindGuiJiLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
          context: context,
          title: "硅基动态血糖仪",
          backgroundColor: AppColor.backgroundWhite,
          actions: [
            InkWell(
              onTap: () {
                AppRouter.push(Routes.guiJiUseHelp);
              },
              child: Icon(
                DNUIconFont.gengduo,
                size: 25.w,
              ),
            )
          ]),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: AppColor.primaryBackgroundLight20,
        ),
        child: Stack(
          children: [
            Column(
              children: [
                SizedBox(height: 20.w),
                Image.asset(
                  DNUAssets.to.images.device.bindGuiji,
                ),
                Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.symmetric(vertical: 20.w, horizontal: 20.w),
                  margin:
                      EdgeInsets.symmetric(vertical: 20.w, horizontal: 20.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(20.w)),
                  ),
                  child: Column(
                    children: List.generate(3, (index) {
                      return Container(
                        padding: EdgeInsets.symmetric(vertical: 10.w),
                        decoration: BoxDecoration(
                          border: index == 2
                              ? const Border()
                              : Border(
                                  bottom: BorderSide(
                                    color: AppColor.background3, // 底部边框的颜色
                                    width: 1.w, // 底部边框的宽度
                                  ),
                                ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '序列号:',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColor.textDesc,
                              ),
                            ),
                            Text(
                              'XA77MQ',
                              style: TextStyle(
                                fontSize: 16.sp,
                              ),
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                )
              ],
            ),
            Positioned(
              bottom: 0.w,
              left: 0.w,
              right: 0.w,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.w),
                    topRight: Radius.circular(20.w),
                  ),
                ),
                child: DNUPrimaryButton(
                  onPressed: () {},
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Spacer(),
                      Text(
                        '查看数据',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                        ),
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
