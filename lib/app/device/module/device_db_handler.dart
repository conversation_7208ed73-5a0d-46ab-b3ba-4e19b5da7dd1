/// FileName: device_db_handler
///
/// @Author: ygc
/// @Date: 2024/9/27 15:52
/// @Description:

import 'dart:async';

import 'package:sqflite/sqflite.dart';

import '../../../framework/module/database_handler.dart';
import '../db/migration/device_migration.dart';

class DeviceDBHandler extends DatabaseHandler {
  DeviceDBHandler() : super(1);

  @override
  FutureOr<bool> onDatabaseCreate(Transaction db) async {
    await db.execute(DeviceMigration.getCreateSql());

    return true;
  }

  @override
  FutureOr<bool> onDatabaseUpgrade(
    Transaction db,
    int oldVer,
    int newVer,
  ) async {
    if (oldVer < 1) {
      await db.execute(DeviceMigration.getCreateSql());
    }

    return true;
  }
}
