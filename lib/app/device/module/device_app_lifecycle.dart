/// FileName: device_app_lifecycle
///
/// @Author: ygc
/// @Date: 2024/9/26 17:21
/// @Description:
import 'dart:async';

import 'package:get/get.dart';

import '../../../framework/module/application_lifecycle.dart';
import '../../../framework/utils/log.dart';
import '../service/ble_device_service.dart';
import '../service/device_service.dart';

class DeviceAppLifecycle extends ApplicationLifecycle {
  @override
  FutureOr<void> onAfterAppInit() {
    Log.i('DeviceAppLifecycle onAfterAppInit');

    // Log.i("user token:" + UserContext.to.token);
  }

  @override
  FutureOr<void> onAppInit() async {
    Log.i('DeviceAppLifecycle onAppInit');
    Get.lazyPut<BleDeviceService>(() => BleDeviceService());
    Get.lazyPut<DeviceService>(() => DeviceService());
  }

  @override
  FutureOr<void> onAgreedPrivacy() async {
    Log.i('DeviceAppLifecycle onAgreedPrivacy');

    BleDeviceService.to.setWorkMode(BleWorkMode.sync);

  }
}
