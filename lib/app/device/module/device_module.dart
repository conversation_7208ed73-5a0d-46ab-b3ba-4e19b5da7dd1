/// FileName: device_module.dart
///
/// @Date: 2024-07-01 18:40:40
/// @Description:
///
import 'package:dnurse/config/module_priority.dart';
import 'package:dnurse/framework/module/build/annotations.dart';
import 'package:dnurse/framework/module/module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

import '../../../router/routes.dart';
import '../pages/bind_gui_ji/binding.dart';
import '../pages/bind_gui_ji/gui_ji_use_help/binding.dart';
import '../pages/bind_gui_ji/gui_ji_use_help/view.dart';
import '../pages/bind_gui_ji/view.dart';
import '../pages/bind_san_nuo/binding.dart';
import '../pages/bind_san_nuo/san_nuo_use_help/binding.dart';
import '../pages/bind_san_nuo/san_nuo_use_help/view.dart';
import '../pages/bind_san_nuo/view.dart';
import '../pages/bind_wei_tai/binding.dart';
import '../pages/bind_wei_tai/view.dart';
import '../pages/ble_diagnose/binding.dart';
import '../pages/ble_diagnose/view.dart';
import '../pages/bounded_device_detail/gui_ji/binding.dart';
import '../pages/bounded_device_detail/gui_ji/view.dart';
import '../pages/bounded_device_detail/ya_bei/binding.dart';
import '../pages/bounded_device_detail/ya_bei/view.dart';
import '../pages/contour_info/binding.dart';
import '../pages/contour_info/view.dart';
import '../pages/device_detail/binding.dart';
import '../pages/device_detail/view.dart';
import '../pages/device_link/binding.dart';
import '../pages/device_link/view.dart';
import '../pages/device_list/binding.dart';
import '../pages/device_list/view.dart';
import '../pages/user_guide/dynamic_guide/binding.dart';
import '../pages/user_guide/dynamic_guide/view.dart';
import '../pages/user_guide/dynamic_sensor/binding.dart';
import '../pages/user_guide/dynamic_sensor/view.dart';
import '../pages/user_guide/sensor_guide/binding.dart';
import '../pages/user_guide/sensor_guide/scan_sensor_guide/binding.dart';
import '../pages/user_guide/sensor_guide/scan_sensor_guide/view.dart';
import '../pages/user_guide/sensor_guide/view.dart';
import '../pages/user_guide/spug_guide/binding.dart';
import '../pages/user_guide/spug_guide/view.dart';
import 'device_app_lifecycle.dart';
import 'device_db_handler.dart';

@AppModule(priority: modulePriorityDefault)
class DeviceModule extends Module {
  DeviceModule({int priority = 0})
      : super(
          name: "device",
          priority: priority,
          applicationLifecycle: DeviceAppLifecycle(),
          databaseHandler: DeviceDBHandler(),
        );

  @override
  List<GetPage> getRouters() {
    return [
      GetPage(
        name: Routes.deviceList,
        page: () => const DeviceListPage(),
        binding: DeviceListBinding(),
        // transition: Transition.cupertino,
      ),
      GetPage(
        name: Routes.deviceDetail,
        page: () => DeviceDetailPage(),
        binding: DeviceDetailBinding(),
      ),
      GetPage(
        name: Routes.deviceLink,
        page: () => const DeviceLinkPage(),
        binding: DeviceLinkBinding(),
      ),
      GetPage(
        name: Routes.contourInfo,
        page: () => const ContourInfoPage(),
        binding: ContourInfoBinding(),
      ),
      GetPage(
        name: Routes.bleDiagnose,
        page: () => const BleDiagnosePage(),
        binding: BleDiagnoseBinding(),
      ),
      GetPage(
        name: Routes.spugGuide,
        page: () => const SpugGuidePage(),
        binding: SpugGuideBinding(),
        // transition: Transition.cupertino,
      ),
      GetPage(
        name: Routes.sensorGuide,
        page: () => const SensorGuidePage(),
        binding: SensorGuideBinding(),
      ),
      GetPage(
        name: Routes.dynamicSensor,
        page: () => const DynamicSensorPage(),
        binding: DynamicSensorBinding(),
      ),
      GetPage(
        name: Routes.dynamicGuide,
        page: () => const DynamicGuidePage(),
        binding: DynamicGuideBinding(),
      ),
      GetPage(
        name: Routes.guiJi,
        page: () => const GuiJiPage(),
        binding: GuiJiBinding(),
      ),
      GetPage(
        name: Routes.yaBei,
        page: () => const YaBeiPage(),
        binding: YaBeiBinding(),
      ),
      GetPage(
        name: Routes.scanSensorGuide,
        page: () => const ScanSensorGuidePage(),
        binding: ScanSensorGuideBinding(),
      ),
      GetPage(
        name: Routes.bindWeiTai,
        page: () => BindWeiTaiPage(),
        binding: BindWeiTaiBinding(),
      ),
      GetPage(
        name: Routes.bindGuiJi,
        page: () => BindGuiJiPage(),
        binding: BindGuiJiBinding(),
      ),
      GetPage(
        name: Routes.guiJiUseHelp,
        page: () => GuiJiUseHelpPage(),
        binding: GuiJiUseHelpBinding(),
      ),
      GetPage(
        name: Routes.bindSanNuo,
        page: () => BindSanNuoPage(),
        binding: BindSanNuoBinding(),
      ),
      GetPage(
        name: Routes.sanNuoUseHelp,
        page: () => SanNuoUseHelpPage(),
        binding: SanNuoUseHelpBinding(),
      ),
    ];
  }
}
