import 'package:dnurse/app/device/db/model/device/device_model.dart';
import 'package:dnurse/app/device/entity/contour_device.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../api/dto/device_info_dto/device_info_dto.dart';
import '../entity/device_type.dart';

/// FileName: device_convertor
///
/// @Author: ygc
/// @Date: 2024/9/27 14:56
/// @Description:

class DeviceConvertor {
  static ContourDevice contourFromDTO(DeviceInfoDTO dto) {
    int bindTime = int.tryParse(dto.bind_time) ?? 0;
    if (bindTime == 0) {
      bindTime = DateTime.now().secondsSinceEpoch;
    }

    return ContourDevice(
      dto.dev_sn,
      DateTime.fromMillisecondsSinceEpoch(bindTime * 1000),
    );
  }

  static ContourDevice contourFromModel(DeviceModel model) {
    DateTime bindTime =
        DateTime.fromMillisecondsSinceEpoch(model.bindTime * 1000);
    DateTime? syncTime;
    if (model.syncTime > 0) {
      syncTime = DateTime.fromMillisecondsSinceEpoch(model.syncTime * 1000);
    }

    return ContourDevice(
      model.sn,
      bindTime,
      syncTime: syncTime,
    );
  }

  static DeviceModel deviceModelFromContour(ContourDevice device, String uid) {
    return DeviceModel(
      uid: uid,
      sn: device.sn,
      type: DeviceType.contour.id,
      bindTime: device.bindTime.secondsSinceEpoch,
      syncTime: device.syncTime?.secondsSinceEpoch ?? 0,
    );
  }
}
