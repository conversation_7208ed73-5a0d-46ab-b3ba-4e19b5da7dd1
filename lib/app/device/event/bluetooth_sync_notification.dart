/// FileName: bluetooth_sync_notification
///
/// @Author: ygc
/// @Date: 2024/10/8 13:11
/// @Description:

class BluetoothSyncNotification {
  final bool locationPermissionGranted;
  final bool bluetoothPermissionGranted;
  final bool bluetoothEnabled;

  BluetoothSyncNotification({
    this.locationPermissionGranted = false,
    this.bluetoothPermissionGranted = false,
    this.bluetoothEnabled = false,
  });

  @override
  String toString() {
    return "BluetoothSyncNotification{locationPermissionGranted: $locationPermissionGranted, bluetoothPermissionGranted: $bluetoothPermissionGranted, bluetoothEnabled: $bluetoothEnabled}";
  }
}
