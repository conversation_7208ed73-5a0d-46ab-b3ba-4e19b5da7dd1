/// FileName: contour_sync_finished
///
/// @Author: ygc
/// @Date: 2024/9/30 15:10
/// @Description:
///
import 'package:dnurse/app/device/entity/contour_device.dart';

import '../../data/entity/device_sync_info.dart';

class ContourSyncFinished {
  ContourDevice device;
  List<DeviceSyncInfo> syncInfoList;

  ContourSyncFinished({
    required this.device,
    required this.syncInfoList,
  });

  @override
  String toString() {
    return 'ContourSyncFinished{device: $device, syncInfoList: $syncInfoList}';
  }
}
