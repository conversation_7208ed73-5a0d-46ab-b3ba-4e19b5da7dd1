/// FileName: device_migration
///
/// @Author: ygc
/// @Date: 2024/9/27 15:48
/// @Description:
import '../../../../../framework/database/model/migration.dart';
import '../model/device/device_model.dart';

class DeviceMigration extends Migration {
  DeviceMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $deviceTable (
          ${Migration.getCommSql()}
          ${DeviceColumns.uid} TEXT,
          ${DeviceColumns.sn} TEXT,
          ${DeviceColumns.type} INTEGER,
          ${DeviceColumns.bindTime} INTEGER,
          ${DeviceColumns.syncTime} INTEGER,
          ${DeviceColumns.extraInfo} TEXT,
          ${DeviceColumns.config} TEXT
      );
    """;
  }
}
