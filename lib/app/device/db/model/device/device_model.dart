/// FileName: device_model
///
/// @Author: ygc
/// @Date: 2024/9/27 11:39
/// @Description:
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'device_model.freezed.dart';
part 'device_model.g.dart';

const deviceTable = 'device';

class DeviceColumns extends DNUColumns {
  static const String uid = "uid";
  static const String sn = "sn";
  static const String type = "type";
  static const String syncTime = "syncTime";
  static const String bindTime = "bindTime";
  static const String extraInfo = "extraInfo";
  static const String config = "config";
}

@unfreezed
class DeviceModel with _$DeviceModel {
  DeviceModel._();

  factory DeviceModel({
    @Default(0) int id,
    required String uid,
    @Default("") String sn,
    required int type,
    required int syncTime,
    required int bindTime,
    @Default("") String extraInfo,
    @Default("") String config,
  }) = _DeviceModel;

  factory DeviceModel.fromJson(Map<String, Object?> json) =>
      _$DeviceModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }
}
