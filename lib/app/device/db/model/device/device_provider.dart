/// FileName: device_provider
///
/// @Author: ygc
/// @Date: 2024/9/27 11:49
/// @Description:

import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import '../../../../../framework/utils/log.dart';
import 'device_model.dart';

class DeviceProvider extends Provider {
  DeviceProvider._internal() : super(deviceTable);
  static final DeviceProvider _instance = DeviceProvider._internal();

  static DeviceProvider get instance {
    return _instance;
  }

  /// 插入一条数据
  Future<DeviceModel> insert(DeviceModel model) async {
    int id = await insertMap(model.toDB());
    model.id = id;

    return model;
  }

  Future<int> updateDevice(DeviceModel model) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      model.id,
    ];

    Log.i("updateDevice: $model");

    return await updateMap(model.toDB(), where: where, whereArgs: whereArgs);
  }

  Future<int> deleteDevice(DeviceModel model) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      model.id,
    ];

    return await delete(where: where, whereArgs: whereArgs);
  }

  Future<int> deleteDeviceByType(String uid, {List<int>? typeList}) async {
    String where = "${DeviceColumns.uid} = ?";
    List<Object?> whereArgs = [
      uid,
    ];

    if(typeList != null) {
      where = "$where and ${DeviceColumns.type} in (${typeList.join(",")})";
    }

    Log.i("deleteDeviceByType: $where, $whereArgs");

    return await delete(where: where, whereArgs: whereArgs);
  }

  Future<List<DeviceModel>?> getDevice(String uid, {List<int>? typeList}) async {
    String where = "${DeviceColumns.uid} = ?";
    List<Object?> whereArgs = [
      uid,
    ];

    if(typeList != null) {
      where = "$where and ${DeviceColumns.type} in (${typeList.join(",")})";
    }

    Log.i("getDevice where: $where, whereArgs: $whereArgs");
    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isEmpty) {
      return null;
    }

    return result.map((e) => DeviceModel.fromJson(e)).toList();
  }
}
