/// FileName: data_db_handler
///
/// @Author: ygc
/// @Date: 2024/8/6 18:15
/// @Description:

import 'dart:async';

import 'package:dnurse/app/data/db/migration/device_sync_migration.dart';
import 'package:sqflite/sqflite.dart';

import '../../../framework/module/database_handler.dart';
import '../db/migration/cgm/cgm_data_migration.dart';
import '../db/migration/data_log_migration.dart';
import '../db/migration/drug_data_migration.dart';
import '../db/migration/food_data_migration.dart';
import '../db/migration/glucose/bs_ua_migration.dart';
import '../db/migration/glucose/data_sync_time_interval_migration.dart';
import '../db/migration/glucose/target/target_migration.dart';
import '../db/migration/reminder/monitor_plan_migration.dart';
import '../db/migration/sport_data_migration.dart';
import '../db/model/glucose/glucose_ua_value_model.dart';

class DataDBHandler extends DatabaseHandler {
  DataDBHandler() : super(9);

  @override
  FutureOr<bool> onDatabaseCreate(Transaction db) async {
    await db.execute(MonitorPlanMigration.getCreateSql());
    await db.execute(GlucoseDataTargetMigration.getCreateSql());

    await db.execute(GlucoseUADataMigration.getCreateSql());
    await db.execute(GlucoseUADataMigration.getCreateViewSql());

    await db.execute(GlucoseUADataMigration.addRemarkColumn());

    await db.execute(GlucoseUADataMigration.addRemark2Column());

    await db.execute(GlucoseUADataMigration.addExtraColumn());

    await db.execute(GlucoseUADataMigration.addExtraSampleTypeColumn());

    await db.execute(DataSyncTimeIntervalMigration.getCreateSql());

    await db.execute(DeviceSyncMigration.getCreateSql());

    await db.execute(CgmDataMigration.getCreateSql());
    await db.execute(CgmDataMigration.getCreateDataPackageSql());
    await db.execute(FoodDataMigration.getCreateSql());
    await db.execute(SportDataMigration.getCreateSql());
    await db.execute(DrugDataMigration.getCreateSql());
    await db.execute(DataLogMigration.getCreateViewSql());

    return true;
  }

  @override
  FutureOr<bool> onDatabaseUpgrade(
    Transaction db,
    int oldVer,
    int newVer,
  ) async {
    if (oldVer < 2) {
      await db.execute(GlucoseDataTargetMigration.getCreateSql());
    }
    if( oldVer < 3) {
      await db.execute("ALTER TABLE  data_target ADD diastatic DECIMAL ( 5, 1 );");
    }
    if( oldVer < 4) {
      await db.execute('ALTER TABLE $glucoseValueTable ADD ${GlucoseUAValueColumns.upid} INTEGER;');
    }
    if (oldVer < 5) {
      await db.execute(DataSyncTimeIntervalMigration.getCreateSql());
    }
    if (oldVer < 6) {
      await db.execute(GlucoseUADataMigration.addRemarkColumn());
      await db.execute(GlucoseUADataMigration.addRemark2Column());
      await db.execute(GlucoseUADataMigration.addExtraColumn());
      await db.execute(GlucoseUADataMigration.addExtraSampleTypeColumn());
    }
    if (oldVer < 7) {
      await db.execute(DeviceSyncMigration.getCreateSql());
    }

    if (oldVer < 8) {
      await db.execute(CgmDataMigration.getCreateSql());
      await db.execute(CgmDataMigration.getCreateDataPackageSql());
      await db.execute(FoodDataMigration.getCreateSql());
      await db.execute(SportDataMigration.getCreateSql());
      await db.execute(DrugDataMigration.getCreateSql());
    }

    if (oldVer < 9) {
      await db.execute(DataLogMigration.getCreateViewSql());
    }

    return true;
  }
}
