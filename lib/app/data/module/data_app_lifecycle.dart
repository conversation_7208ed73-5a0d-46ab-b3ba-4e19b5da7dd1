import 'dart:async';

import 'package:dnurse/app/data/service/drug_service.dart';
import 'package:dnurse/app/data/service/sport_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:get/get.dart';

import '../../../framework/module/application_lifecycle.dart';
import '../../../framework/utils/log.dart';
import '../service/data_log_service.dart';
import '../service/data_service.dart';
import '../service/device_sync_service.dart';
import '../service/extra_data_service.dart';
import '../service/glucose_service.dart';
import '../service/reminder_service.dart';
import '../service/sign_in_service.dart';
import '../service/uric_acid_service.dart';
import '../service/food_service.dart';

class DataAppLifecycle extends ApplicationLifecycle {
  @override
  FutureOr<void> onAfterAppInit() {
    Log.i('DataAppLifecycle onAfterAppInit');

    // Log.i("user token:" + UserContext.to.token);
  }

  @override
  FutureOr<void> onAppInit() async {
    Log.i('DataAppLifecycle onAppInit');
    //lazyPut 当第一次使用Get.find<*>时 *才会被调用。
    Get.lazyPut<DataService>(() => DataService());
    Get.lazyPut<GlucoseService>(() => GlucoseService());
    Get.lazyPut<SportService>(() => SportService());
    Get.lazyPut<UricAcidService>(() => UricAcidService());
    Get.lazyPut<ReminderService>(() => ReminderService());
    Get.lazyPut<DeviceSyncService>(() => DeviceSyncService());
    Get.lazyPut<ExtraDataService>(() => ExtraDataService());
    Get.lazyPut<SignInService>(() => SignInService());
    Get.lazyPut<FoodService>(() => FoodService());
    Get.lazyPut<DrugService>(() => DrugService());
    Get.lazyPut<DataLogService>(() => DataLogService());
  }

  @override
  FutureOr<void> onAgreedPrivacy() async {
    Log.i('DataAppLifecycle onAgreedPrivacy');

    DataService.to.initTestLib();

    try {
      if(!AppContext.to.isTemp) {
        ReminderService.to.downloadMonitor();

      }

       ExtraDataService.to.getStorage();
    } catch (e) {
      Log.e("ReminderService.to.downloadMonitor error:", e);
    }
  }
}
