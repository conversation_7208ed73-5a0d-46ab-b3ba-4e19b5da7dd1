
import 'package:dnurse/app/data/service/sport_service.dart';

import '../../../framework/module/sync_handler.dart';
import '../service/data_service.dart';

/// FileName: data_sync_handler
///
/// @Author: ygc
/// @Date: 2022/7/13 18:14
/// @Description:

class DataSyncHandler extends SyncHandler {
  @override
  Future<int> handle(String userId, SyncType syncType, DateTime start, DateTime end) async {

    if (syncType.value & SyncType.syncDownload.value > 0) {
      await DataService.to.syncDataDownload(userId, start, end);
       await SportService.to.syncDataDownload(userId);
    }

    if (syncType.value & SyncType.syncUpload.value > 0) {
      await DataService.to.syncDataUpload(userId);
    }

    return 0;
  }

  @override
  String syncMessage() {
    return "同步数据";
  }
}
