/// FileName: data_module.dart
///
/// @Date: 2024-05-20 11:39:28
/// @Description:
import 'package:dnurse/app/data/module/data_sync_handler.dart';
import 'package:dnurse/app/data/pages/data_test_loading/view.dart';
import 'package:dnurse/app/data/pages/food_custom_add/binding.dart';
import 'package:dnurse/app/data/pages/food_custom_add/view.dart';
import 'package:dnurse/app/data/pages/sport_custom_add/binding.dart';
import 'package:dnurse/app/data/pages/sport_custom_add/view.dart';
import 'package:dnurse/app/data/pages/sport_record/add_sport_record/binding.dart';
import 'package:dnurse/app/data/pages/sport_record/add_sport_record/view.dart';
import 'package:dnurse/config/module_priority.dart';
import 'package:dnurse/framework/module/build/annotations.dart';
import 'package:dnurse/framework/module/module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

import '../../../router/routes.dart';
import '../pages/ble_sync_data/binding.dart';
import '../pages/ble_sync_data/view.dart';
import '../pages/bs_data_add/binding.dart';
import '../pages/bs_data_add/view.dart';
import '../pages/check_in_record/binding.dart';
import '../pages/check_in_record/view.dart';
import '../pages/data_result/binding.dart';
import '../pages/data_result/view.dart';
import '../pages/data_statistic/binding.dart';
import '../pages/data_statistic/view.dart';
import '../pages/data_test_loading/binding.dart';
import '../pages/data_test_main/binding.dart';
import '../pages/data_test_main/view.dart';
import '../pages/drug_record/drug_record_home/binding.dart';
import '../pages/drug_record/drug_record_home/view.dart';
import '../pages/food_record/add_food/binding.dart';
import '../pages/food_record/add_food/view.dart';
import '../pages/food_record/food_record_first/binding.dart';
import '../pages/food_record/food_record_first/view.dart';
import '../pages/food_record/search_food/binding.dart';
import '../pages/food_record/search_food/view.dart';
import '../pages/monitor_reminder/binding.dart';
import '../pages/monitor_reminder/view.dart';
import '../pages/sign_in/daily_sign_in/binding.dart';
import '../pages/sign_in/daily_sign_in/view.dart';
import '../pages/sign_in/sign_diy_share/binding.dart';
import '../pages/sign_in/sign_diy_share/view.dart';
import '../pages/sign_in/sign_edit/binding.dart';
import '../pages/sign_in/sign_edit/view.dart';
import '../pages/sign_in/single_sign_in/binding.dart';
import '../pages/sign_in/single_sign_in/view.dart';

import '../pages/sport_record/binding.dart';
import '../pages/sport_record/view.dart';
import '../pages/sugar_target/binding.dart';
import '../pages/sugar_target/view.dart';
import '../pages/ua_data_add/binding.dart';
import '../pages/ua_data_add/view.dart';
import '../pages/uric_acid_target/binding.dart';
import '../pages/uric_acid_target/view.dart';
import 'data_app_lifecycle.dart';
import 'data_db_handler.dart';

@AppModule(priority: modulePriorityDefault)
class DataModule extends Module {
  DataModule({super.priority})
      : super(
          name: "data",
          databaseHandler: DataDBHandler(),
          applicationLifecycle: DataAppLifecycle(),
          syncHandler: DataSyncHandler(),
        );

  @override
  List<GetPage> getRouters() {
    return [
      GetPage(
        name: Routes.dataTest,
        page: () => DataTestMainPage(),
        binding: DataTestMainBinding(),
      ),
      GetPage(
        name: Routes.dataTestLoading,
        page: () => const DataTestLoadingPage(),
        binding: DataTestLoadingBinding(),
      ),
      GetPage(
        name: Routes.dataResult,
        page: () => const DataResultPage(),
        binding: DataResultBinding(),
      ),
      GetPage(
        name: Routes.dataSugarTarget,
        page: () => const SugarTargetPage(),
        binding: SugarTargetBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.uricAcidTarget,
        page: () => const UricAcidTargetPage(),
        binding: UricAcidTargetBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.dataStatistic,
        page: () => const DataStatisticPage(),
        binding: DataStatisticBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.dataAddBS,
        page: () => const BsDataAddPage(),
        binding: BsDataAddBinding(),
      ),
      GetPage(
        name: Routes.dataBleSync,
        page: () => const BleSyncDataPage(),
        binding: BleSyncDataBinding(),
      ),
      GetPage(
        name: Routes.dataAddUa,
        page: () => const UaDataAddPage(),
        binding: UaDataAddBinding(),
      ),
      GetPage(
        name: Routes.dataMonitorReminder,
        page: () => const MonitorReminderPage(),
        binding: MonitorReminderBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.checkInRecord,
        page: () => const CheckInRecordPage(),
        binding: CheckInRecordBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.dailySignIn,
        page: () => const DailySignInPage(),
        binding: DailySignInBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.singleSignIn,
        page: () => const SingleSignInPage(),
        binding: SingleSignInBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.signEdit,
        page: () => const SignEditPage(),
        binding: SignEditBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.signDiyShare,
        page: () => const SignDiySharePage(),
        binding: SignDiyShareBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.drugHome,
        page: () => const DrugRecordHomePage(),
        binding: DrugRecordHomeBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.foodRecordFirst,
        page: () => const FoodRecordFirstPage(),
        binding: FoodRecordFirstBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.foodCustomAdd,
        page: () => FoodCustomAddPage(),
        binding: FoodCustomAddBinding(),
      ),
      GetPage(
        name: Routes.searchFood,
        page: () => const SearchFoodPage(),
        binding: SearchFoodBinding(),
      ),
      GetPage(
        name: Routes.addFood,
        page: () => const AddFoodPage(),
        binding: AddFoodBinding(),
        // middlewares: [
        //   RoutePrivacyMiddleware(priority: 1),
        // ],
      ),
      GetPage(
        name: Routes.sportRecord,
        page: () => SportRecordPage(),
        binding: SportRecordBinding(),
      ),
      GetPage(
        name: Routes.sportCustomAdd,
        page: () => SportCustomAddPage(),
        binding: SportCustomAddBinding(),
      ),
      GetPage(
        name: Routes.addSportRecord,
        page: () => AddSportRecordPage(),
        binding: AddSportRecordBinding(),
      ),
    ];
  }
}
