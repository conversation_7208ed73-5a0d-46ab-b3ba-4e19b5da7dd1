import '../../../bootstrap/app.dart';

/// FileName: time_point
///
/// @Author: ygc
/// @Date: 2022/6/22 18:25
/// @Description: TODO

class TimePoint extends Object {
  const TimePoint._(this.name, this.id, this.foodPoint);

  final int id;
  final String name;
  final int foodPoint;

  static TimePoint none = TimePoint._(AppContext.to.getLocale().str_allday, 0, 0);
  static TimePoint breakfastBefore = TimePoint._(AppContext.to.getLocale().str_kongfu, 1, 0);
  static TimePoint breakfastAfter = TimePoint._(AppContext.to.getLocale().str_zaocanhou, 2, 0);

  static TimePoint lunchBefore = TimePoint._(AppContext.to.getLocale().str_wcq, 3, 0);
  static TimePoint lunchAfter = TimePoint._(AppContext.to.getLocale().str_wch, 4, 0);

  static TimePoint supperBefore = TimePoint._(AppContext.to.getLocale().str_wancq, 5, 0);
  static TimePoint supperAfter = TimePoint._(AppContext.to.getLocale().str_wanch, 6, 0);

  static TimePoint sleep = TimePoint._(AppContext.to.getLocale().str_shuiqian, 7, 0);
  static TimePoint dawn = TimePoint._(AppContext.to.getLocale().str_lingchen, 8, 0);

  static TimePoint random = TimePoint._(AppContext.to.getLocale().str_suiji, 9, 0);

  static List<TimePoint> values = [
    dawn,
    breakfastBefore,
    breakfastAfter,
    lunchBefore,
    lunchAfter,
    supperBefore,
    supperAfter,
    sleep,
    random
  ];

  static List<TimePoint> normalValues = [
    dawn,
    breakfastBefore,
    breakfastAfter,
    lunchBefore,
    lunchAfter,
    supperBefore,
    supperAfter,
    sleep,
  ];

  static TimePoint getById(int id) => values
      .firstWhere((element) => element.id == id, orElse: () => breakfastBefore);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is TimePoint
        ? id == other.id && foodPoint == other.foodPoint
        : false;
  }

  @override
  int get hashCode => id + name.hashCode + foodPoint;

  @override
  String toString() {
    return "TimePoint {id: $id, name: $name, foodPoint:$foodPoint}";
  }
}
