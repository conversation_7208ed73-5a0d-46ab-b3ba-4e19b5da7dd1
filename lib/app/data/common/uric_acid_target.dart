import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';

import 'data_target.dart';

part 'uric_acid_target.g.dart';

@CopyWith()
class UricAcidTarget with EquatableMixin {
  double low = 120;
  double high = 420;

  UricAcidTarget({
    this.low = 120,
    this.high = 420,
  });

  UricAcidTarget.defaultTarget();

  PointTarget getPointTarget() {
    return PointTarget(low, high);
  }

  @override
  List<Object?> get props => [
        low,
        high,
      ];
}
