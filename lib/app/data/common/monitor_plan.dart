/// FileName: monitor_plan
///
/// @Author: ygc
/// @Date: 2024/8/5 18:19
/// @Description:
import 'package:dnurse/app/data/common/time_point.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../resource/generated/l10n.dart';
import '../entity/reminder/reminder_plan.dart';

// <string name="new_monitor_plan_1">餐后高</string>
// <string name="new_monitor_plan_2">空腹高</string>
// <string name="new_monitor_plan_3">密集监测</string>
// <string name="new_monitor_plan_4">门诊前1天</string>
// <string name="new_monitor_plan_5">强化治疗</string>
// <string name="new_monitor_plan_6">基础一针</string>
// <string name="new_monitor_plan_7">预混两针</string>
// <string name="new_monitor_plan_8">自定义</string>

// <string name="new_monitor_plan_details_1">该方案可以较好的了解三餐前后的血糖变化。适用于非胰岛素治疗的血糖波动大或餐后高血糖患者，方便调整用餐或用药方案</string>
// <string name="new_monitor_plan_details_2">通过连续测量一周睡前和空腹血糖，了解基础胰岛素对血糖的影响。如果连续空腹高血糖，可以适当增加凌晨血糖监测，确认是否有黎明或苏木杰现象。</string>
// <string name="new_monitor_plan_details_3">该方案适用于：刚确诊患者；调整治疗方案；有频发低血糖症状；感染等应激状态等情况。</string>
// <string name="new_monitor_plan_details_4">如果需要复诊，请提前1天测量7点血糖，会更有利于医生分析病情。</string>
// <string name="new_monitor_plan_details_5">适用于多次胰岛素注射或胰岛素泵治疗的患者，每天需要监测空腹、三餐餐后和睡前血糖。</string>
// <string name="new_monitor_plan_details_6">适用于基础胰岛素方案的患者，每周测3天。</string>
// <string name="new_monitor_plan_details_7">适用于每日2次预混胰岛素治疗患者，每周测3天。</string>
// <string name="new_monitor_plan_details_8">您可以根据医生建议制定适合自己的监测方案。</string>

const int _typeCustom = 0;
const int _typeAfterMealHigh = 1;
const int _typeEmptyHigh = 2;
const int _typeIntensiveMonitoring = 3;
const int _typeOutpatientBefore1Day = 4;
const int _typeReinforcementTreatment = 5;
const int _typeSingleShot = 6;
const int _typeTwoShot = 7;

class MonitorPlanType extends Equatable {
  final int id;
  final String Function(BuildContext context) buildName;
  final String Function(BuildContext context) detail;

  const MonitorPlanType({
    required this.id,
    required this.buildName,
    required this.detail,
  });

  static MonitorPlanType afterMealHigh = MonitorPlanType(
    id: _typeAfterMealHigh,
    buildName: (context) {
      return S.of(context).monitorPlanTypeAfterMealHigh;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeAfterMealHighDetail;
    },
  );

  static MonitorPlanType emptyHigh = MonitorPlanType(
    id: _typeEmptyHigh,
    buildName: (context) {
      return S.of(context).monitorPlanTypeEmptyHigh;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeEmptyHighDetail;
    },
  );
  static MonitorPlanType intensiveMonitoring = MonitorPlanType(
    id: _typeIntensiveMonitoring,
    buildName: (context) {
      return S.of(context).monitorPlanTypeIntensiveMonitoring;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeIntensiveMonitoringDetail;
    },
  );
  static MonitorPlanType outpatientBefore1Day = MonitorPlanType(
    id: _typeOutpatientBefore1Day,
    buildName: (context) {
      return S.of(context).monitorPlanTypeOutpatientBefore1Day;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeOutpatientBefore1DayDetail;
    },
  );
  static MonitorPlanType reinforcementTreatment = MonitorPlanType(
    id: _typeReinforcementTreatment,
    buildName: (context) {
      return S.of(context).monitorPlanTypeReinforcementTreatment;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeReinforcementTreatmentDetail;
    },
  );
  static MonitorPlanType singleShot = MonitorPlanType(
    id: _typeSingleShot,
    buildName: (context) {
      return S.of(context).monitorPlanTypeSingleShot;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeSingleShotDetail;
    },
  );
  static MonitorPlanType twoShot = MonitorPlanType(
    id: _typeTwoShot,
    buildName: (context) {
      return S.of(context).monitorPlanTypeTwoShot;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeTwoShotDetail;
    },
  );
  static MonitorPlanType custom = MonitorPlanType(
    id: _typeCustom,
    buildName: (context) {
      return S.of(context).monitorPlanTypeCustom;
    },
    detail: (context) {
      return S.of(context).monitorPlanTypeCustomDetail;
    },
  );

  static MonitorPlanType? fromId(int id) {
    switch (id) {
      case _typeAfterMealHigh:
        return afterMealHigh;
      case _typeEmptyHigh:
        return emptyHigh;
      case _typeIntensiveMonitoring:
        return intensiveMonitoring;
      case _typeOutpatientBefore1Day:
        return outpatientBefore1Day;
      case _typeReinforcementTreatment:
        return reinforcementTreatment;
      case _typeSingleShot:
        return singleShot;
      case _typeTwoShot:
        return twoShot;
      case _typeCustom:
        return custom;
    }

    return null;
  }

  static List<MonitorPlanType> getOptions() {
    return [
      afterMealHigh,
      emptyHigh,
      intensiveMonitoring,
      outpatientBefore1Day,
      reinforcementTreatment,
      singleShot,
      twoShot,
      custom,
    ];
  }

  String getName(BuildContext context) {
    return buildName(context);
  }

  String getDetail(BuildContext context) {
    return detail(context);
  }

  @override
  String toString() {
    return "MonitorPlanType{id: $id}";
  }

  @override
  List<Object?> get props => [id];

  void fillReminderPlan(ReminderPlan plan) {
    plan.enabled = 0;

    if (plan.timePoint == TimePoint.dawn) {
      return;
    }
    switch (id) {
      case _typeAfterMealHigh:
        {
          if (plan.timePoint == TimePoint.breakfastBefore ||
              plan.timePoint == TimePoint.breakfastAfter) {
            plan.enabled = 0x49;
            plan.repeated =0x49;
          } else if (plan.timePoint == TimePoint.lunchBefore ||
              plan.timePoint == TimePoint.lunchAfter) {
            plan.enabled = 0x12;
            plan.repeated =0x12;
          } else {
            plan.enabled = 0x24;
            plan.repeated =0x24;
          }
        }
        break;

      case _typeEmptyHigh:
        {
          if (plan.timePoint == TimePoint.breakfastBefore ||
              plan.timePoint == TimePoint.sleep) {
            plan.enabled = reminderAllDayOpen;
            plan.repeated =reminderAllDayOpen;
          }
        }
        break;

      case _typeIntensiveMonitoring:
        {
          plan.enabled = 0x1C;
          plan.repeated =0x1C;
        }
        break;

      case _typeOutpatientBefore1Day:
        {
          plan.enabled = 0x04;
          plan.repeated =0x04;
        }
        break;

      case _typeReinforcementTreatment:
        {
          if (!(plan.timePoint == TimePoint.lunchBefore ||
              plan.timePoint == TimePoint.supperBefore)) {
            plan.enabled = reminderAllDayOpen;
            plan.repeated =reminderAllDayOpen;
          }
        }
        break;

      case _typeSingleShot:
        {
          if (plan.timePoint == TimePoint.breakfastBefore ||
              plan.timePoint == TimePoint.breakfastAfter ||
              plan.timePoint == TimePoint.supperAfter) {
            plan.enabled = 0x54;
            plan.repeated =0x54;
          }
        }
        break;

      case _typeTwoShot:
        {
          if (plan.timePoint == TimePoint.breakfastBefore ||
              plan.timePoint == TimePoint.supperBefore ||
              plan.timePoint == TimePoint.supperAfter) {
            plan.enabled = 0x54;
            plan.repeated =0x54;
          }
        }
    }
  }
}
