/// FileName: food_type
///
/// @Author: ygc
/// @Date: 2025/7/1 14:37
/// @Description:
import 'package:dnurse/app/data/common/time_point.dart';

class FoodType extends Object {
  const FoodType._(this.id, this.name, this.title, this.timePoint);

  final int id;
  final String name;
  final String title;
  final TimePoint timePoint;

  static FoodType breakfast = FoodType._(
    0,
    "breakfast",
    "早餐",
    TimePoint.breakfastAfter,
  );

  static FoodType lunch = FoodType._(
    1,
    "lunch",
    "午餐",
    TimePoint.lunchAfter,
  );

  static FoodType supper = FoodType._(
    2,
    "supper",
    "晚餐",
    TimePoint.supperAfter,
  );

  static FoodType extra = FoodType._(
    3,
    "extra",
    "加餐",
    TimePoint.none,
  );

  static FoodType extraBreakfast = FoodType._(
    4,
    "extra_breakfast",
    "上午加餐",
    TimePoint.lunchBefore,
  );

  static FoodType extraLunch = FoodType._(
    5,
    "extra_lunch",
    "下午加餐",
    TimePoint.supperBefore,
  );

  static FoodType extraSupper = FoodType._(
    6,
    "extra_supper",
    "晚上加餐",
    TimePoint.sleep,
  );

  static List<FoodType> values = [
    breakfast,
    lunch,
    supper,
    extra,
    extraBreakfast,
    extraLunch,
    extraSupper,
  ];

  static FoodType getById(int id) => values.firstWhere(
        (element) => element.id == id,
        orElse: () => breakfast,
      );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is FoodType ? id == other.id : false;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return "FoodType {id: $id, name:$name, title:$title, timePoint:$timePoint}";
  }
}
