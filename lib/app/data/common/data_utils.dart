import 'dart:math' as math;

import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/app/user/entity/unit/glucose_unit.dart';
import 'package:dnurse/app/user/service/user_config_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:intl/intl.dart';

import '../../../framework/utils/log.dart';
import '../entity/data_glucose.dart';
import 'data_status.dart';
import 'data_target.dart';

/// FileName: data_utils
///
/// @Author: ygc
/// @Date: 2022/6/23 19:08
/// @Description:

class DataUtils {
  static int convertToDBValue(double value) {
    return (value * 100).roundToDouble().toInt();
  }

  static double convertToEntityValue(int value) {
    return value / 100;
  }

  static DataValueStatus getDataValueStatus(
      double value, TimePoint timePoint, DataTarget dataTarget) {
    if (value < DataGlucose.minValue || value > DataGlucose.maxValue) {
      return DataValueStatus.empty;
    }

    PointTarget pointTarget = dataTarget.getTargetByPoint(timePoint);
    if (value < pointTarget.mini) {
      return DataValueStatus.low;
    } else if (value > pointTarget.max) {
      return DataValueStatus.high;
    }

    return DataValueStatus.normal;
  }

  static String formatMole(double value, {bool withHighLow = true}) {
    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;

    return DataGlucose.formatMole(value, glucoseUnit, withHighLow: withHighLow);
  }

  static double stringValueToMole(String value) {
    double doubleValue = double.tryParse(value) ?? 0;
    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;
    if (glucoseUnit == GlucoseUnit.mg) {
      double maxValue = DataGlucose.maxValue * DataGlucose.moleToMgRatio;
      double miniValue = DataGlucose.minValue * DataGlucose.moleToMgRatio;
      if (doubleValue < miniValue) {
        return miniValue;
      } else if (doubleValue > maxValue) {
        return maxValue;
      } else {
        return doubleValue / DataGlucose.moleToMgRatio;
      }
    } else {
      double maxValue = DataGlucose.maxValue;
      double miniValue = DataGlucose.minValue;
      if (doubleValue < miniValue) {
        return miniValue;
      } else if (doubleValue > maxValue) {
        return maxValue;
      }

      return doubleValue;
    }
  }

  static String glucoseValueRangeError() {
    return '血糖值应该在${formatMole(DataGlucose.minValue)}到${formatMole(DataGlucose.maxValue)}之间.';
  }

  static bool validateGlucoseValueMole(double value) {
    if (value < DataGlucose.minValue) {
      return false;
    } else if (value > DataGlucose.maxValue) {
      return false;
    }
    return true;
  }

  static NumberFormat b6Format = NumberFormat("000000", "en_US");
  static String generateDidWithPrefix(String prefix) {
    DateTime now = DateTime.now();
    Log.d('当前时间：$now');
    String dateTimeStr = now.formatDate('yyyyMMddHHmmss');
    Log.d('时间格式化：$dateTimeStr');
    int random = math.Random().nextInt(100000); //6位数随机数
    Log.d('随机数：$random');
    String randomStr = b6Format.format(random);
    Log.d('随机数格式化：$randomStr');

    return '$prefix$dateTimeStr$randomStr';
  }

// static TimePoint getTimePointFromTime(UserPlan userPlan, DateTime dateTime) {
//   List<int> times = userPlan.timeList;
//   List<TimePoint> timePoints = [
//     TimePoint.dawn,
//     TimePoint.breakfastBefore,
//     TimePoint.breakfastAfter,
//     TimePoint.lunchBefore,
//     TimePoint.lunchAfter,
//     TimePoint.supperBefore,
//     TimePoint.supperAfter,
//     TimePoint.sleep,
//   ];
//   int dayMinutes = 60 * 24;
//   int timeMinutes = dateTime.hour * 60 + dateTime.minute;
//   if (timeMinutes < times.first && timeMinutes > times.last) {
//     //  睡前时间以后， 凌晨之前
//
//     int endTime = times.first + dayMinutes;
//     int startTime = times.last;
//
//     if (timeMinutes < times.first) {
//       timeMinutes += dayMinutes;
//     }
//     int dis1 = (timeMinutes - startTime).abs();
//     int dis2 = (timeMinutes - endTime).abs();
//
//     return dis1 >= dis2 ? TimePoint.dawn : TimePoint.sleep;
//   } else {
//     for (int index = 0; index < times.length - 1; index++) {
//       int startTime = times[index];
//       int endTime = times[index + 1];
//       if (timeMinutes >= startTime && timeMinutes < endTime) {
//         int dis1 = (timeMinutes - startTime).abs();
//         int dis2 = (timeMinutes - endTime).abs();
//         return dis1 <= dis2 ? timePoints[index] : timePoints[index + 1];
//       }
//     }
//
//     return TimePoint.breakfastBefore;
//   }
// }
}
