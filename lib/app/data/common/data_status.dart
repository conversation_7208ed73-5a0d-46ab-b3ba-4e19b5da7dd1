import 'package:flutter/material.dart';

/// FileName: time_point
///
/// @Author: ygc
/// @Date: 2022/6/22 18:25
/// @Description: 

class DataValueStatus extends Object {
  const DataValueStatus._(this.id, this.name, this.color, this.gradientColor);

  final int id;
  final String name;
  final Color color;
  final LinearGradient gradientColor;
  static const DataValueStatus high = DataValueStatus._(
      0,
      "high",
      Color(0xFFE35864),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFF96B78),
            Color(0xFFE35864),
          ]));
  static const DataValueStatus normal = DataValueStatus._(
      1,
      "normal",
      Color(0xFF8CC152),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFA0D486),
            Color(0xFF8CC152),
          ]));
  static const DataValueStatus low = DataValueStatus._(
      2,
      "low",
      Color(0xFFF6BB42),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFF6BB42),
            Color(0xFFFFCE54),
          ]));
  static const DataValueStatus empty = DataValueStatus._(
      3,
      "empty",
      Colors.black,
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Colors.black,
            Colors.black,
          ]));

  static List<DataValueStatus> values = [
    high,
    normal,
    low,
    empty,
  ];

  static DataValueStatus getById(int id) =>
      values.firstWhere((element) => element.id == id, orElse: () => normal);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is DataValueStatus ? id == other.id : false;
  }

  @override
  int get hashCode => id + name.hashCode + color.hashCode;

  @override
  String toString() {
    return "DataValueStatus {id: $id, name:$name, color:$color}";
  }
}
