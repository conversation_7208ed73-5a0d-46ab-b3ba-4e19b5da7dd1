import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';

import './time_point.dart';

/// FileName: data_target
///
/// @Author: ygc
/// @Date: 2022/6/23 09:47
/// @Description: 控糖目标

part 'data_target.g.dart';

/// 时间段目标值
class PointTarget {
  PointTarget(this.mini, this.max);

  final double mini;
  final double max;

  @override
  String toString() {
    return "PointTarget{mini: $mini, max: $max}";
  }
}



@CopyWith()
class DataTarget with EquatableMixin {
  int id = 0;
  double diastatic = 6.5;

  double dawnLow = 4.4;
  double dawnHigh = 7.0;

  double emptyLow = 4.4;
  double emptyHigh = 7.0;

  double mealBeforeLow = 4.4;
  double mealBeforeHigh = 7.0;

  double mealAfterLow = 5.0;
  double mealAfterHigh = 10.0;

  double sleepLow = 4.4;
  double sleepHigh = 7.0;

  bool userModified = false;

  DataTarget({
    this.diastatic = 6.5,
    this.dawnLow = 4.4,
    this.dawnHigh = 7.0,
    this.emptyLow = 4.4,
    this.emptyHigh = 7.0,
    this.mealBeforeLow = 4.4,
    this.mealBeforeHigh = 7.0,
    this.mealAfterLow = 5.0,
    this.mealAfterHigh = 10.0,
    this.sleepLow = 4.4,
    this.sleepHigh = 7.0,
    this.userModified = false,
    this.id = 0,
  });

  DataTarget.defaultTarget();

  @override
  String toString() {
    return "DataTarget{id: $id, diastatic: $diastatic, dawnLow: $dawnLow, dawnHigh: $dawnHigh, emptyLow: $emptyLow, emptyHigh: $emptyHigh, mealBeforeLow: $mealBeforeLow, meal}";
  }
  PointTarget getTargetByPoint(TimePoint point) {
    if (point == TimePoint.breakfastBefore) {
      return PointTarget(emptyLow, emptyHigh);
    } else if (point == TimePoint.dawn) {
      return PointTarget(dawnLow, dawnHigh);
    } else if (point == TimePoint.sleep) {
      return PointTarget(sleepLow, sleepHigh);
    } else if (point == TimePoint.breakfastBefore ||
        point == TimePoint.lunchBefore ||
        point == TimePoint.supperBefore) {
      return PointTarget(mealBeforeLow, mealBeforeHigh);
    } else if (point == TimePoint.breakfastAfter ||
        point == TimePoint.lunchAfter ||
        point == TimePoint.supperAfter) {
      return PointTarget(mealAfterLow, mealAfterHigh);
    }

    return getRandomTarget();
  }

  PointTarget getAllRange() {
    double miniLow = dawnLow;
    if (emptyLow < miniLow) {
      miniLow = emptyLow;
    }
    if (mealBeforeLow < miniLow) {
      miniLow = mealBeforeLow;
    }
    if (mealAfterLow < miniLow) {
      miniLow = mealAfterLow;
    }
    if (sleepLow < miniLow) {
      miniLow = sleepLow;
    }

    double maxHigh = dawnHigh;
    if (emptyHigh > maxHigh) {
      maxHigh = emptyHigh;
    }
    if (mealBeforeHigh > maxHigh) {
      maxHigh = mealBeforeHigh;
    }
    if (mealAfterHigh > maxHigh) {
      maxHigh = mealAfterHigh;
    }
    if (sleepHigh > maxHigh) {
      maxHigh = sleepHigh;
    }

    return PointTarget(miniLow, maxHigh);
  }

  PointTarget getRandomTarget() {
    double low = dawnLow;
    low = low > emptyLow ? emptyLow : low;
    low = low > mealBeforeLow ? mealBeforeLow : low;
    low = low > mealAfterLow ? mealAfterLow : low;
    low = low > sleepLow ? sleepLow : low;

    double high = dawnHigh;
    high = high < emptyHigh ? emptyHigh : high;
    high = high < mealBeforeHigh ? mealBeforeHigh : high;
    high = high < mealAfterHigh ? mealAfterHigh : high;
    high = high < sleepHigh ? sleepHigh : high;

    return PointTarget(low, high);
  }

  @override
  List<Object?> get props =>
      [
        id,
        diastatic,
        dawnLow,
        dawnHigh,
        emptyLow,
        emptyHigh,
        mealBeforeLow,
        mealBeforeHigh,
        mealAfterLow,
        mealAfterHigh,
        sleepLow,
        sleepHigh,
        userModified
      ];
}
