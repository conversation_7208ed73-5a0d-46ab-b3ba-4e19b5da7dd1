/// FileName: uric_acid_data_status
///
/// @Author: ygc
/// @Date: 2024/8/13 14:20
/// @Description: 


import 'package:flutter/material.dart';

class UricAcidDataValueStatus extends Object {
  const UricAcidDataValueStatus._(this.id, this.name, this.color, this.gradientColor);

  final int id;
  final String name;
  final Color color;
  final LinearGradient gradientColor;
  static const UricAcidDataValueStatus high = UricAcidDataValueStatus._(
      0,
      "high",
      Color(0xFFE35864),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFF96B78),
            Color(0xFFE35864),
          ]));
  static const UricAcidDataValueStatus normal = UricAcidDataValueStatus._(
      1,
      "normal",
      Color(0xFF8CC152),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFA0D486),
            Color(0xFF8CC152),
          ]));
  static const UricAcidDataValueStatus low = UricAcidDataValueStatus._(
      2,
      "low",
      Color(0xFFF6BB42),
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Color(0xFFF6BB42),
            Color(0xFFFFCE54),
          ]));
  static const UricAcidDataValueStatus empty = UricAcidDataValueStatus._(
      3,
      "empty",
      Colors.black,
      LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[
            Colors.black,
            Colors.black,
          ]));

  static List<UricAcidDataValueStatus> values = [
    high,
    normal,
    low,
    empty,
  ];

  static UricAcidDataValueStatus getById(int id) =>
      values.firstWhere((element) => element.id == id, orElse: () => normal);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is UricAcidDataValueStatus ? id == other.id : false;
  }

  @override
  int get hashCode => id + name.hashCode + color.hashCode;

  @override
  String toString() {
    return "UricAcidDataValueStatus {id: $id, name:$name, color:$color}";
  }
}
