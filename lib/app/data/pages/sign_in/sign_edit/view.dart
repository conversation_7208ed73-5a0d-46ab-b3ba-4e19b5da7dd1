import 'package:bruno/bruno.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:dnurse/ui/widgets/page/loading_page.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/widgets/dialog/dnu_dialog.dart';
import '../../../../../ui/widgets/dnu_toast.dart';
import '../../../api/dto/sign_in/sign_in_diy_background_dto.dart';
import 'logic.dart';

class SignEditPage extends StatelessWidget {
  const SignEditPage({super.key});

  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    final logic = Get.find<SignEditLogic>();
    final state = Get.find<SignEditLogic>().state;
    return Scaffold(
      body: Obx(() {
        return LoadingPage(
          pageState: logic.state.pageState.value,
          child: Padding(
            padding: EdgeInsets.only(top: padding.top),
            child: Stack(
              children: [
                SizedBox.expand(
                  child: SingleChildScrollView(
                    child: GestureDetector(
                      onTap: () {
                        state.isSelectedBG.value = !state.isSelectedBG.value;
                        state.isSelectedIcon.value = false;
                      },
                      child: AspectRatio(
                        aspectRatio: 750 / 1344,
                        child: WidgetsToImage(
                          controller: logic.controller,
                          child: Stack(clipBehavior: Clip.none, children: [
                            DottedBorder(
                              color: state.isSelectedRight.value
                                  ? Colors.transparent
                                  : Colors.red,
                              dashPattern: const [8, 4],
                              borderType: BorderType.RRect,
                              child: SizedBox(
                                child: state.selectedBackground.value != null
                                    ? DNUImage.network(
                                        state.selectedBackground.value!.imgUrl,
                                        fit: BoxFit.fill,
                                        width: double.infinity,
                                        height: double.infinity,
                                        useCache: true,
                                      )
                                    : Container(),
                              ),
                            ),
                            buildWhiteBox(context),
                          ]),
                        ),
                      ),
                    ),
                  ),
                ),
                if (!state.isSelectedRight.value)
                  Positioned(
                      right: 0, child: buildClickChange(context, '点击更换')),
                Visibility(
                    visible: !state.isSelectedRight.value,
                    child: buildBottomSheet(context)),
              ],
            ),
          ),
        );
      }),
    );
  }

  //白色框部分
  Widget buildWhiteBox(BuildContext context) {
    return Positioned(
      top: 50.w,
      left: 0,
      right: 0,
      child: Center(
        child: Column(children: [
          buildIcon(context),
          SizedBox(
            height: 30.w,
          ),
          _buildDate(context),
          SizedBox(
            height: 100.w,
          ),
          buildTextEdit(context),
        ]
            //   buildIcon(context)
            // ]),
            ),
      ),
      // ])
    );
  }

//日期
  Widget _buildDate(BuildContext context) {
    final state = Get.find<SignEditLogic>().state;

    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd').format(now); // 日
    String formattedMonth = DateFormat('MM/yyyy\nEEEE').format(now); // 月

    return Container(
      decoration: const BoxDecoration(
          border: Border(
        left: BorderSide(color: AppColor.lineColor, width: 1),
        right: BorderSide(color: AppColor.lineColor, width: 1),
      )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            formattedDate,
            style: TextStyle(
                fontSize: 35.sp,
                color: state.selectedBackground.value != null
                    ? Color(int.parse(
                        '0xFF${state.selectedBackground.value!.dateColor.substring(1)}'))
                    : Colors.white),
          ),
          SizedBox(
            width: 2.w,
          ),
          Text(
            formattedMonth,
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 13.sp,
              color: state.selectedBackground.value != null
                  ? Color(int.parse(
                      '0xFF${state.selectedBackground.value!.dateColor.substring(1)}'))
                  : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  //图标框
  Widget buildIcon(BuildContext context) {
    final state = Get.find<SignEditLogic>().state;
    return Center(
      child: GestureDetector(
        onTap: () {
          state.isSelectedIcon.value = !state.isSelectedIcon.value;
          state.isSelectedBG.value = false;
          Log.i('state.isSelectedIcon.value ---${state.isSelectedIcon.value}');
        },
        child: Stack(children: [
          DottedBorder(
            color:
                state.isSelectedRight.value ? Colors.transparent : Colors.red,
            dashPattern: const [8, 4],
            borderType: BorderType.RRect,
            child: state.selectedIconUrl.value != ''
                ? DNUImage.network(
                    state.selectedIconUrl.value,
                    width: 125.w,
                    height: 105.w,
                    useCache: true,
                  )
                : Container(),
          ),
          if (!state.isSelectedRight.value)
            Positioned(
              right: 0,
              top: 3.w,
              child: buildClickChange(context, '点击更换'),
            ),
        ]),
      ),
    );
  }

//图标选择框
  Widget buildIconSelect(BuildContext context) {
    final logic = Get.put(SignEditLogic());
    final state = Get.find<SignEditLogic>().state;
    final MediaQueryData data = MediaQuery.of(context);
    return Container(
      padding:
          EdgeInsets.only(left: 10.w, right: 10.w, top: 10.w, bottom: 10.w),
      height: 150.w,
      width: data.size.width,
      color: Colors.white,
      child: GridView.builder(
        scrollDirection: Axis.horizontal,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: state.signInDiyDTO.value!.iconList.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, crossAxisSpacing: 10.w, mainAxisSpacing: 10.w),
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
            onTap: () {
              logic.selectIcon(state.signInDiyDTO.value!.iconList[index]);
            },
            child: Stack(children: [
              DNUImage.network(
                state.signInDiyDTO.value!.iconList[index],
                useCache: true,
              ),
              if (state.signInDiyDTO.value!.iconList[index] ==
                  state.selectedIconUrl.value)
                Positioned(
                  bottom: 0.w,
                  right: -1.w,
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.orange,
                    size: 15.w,
                  ),
                )
            ]),
          );
        },
      ),
    );
  }

  //点击更换/编辑
  Widget buildClickChange(BuildContext context, String text) {
    return Container(
      width: 60.w,
      height: 18.w,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(10),
        ),
        color: Colors.red.withOpacity(0.7),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(color: Colors.white, fontSize: 12.sp),
        ),
      ),
    );
  }

// 返回dialog
  void _showDialog() {
    final logic = Get.find<SignEditLogic>();
    if (logic.state.signInDiyDTO.value == null) {
      AppRouter.back();
      return;
    }

    if (logic.state.inputText.value.isEmpty &&
        logic.state.selectedBackground.value ==
            logic.state.signInDiyDTO.value!.backgroundList.first &&
        logic.state.selectedIconUrl.value ==
            logic.state.signInDiyDTO.value!.iconList.first) {
      AppRouter.back();
      return;
    }

    DNUDialog.showConfirm(
        title: '将此次编辑保留?',
        cancel: '不保留',
        confirm: '保留',
        // content: AppContext.to.getLocale().dataDelete,
        onConfirm: () async {
          await logic.saveState();
          AppRouter.back();
        },
        onCancel: () async {
          await logic.clearState();
          AppRouter.back();
        });
  }

//底部标签栏
  Widget buildBottomSheet(BuildContext context) {
    final logic = Get.find<SignEditLogic>();
    final state = Get.find<SignEditLogic>().state;
    final MediaQueryData data = MediaQuery.of(context);
    return Positioned(
        bottom: 0.w,
        left: 0,
        right: 0,
        child: Column(
          children: [
            Obx(() {
              if (!state.isSelectedBG.value) {
                return Container();
              }
              return buildBackgroundSelect(context);
            }),
            Obx(() {
              if (!state.isSelectedIcon.value) {
                return Container();
              }
              return buildIconSelect(context);
            }),
            Container(
              padding: EdgeInsets.only(
                left: 20.w,
                right: 20.w,
                bottom: MediaQuery.of(context).padding.bottom + 10.w,
                top: 10.w,
              ),
              // height: 40.w,
              width: data.size.width,
              color: Colors.black.withOpacity(0.5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      _showDialog();
                    },
                    child: Icon(
                      Icons.chevron_left,
                      color: Colors.white,
                      size: 30.w,
                    ),
                  ),
                  Text(
                    state.isSelectedIcon.value
                        ? '请选择插图'
                        : state.isSelectedBG.value
                            ? '请选择背景色'
                            : '自定义日签',
                    style: const TextStyle(color: Colors.white),
                  ),
                  GestureDetector(
                    onTap: () async {
                      if (state.inputText.value != '') {
                        if (context.mounted) {
                          DNULoading.show(context, content: '生成中');
                        }

                        await logic.clearState();

                        await logic.saveDiyInfo(state.inputText.value);

                        state.isSelectedRight.value =
                            !state.isSelectedRight.value;
                        state.isSelectedBG.value = false;
                        state.isSelectedIcon.value == false;

                        await Future.delayed(const Duration(milliseconds: 20));

                        state.bytes.value = await logic.controller.capture();
                        if (context.mounted) {
                          DNULoading.dismiss(context);
                        }
                        AppRouter.push(Routes.signDiyShare,
                            arguments: state.bytes.value);
                        state.isSelectedRight.value = false;
                      } else {
                        DNUToast.show("请先输入内容", context);
                      }
                    },
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 30.w,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

//文字编辑框
  Widget buildTextEdit(BuildContext context) {
    final state = Get.find<SignEditLogic>().state;
    return GestureDetector(
      onTap: () {
        _showBottomWriteDialog(context);
      },
      child: Stack(children: [
        DottedBorder(
          color: state.isSelectedRight.value ? Colors.transparent : Colors.red,
          dashPattern: const [8, 4],
          borderType: BorderType.RRect,
          child: Container(
            color: Colors.white,
            width: 200.w,
            height: 230.w,
            child: Align(
              alignment: Alignment.topCenter,
              child: state.inputText.value == ''
                  ? Text(
                      '请输入您的文字',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: AppColor.textDesc,
                      ),
                    )
                  : Text(
                      state.inputText.value,
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: const Color(0xFF434A54),
                      ),
                    ),
            ),
          ),
        ),
        if (!state.isSelectedRight.value)
          Positioned(right: 0, child: buildClickChange(context, '点击编辑'))
      ]),
    );
  }

//点击输入文字弹出
  void _showBottomWriteDialog(BuildContext context) {
    final state = Get.find<SignEditLogic>().state;
    BrnBottomWritePicker.show(
      context,
      leftTag: '放弃',
      rightTag: '确认',
      hintText: '请输入您的文字(80字以内)',
      confirmDismiss: true,
      maxLength: 80,
      onConfirm: (context, string) async {
        if (string == '') {
          DNUToast.show("请先输入内容", context);
        } else {
          state.inputText.value = string!;
          AppRouter.back();
        }
      },
      onCancel: (_) async {
        AppRouter.back();
      },
      defaultText: state.inputText.value,
    );
  }

  //背景选择框
  Widget buildBackgroundSelect(BuildContext context) {
    final state = Get.find<SignEditLogic>().state;
    return Container(
      padding:
          EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w, bottom: 20.w),
      height: 100.w,
      width: double.infinity,
      color: Colors.white,
      child: GridView.builder(
        scrollDirection: Axis.horizontal,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: state.signInDiyDTO.value!.backgroundList.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          mainAxisSpacing: 10.w,
        ),
        itemBuilder: (BuildContext context, int index) {
          SignInDiyBackgroundDTO background =
              state.signInDiyDTO.value!.backgroundList[index];
          return GestureDetector(
            onTap: () {
              // 更新选中的背景
              state.selectedBackground.value = background;
            },
            child: Stack(clipBehavior: Clip.none, children: [
              Container(
                decoration: BoxDecoration(
                  gradient: _createGradient(
                      background.bgStartColor, background.bgEndColor),
                ),
              ),
              if (background == state.selectedBackground.value)
                Positioned(
                  bottom: 0.w,
                  right: -3.w,
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.orange,
                    size: 15.w,
                  ),
                )
            ]),
          );
        },
      ),
    );
  }

  LinearGradient _createGradient(String startColor, String endColor) {
    return LinearGradient(
      colors: [
        Color(int.parse('0xFF${startColor.substring(1)}')), // 添加0xFF
        Color(int.parse('0xFF${endColor.substring(1)}')) // 添加0xFF
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );
  }
}
