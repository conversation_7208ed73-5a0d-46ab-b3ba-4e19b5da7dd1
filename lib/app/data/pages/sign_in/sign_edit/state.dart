import 'dart:typed_data';

import 'package:get/get.dart';

import '../../../../../ui/widgets/page/loading_page.dart';
import '../../../api/dto/sign_in/sign_in_diy_background_dto.dart';
import '../../../api/dto/sign_in/sign_in_diy_dto.dart';

class SignEditState {
  final pageState = LoadingPageState.loading.obs;

  final isSelectedBG = false.obs;
  final isSelectedIcon = false.obs;
  final isSelectedRight = false.obs;
  final selectedIconUrl = ''.obs;
  final inputText = ''.obs;
  final selectedBackground = Rx<SignInDiyBackgroundDTO?>(null);

  final signInDiyDTO = Rx<SignInDiyDTO?>(null);
  
  final bytes = Rx<Uint8List?>(null);
}
