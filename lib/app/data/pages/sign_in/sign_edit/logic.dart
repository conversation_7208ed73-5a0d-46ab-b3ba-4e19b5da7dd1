import 'dart:convert';

import 'package:dnurse/app/data/service/sign_in_service.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/router/router.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import '../../../../../framework/network/response.dart';
import '../../../../../framework/service/key_value_storage.dart';
import '../../../../../framework/utils/log.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import '../../../api/daily_sign_api.dart';
import '../../../api/dto/sign_in/sign_in_diy_background_dto.dart';
import '../../../api/dto/sign_in/sign_in_diy_dto.dart';
import 'state.dart';

class SignEditLogic extends DNUBaseController {
  final SignEditState state = SignEditState();
  WidgetsToImageController controller = WidgetsToImageController();

  @override
  void onInit() {
    super.onInit();
    // clearState();
    restoreState();
    loadDiyConfig();
  }

  Future<void> loadDiyConfig() async {
    try {
      SignInDiyDTO? signInDiyDTO = await SignInService.to.getDiyConfig();
      if (signInDiyDTO == null) {
        AppRouter.back();
        return;
      }
      state.signInDiyDTO.value = signInDiyDTO;
      if (signInDiyDTO.iconList.isEmpty ||
          signInDiyDTO.backgroundList.isEmpty) {
        AppRouter.back();
        return;
      }

      if(state.selectedIconUrl.value.isEmpty) {
        state.selectedIconUrl.value = signInDiyDTO.iconList.first;

      }
      state.selectedBackground.value ??= signInDiyDTO.backgroundList.first;

      state.pageState.value = LoadingPageState.success;
    } catch (e) {
      Log.i('Error: $e');
      AppRouter.back();
    }
  }

  Future<void> saveDiyInfo(String content) async {
    try {
      JsonResponse response = await DailySignApi.saveDiyInfo(content: content);
      if (response.successful) {
        Log.i('DiyAdviceInfo456----------${response.data}');
      } else {
        Log.i('Error: ${response.message}');
      }
    } catch (e) {
      Log.i('Error: $e');
    }
  }

  Future<void> saveState() async {
    await KeyValueStorage.to
        .setString('selectedIconUrl', state.selectedIconUrl.value);
    String selectedBackgroundJson =
        json.encode(state.selectedBackground.value?.toJson());
    await KeyValueStorage.to
        .setString('selectedBackground', selectedBackgroundJson);
    await KeyValueStorage.to.setString('inputText', state.inputText.value);
  }

  Future<void> restoreState() async {
    final selectedIconUrl = KeyValueStorage.to.getString('selectedIconUrl');
    final selectedBackgroundJson =
        KeyValueStorage.to.getString('selectedBackground');
    final inputText = KeyValueStorage.to.getString('inputText');
    if (selectedIconUrl != null && selectedIconUrl.isNotEmpty) {
      state.selectedIconUrl.value = selectedIconUrl;
    }
    if (selectedBackgroundJson != null && selectedBackgroundJson.isNotEmpty) {
      final jsonMap = jsonDecode(selectedBackgroundJson);
      state.selectedBackground.value = SignInDiyBackgroundDTO.fromJson(jsonMap);
    }
    if (inputText != null && inputText.isNotEmpty) {
      state.inputText.value = inputText;
    }
    Log.i('State restored');
  }

  Future<void> clearState() async {
    await Future.wait([
      KeyValueStorage.to.remove('selectedIconUrl'),
      KeyValueStorage.to.remove('selectedBackground'),
      KeyValueStorage.to.remove('inputText'),
    ]);
    Log.i('State cleared');
  }

  void selectIcon(String iconUrl) {
    state.selectedIconUrl.value = iconUrl; // 添加这个方法来更新当前选中的图标
  }

  void selectBackground(SignInDiyBackgroundDTO background) {
    state.selectedBackground.value = background;
  }
}
