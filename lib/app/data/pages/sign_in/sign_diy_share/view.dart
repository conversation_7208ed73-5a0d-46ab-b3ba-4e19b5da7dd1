import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/router/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import 'logic.dart';

class SignDiySharePage extends StatelessWidget {
  const SignDiySharePage({super.key});

  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    final state = Get.find<SignDiyShareLogic>().state;
    return Scaffold(
        body: Padding(
      padding: EdgeInsets.only(top: padding.top),
      child: Obx(() {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            // if (state.bytes.value != null)
            SizedBox.expand(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(
                      backgroundColor: Colors.white,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                    SizedBox(
                      height: 5.w,
                    ),
                    Text(
                      "日签加载中",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (state.bytes.value != null)
              SizedBox.expand(
                child: GestureDetector(
                  onTap: () {
                    state.isVisible.value = !state.isVisible.value;
                  },
                  child: Image(
                    image: MemoryImage(state.bytes.value!),
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    frameBuilder: (BuildContext context, Widget child,
                        int? frame, bool wasSynchronouslyLoaded) {
                      if (wasSynchronouslyLoaded) {
                        // 图片同步加载完成
                        return child;
                      }
                      return AnimatedOpacity(
                        opacity: frame == null ? 0 : 1,
                        duration: const Duration(seconds: 1),
                        curve: Curves.easeOut,
                        child: child,
                      );
                    },
                    loadingBuilder: (
                      BuildContext context,
                      Widget child,
                      ImageChunkEvent? loadingProgress,
                    ) {
                      if (loadingProgress == null) {
                        return child;
                      }
                      // 图片正在加载时的回调
                      return Center(
                        child: CircularProgressIndicator(
                          backgroundColor: Colors.blue,
                          color: Colors.red,
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                        ),
                      );
                    },
                  ),
                ),
              ),
            _buildTopCloseButton(context),
            _buildTopEditButton(context),
            _buildBottomBar(context),
          ],
        );
      }),
    ));
  }

//顶部 关闭 编辑
  Widget _buildTopCloseButton(BuildContext context) {
    final state = Get.find<SignDiyShareLogic>().state;
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeInOut,
      top: state.isVisible.value ? 40.w : -300.w,
      right: 10.w,
      child: GestureDetector(
        onTap: () {
          AppRouter.backNum(2);
        },
        child: Container(
          width: 20.w,
          height: 20.w,
          color: Colors.black.withOpacity(0.7),
          child: Icon(Icons.close, color: Colors.white, size: 15.w),
        ),
      ),
    );
  }

//顶部  编辑
  Widget _buildTopEditButton(BuildContext context) {
    final state = Get.find<SignDiyShareLogic>().state;
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeInOut,
      top: state.isVisible.value ? 80.w : -300.w,
      right: 10.w,
      child: GestureDetector(
        onTap: () {
          AppRouter.back();
        },
        child: Container(
          width: 20.w,
          height: 20.w,
          color: Colors.black.withOpacity(0.7),
          child: Icon(Icons.edit, color: Colors.white, size: 15.w),
        ),
      ),
    );
  }

  //底部灰色框
  Widget _buildBottomBar(BuildContext context) {
    final logic = Get.find<SignDiyShareLogic>();
    final state = Get.find<SignDiyShareLogic>().state;
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeInOut,
      bottom: state.isVisible.value ? 0.w : -300.w,
      left: 0,
      right: 0,
      child: Container(
        color: Colors.black.withOpacity(0.7),
        padding: EdgeInsets.only(
          left: 20.w,
          right: 20.w,
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: Transform(
          transform: Matrix4.translationValues(0, -10.w, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              GestureDetector(
                onTap: () {
                  // await logic.saveImageAsPng(state.bytes.value!);
                  logic.saveImageToGallery(state.bytes.value!, context);
                },
                child: Image.asset(
                  DNUAssets.to.images.main.download,
                  width: 40.w,
                  fit: BoxFit.cover,
                ),
              ),
              // GestureDetector(
              //   onTap: () {},
              //   child: Image.asset(
              //     DNUAssets.to.images.main.dailyShareMoments,
              //     width: 40.w,
              //     fit: BoxFit.cover,
              //   ),
              // ),
              // SizedBox(width: 20.w),
              GestureDetector(
                onTap: () {
                  logic.shareToWechat(context);
                },
                child: Image.asset(
                  DNUAssets.to.images.main.dailyShareWechat,
                  width: 40.w,
                  fit: BoxFit.cover,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
