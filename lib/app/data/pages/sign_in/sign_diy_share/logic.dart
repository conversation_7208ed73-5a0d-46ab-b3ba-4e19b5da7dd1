import 'dart:io';
import 'dart:typed_data';

import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_kit/wechat_kit.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../router/router.dart';
import '../../../../../ui/widgets/dnu_toast.dart';
import '../../../../common/service/wechat_service.dart';
import 'state.dart';

class SignDiyShareLogic extends DNUBaseController {
  final SignDiyShareState state = SignDiyShareState();

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments == null) {
      AppRouter.back();
      return;
    }

    state.bytes.value = Get.arguments;
  }

  Future<String?> saveImageAsPng(Uint8List imageByte) async {
    var tempDir = await getTemporaryDirectory();
    var filePath =
        '${tempDir.path}/temp/sign/image_${DateTime.now().millisecondsSinceEpoch}.png';
    try {
      var file = await File(filePath).create(recursive: true);
      await file.writeAsBytes(imageByte);
      return filePath;
    } catch (e) {
      Log.e("saveImageAsPng filePath: $filePath error: ", e);
    }

    return null;
  }

  Future<void> saveImageToGallery(
      Uint8List imageByte, BuildContext context) async {
    if (context.mounted) {
      DNULoading.show(context, content: '保存中...');
    }
    final result = await ImageGallerySaver.saveImage(imageByte);
    // Log.i('result----${result}');
    final resultMap = result as Map<Object?, Object?>;
    final success = (resultMap['isSuccess'] ?? false) == true;
    if (context.mounted) {
      DNULoading.dismiss(context);
      DNUToast.show(success ? "保存成功,可在相册中查看" : "保存失败", context);
    }
  }

  void shareToWechat(BuildContext context) async {
    // imageFilePath
    if (state.imageFilePath.value.isEmpty) {
      String? filePath = await saveImageAsPng(state.bytes.value!);
      if (filePath == null) {
        if (context.mounted) {
          DNUToast.show("保存日签失败", context);
        }
        return;
      }

      state.imageFilePath.value = filePath;
    }

    Log.i("shareToWechat: ${state.imageFilePath.value}");

    WechatService.to.shareImage(
      scene: WechatScene.kSession,
      url: state.imageFilePath.value,
      onSuccess: () {
        DNUToast.show("分享成功", context);
      },
      onError: (error) {
        DNUToast.show(error.message, context);
      },
    );
  }

  @override
  void onClose() {
    if (state.imageFilePath.value.isNotEmpty) {
      File(state.imageFilePath.value).delete();
    }

    super.onClose();
  }
}
