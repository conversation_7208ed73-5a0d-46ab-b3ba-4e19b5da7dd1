import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../ui/widgets/page/loading_page.dart';
import '../../../service/sign_in_service.dart';
import '../common/base_sign_in_logic.dart';
import 'state.dart';

class DailySignInLogic extends BaseSignInLogic<DailySignInState> {
  DailySignInLogic() : super(state: DailySignInState());

  @override
  void onInit() {
    super.onInit();
    state.dailyCardList.clear();

    if (Get.arguments != null) {
      List<MonthSignIn> signList = Get.arguments as List<MonthSignIn>;
      // MonthSignIn
      state.dailyCardList.addAll(signList);
      state.pageState.value = LoadingPageState.success;
    } else {
      loadLatestSignInData();
    }
    refreshController = RefreshController(initialRefresh: false);
  }

  String formatTag(String tag) {
    int year = int.parse(tag.substring(0, 4));
    int month = int.parse(tag.substring(4));
    return '$year 年 ${month.toString().padLeft(2, '0')} 月';
  }
}
