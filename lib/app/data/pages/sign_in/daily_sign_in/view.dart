import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:dnurse/ui/widgets/page/loading_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../router/routes.dart';
import '../../../../../ui/style/color.dart';
import '../../../api/dto/sign_in/sign_in_dto.dart';
import '../../../service/sign_in_service.dart';
import 'logic.dart';

class DailySignInPage extends StatelessWidget {
  const DailySignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(DailySignInLogic());
    final state = Get.find<DailySignInLogic>().state;
    return Scaffold(
      backgroundColor: AppColor.background2,
      appBar: getAppBar(
          context: context,
          title: '全部日签',
          titleColor: Colors.white,
          darkMode: true,
          backButtonColor: Colors.white,
          backgroundColor: AppColor.primaryLight),
      body: Obx(() {
        return LoadingPage(
          pageState: logic.state.pageState.value,
          child: SmartRefresher(
            enablePullDown: true,
            enablePullUp: true,
            controller: logic.refreshController,
            onLoading: logic.loadMore,
            onRefresh: logic.loadNext,
            child: CustomScrollView(
              slivers: <Widget>[
                for (var monthSignIn in state.dailyCardList)
                  SliverStickyHeader(
                    header: Container(
                        height: 30.w,
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColor.lineColor,
                              width: 1,
                            ),
                          ),
                          color: Colors.white,
                        ),
                        child: Row(
                          children: [
                            Text(
                              logic.formatTag(monthSignIn.month),
                              style: const TextStyle(color: Colors.black),
                            )
                          ],
                        )),
                    sliver: SliverPadding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 10.w,
                      ),
                      sliver: SliverGrid(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisSpacing: 10.w,
                          crossAxisSpacing: 10.w,
                          childAspectRatio: 750 / 1344,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) =>
                              _buildListItem(context, monthSignIn, index),
                          childCount: monthSignIn.signIns.length,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildListItem(BuildContext context, MonthSignIn item, int index) {
    final state = Get.find<DailySignInLogic>().state;
    SignInDTO signItem = item.signIns[item.signIns.length - 1 - index];
    return GestureDetector(
      onTap: () {
        AppRouter.push(
          Routes.singleSignIn,
          arguments: state.dailyCardList.toList(),
          parameters: {'date': signItem.day, 'from': 'all'},
        );
      },
      child: DNUImage.network(
        signItem.image,
        fit: BoxFit.cover,
        useCache: true,
      ),
    );
  }
}
