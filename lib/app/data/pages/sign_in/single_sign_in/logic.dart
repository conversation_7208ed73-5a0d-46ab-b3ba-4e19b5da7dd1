import 'package:dnurse/app/common/service/wechat_service.dart';
import 'package:dnurse/app/data/service/sign_in_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wechat_kit/wechat_kit.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import '../common/base_sign_in_logic.dart';
import 'state.dart';

class SingleSignInLogic extends BaseSignInLogic<SingleSignInState> {
  SingleSignInLogic() : super(state: SingleSignInState());

  @override
  void onInit() {
    super.onInit();
    state.dailyCardList.clear();
    state.from.value = Get.parameters['from'] ?? '';

    Log.i('from-----${state.from.value}');

    if (state.from.value == 'first') {
      loadLatestSignInData();
    } else {
      if (Get.arguments == null) {
        AppRouter.back();
        return;
      }

      List<MonthSignIn> monthsSignIn = Get.arguments as List<MonthSignIn>;
      state.dailyCardList.addAll(monthsSignIn);

      state.pageState.value = LoadingPageState.success;

      String selectedDay =
          Get.parameters['date'] ?? DateTime.now().formatDate("yyyy-MM-dd");
      int index = 0;
      bool found = false;

      for (final monthSingInItem in monthsSignIn) {
        for (final daySignInItem in monthSingInItem.signIns.reversed) {
          if (daySignInItem.day == selectedDay) {
            found = true;
            break;
          }
          index++;
        }
        if (found) {
          break;
        }
      }

      if (found) {
        pageController = PageController(initialPage: index);
      }
    }
    refreshController = RefreshController(initialRefresh: false);
  }

  void shareWechat(BuildContext context) {
    int? index = pageController.page?.toInt();
    if (index == null) {
      return;
    }

    List<String> signList = [];
    if (state.dailyCardList.isNotEmpty) {
      for (final monthSign in state.dailyCardList) {
        for (final signItem in monthSign.signIns.reversed) {
          signList.add(signItem.image);
        }
      }
    }

    String imageUrl = signList[index];
    Log.i("shareWechat: $imageUrl");

    WechatService.to.shareImage(
      url: imageUrl,
      scene: WechatScene.kTimeline,
      onSuccess: () {
        DNUToast.show("分享成功", context);
      },
      onError: (error) {
        DNUToast.show(error.message, context);
      },
    );
  }
}
