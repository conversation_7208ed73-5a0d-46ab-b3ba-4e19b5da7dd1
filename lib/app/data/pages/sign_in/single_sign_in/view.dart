import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../resource/dnu_assets.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import 'logic.dart';

class SingleSignInPage extends StatelessWidget {
  const SingleSignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SingleSignInLogic>();
    final state = Get.find<SingleSignInLogic>().state;
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(() {
        List<Widget> signList = [];
        if (state.dailyCardList.isNotEmpty) {
          for (final monthSign in state.dailyCardList) {
            for (final signItem in monthSign.signIns.reversed) {
              signList.add(Center(
                child: DNUImage.network(
                  signItem.image,
                  fit: BoxFit.cover,
                  useCache: true,
                ),
              ));
            }
          }
        }
        return LoadingPage(
          pageState: logic.state.pageState.value,
          child: Stack(
            children: [
              SizedBox(
                height: double.infinity,
                width: double.infinity,
                child: GestureDetector(
                  onTap: () {
                    state.isVisible.value = !state.isVisible.value;
                  },
                  child: SmartRefresher(
                    enablePullDown: true,
                    enablePullUp: true,
                    controller: logic.refreshController,
                    scrollDirection: Axis.horizontal,
                    reverse: true,
                    header: const WaterDropHeader(),
                    // footer: _buildFooter(context),
                    onRefresh: () async {
                      logic.loadNext();
                    },
                    onLoading: () {
                      logic.loadMore();
                    },
                    child: CustomScrollView(
                      physics: const PageScrollPhysics(),
                      controller: logic.pageController,
                      slivers: <Widget>[
                        SliverFillViewport(
                          delegate: SliverChildListDelegate(
                            signList,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              state.from.value == 'first' ? topBar2(context) : topBar(context),
              bottomBar(context)
            ],
          ),
        );
      }),
    );
  }

//顶部返回滑动框
  Widget topBar(BuildContext context) {
    final state = Get.find<SingleSignInLogic>().state;
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      top: state.isVisible.value ? 10.w : -300,
      right: 10.w,
      child: Container(
          color: Colors.black,
          padding: EdgeInsets.only(top: 30.w, right: 20.w),
          child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            GestureDetector(
                onTap: () {
                  AppRouter.back();
                },
                child: const Text('返回', style: TextStyle(color: Colors.white)))
          ])),
    );
  }

  //顶部打开全部日签 关闭 编辑页
  Widget topBar2(BuildContext context) {
    final state = Get.find<SingleSignInLogic>().state;
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 1500),
      curve: Curves.linear,
      top: state.isVisible.value ? 0 : -300,
      right: 20.w,
      child: Container(
          // width: 30.w,
          color: Colors.black.withOpacity(0.7),
          padding: EdgeInsets.only(
            top: 40.w,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  AppRouter.back();
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              GestureDetector(
                  onTap: () {
                    AppRouter.replace(Routes.dailySignIn,
                        arguments: state.dailyCardList.toList());
                  },
                  child: const Icon(Icons.calendar_month, color: Colors.white)),
              SizedBox(
                height: 10.w,
              ),
              GestureDetector(
                  onTap: () {
                    AppRouter.replace(Routes.signEdit);
                  },
                  child: const Icon(Icons.edit_calendar, color: Colors.white)),
            ],
          )),
    );
  }

//底部滑动框
  Widget bottomBar(BuildContext context) {
    final state = Get.find<SingleSignInLogic>().state;

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      bottom:
          state.isVisible.value ? MediaQuery.of(context).padding.bottom : -300,
      left: 0,
      right: 0,
      child: Container(
        color: Colors.black.withOpacity(0.6),
        padding: EdgeInsets.only(
          left: 20.w,
          right: 20.w,
          top: 10.w,
          bottom: 20.w,
        ),
        child: Row(children: [
          const Text(
            '分享:',
            style: TextStyle(color: Colors.white),
          ),
          // SizedBox(width: 150.w),
          // GestureDetector(
          //   onTap: () {},
          //   child: Image.asset(
          //     DNUAssets.to.images.main.dailyShareMoments,
          //     width: 40.w,
          //     fit: BoxFit.cover,
          //   ),
          // ),
          const Spacer(),
          SizedBox(width: 20.w),
          GestureDetector(
            onTap: () {
              Get.find<SingleSignInLogic>().shareWechat(context);
            },
            child: Image.asset(
              DNUAssets.to.images.main.dailyShareWechat,
              width: 40.w,
              fit: BoxFit.cover,
            ),
          )
        ]),
      ),
    );
  }

// Widget _buildFooter(BuildContext context) {
//   return CustomFooter(
//     builder: (BuildContext context, LoadStatus? mode) {
//       return SizedBox(
//         height: 80.w,
//         child: Center(
//             child: RichText(
//           textDirection: TextDirection.rtl,
//           text: TextSpan(
//             children: [
//               for (int i = 0; i < '已是最后一张日签'.length; i++)
//                 TextSpan(
//                   text: '${'已是最后一张日签'[i]}\n',
//                   style: const TextStyle(color: AppColor.textDesc),
//                 ),
//             ],
//           ),
//           // child: Text('footer',
//           //     style: TextStyle(color: Colors.white)),
//         )),
//       );
//     },
//   );
// }
}
