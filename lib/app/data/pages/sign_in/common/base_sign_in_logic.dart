/// FileName: base_sign_in_logic
///
/// @Author: ygc
/// @Date: 2024/10/28 18:51
/// @Description:
import 'package:dnurse/app/data/service/sign_in_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/router/router.dart';
import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../framework/route/base_controller.dart';
import '../../../../../framework/utils/log.dart';
import '../../../../../ui/widgets/page/loading_page.dart';
import 'base_sign_in_state.dart';

abstract class BaseSignInLogic<T extends BaseSignInState>
    extends DNUBaseController {
  BaseSignInLogic({required this.state});

  final T state;

  PageController pageController = PageController(initialPage: 0);
  late RefreshController refreshController;

  void loadLatestSignInData() async {
    DateTime now = DateTime.now();
    String today = now.startOfDay().secondsSinceEpoch.toString();

    _loadSignInData(today, isInit: true);
  }

  void _loadSignInData(
    String day, {
    bool isLoading = true,
    bool isInit = false,
  }) async {
    try {
      List<MonthSignIn> monthsSignIn =
          await SignInService.to.loadDailyCardData(day);
      // Log.i('_loadSignInData len-----${monthsSignIn.length}');

      if (monthsSignIn.isEmpty) {
        if (!isInit) {
          AppRouter.back();
          return;
        } else {}
      }
      if (isLoading || isInit) {
        state.dailyCardList.addAll(monthsSignIn.reversed);
        if (isLoading) {
          refreshController.loadComplete();
        }
      } else {
        state.dailyCardList.insertAll(0, monthsSignIn.reversed);
        refreshController.refreshCompleted();
      }
      if (state.pageState.value != LoadingPageState.success) {
        state.pageState.value = LoadingPageState.success;
      }
    } catch (e) {
      AppRouter.back();
    }
  }

  void loadMore() async {
    String lastMonth = state.dailyCardList.last.month;

    Log.i('loadMore---$lastMonth');
    DateTime month = DateTime.parse("${lastMonth}01");
    DateTime previousMonth = month.addMonth(-1);

    _loadSignInData(previousMonth.secondsSinceEpoch.toString(),
        isLoading: true);
  }

  void loadNext() async {
    String? firstDay = state.firstDayOfList;
    if (firstDay == null) {
      refreshController.refreshCompleted();
      return;
    }
    DateTime now = DateTime.now();
    String today = now.formatDate("yyyyMMdd");
    if (firstDay == today) {
      refreshController.refreshCompleted();
      return;
    }

    String firstMonth = state.dailyCardList.first.month;

    DateTime month = DateTime.parse("${firstMonth}01");
    DateTime previousMonth = month.addMonth(1);

    _loadSignInData(previousMonth.secondsSinceEpoch.toString(),
        isLoading: false);
  }

  @override
  void dispose() {
    pageController.dispose();
    refreshController.dispose();
    super.dispose();
  }
}
