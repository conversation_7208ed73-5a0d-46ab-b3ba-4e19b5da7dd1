/// FileName: sign_in_state
///
/// @Author: ygc
/// @Date: 2024/10/28 18:49
/// @Description:
import 'package:get/get.dart';

import '../../../../../ui/widgets/page/loading_page.dart';
import '../../../service/sign_in_service.dart';

class BaseSignInState {
  final pageState = LoadingPageState.loading.obs;

  final dailyCardList = <MonthSignIn>[].obs;

  String? get firstDayOfList {
    if (dailyCardList.isNotEmpty) {
      return null;
    }

    return dailyCardList.first.signIns.last.day;
  }

  String? get lastDayOfList {
    if (dailyCardList.isNotEmpty) {
      return null;
    }

    return dailyCardList.last.signIns.first.day;
  }
}
