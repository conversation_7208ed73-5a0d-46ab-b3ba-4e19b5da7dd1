import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/entity/day_calories_info.dart';
import 'package:dnurse/app/data/pages/food_record/food_record_first/widget/food_calendar_cell_widget.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../resource/dnu_assets.dart';
import '../../../../../router/router.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/icons/icons.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/style/theme.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../../ui/widgets/progress/animated_circular_progress.dart';
import '../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../../common/food_type.dart';
import '../../../entity/data_food.dart';
import '../../../entity/data_food_item.dart';
import 'logic.dart';

class FoodRecordFirstPage extends StatelessWidget {
  const FoodRecordFirstPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
        context: context,
        title: '美食记',
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Center(
            child: InkWell(
              onTap: () {
                AppRouter.push(Routes.foodParameter);
              },
              child: Text('食物热量',
                  style: TextStyle(
                      fontSize: 16.sp, color: AppColor.textSecondary)),
            ),
          )
        ],
        bottom: PreferredSize(
          preferredSize: Size(Get.width, 100.w),
          child: _getCalendar(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // _buildMonitorCalender(context),
            // _getCalendar(),
            _buildCard(context),
          ],
        ),
      ),
    );
  }

  Widget _getCalendar() {
    final controller = Get.find<FoodRecordFirstLogic>();
    return Obx(() {
      return Container(
        // color: Colors.white,
        padding: EdgeInsets.symmetric(
          vertical: 10.w,
        ),
        child: TableCalendar(
          focusedDay: controller.state.focusDay.value,
          firstDay: DateTime(2012, 1, 1),
          lastDay: DateTime.now(),
          startingDayOfWeek: StartingDayOfWeek.monday,
          availableCalendarFormats: const {CalendarFormat.week: 'Week'},
          headerVisible: false,
          daysOfWeekVisible: false,
          calendarFormat: CalendarFormat.week,
          rowHeight: 100.w,
          selectedDayPredicate: (day) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            day = day.subtract(timeZoneOffset).toLocal();
            return isSameDay(controller.state.selectedDay.value, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            selectedDay = selectedDay.subtract(timeZoneOffset).toLocal();
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();
            // controller.loadSolution(selectedDay.startOfDay());
            controller.loadData(selectedDay.startOfDay());
            if (!isSameDay(controller.state.selectedDay.value, selectedDay)) {
              controller.state.selectedDay.value = selectedDay.startOfDay();
              controller.state.focusDay.value = focusedDay.startOfDay();
            }
          },
          onPageChanged: (focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();
            controller.state.focusDay.value = focusedDay;
            // DnuDateTime.fromDateTime(focusedDay);
            // controller.state.focusDay.value = focusedDay;
            controller.setEvent();
          },
          calendarBuilders:
              CalendarBuilders(selectedBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: true,
            );
          }, todayBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: false,
            );
          }, disabledBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }, outsideBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }, defaultBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }),
        ),
      );
    });
  }

  Widget _buildCalendarCellWidget({
    required DateTime day,
    bool focused = false,
    bool selected = false,
    bool disabled = false,
  }) {
    return Obx(() {
      final logic = Get.find<FoodRecordFirstLogic>();
      Color textColor = AppColor.textPrimary;
      if (disabled) {
        textColor = AppColor.textDisable;
      } else {
        if (selected) {
          textColor = AppColor.primary;
        } else {
          textColor = AppColor.textSecondary;
        }
      }

      var timeZoneOffset = DateTime.now().timeZoneOffset;
      DateTime dayLocal = day.subtract(timeZoneOffset).toLocal();

      DayCaloriesInfo? dayCaloriesInfo = logic.state.dayIntakeStatus[dayLocal];
      Color? statusColor;
      if (dayCaloriesInfo != null) {
        switch (dayCaloriesInfo.status) {
          case IntakeStatus.normal:
            statusColor = const Color(0xFFA0D486);
            break;
          case IntakeStatus.low:
            statusColor = const Color(0xFFF07A7A);
            break;
          case IntakeStatus.high:
            statusColor = const Color(0xFFF07A7A);
            break;
        }
      }

      return FoodCalendarCellWidget(
        day: day,
        focused: focused,
        textColor: textColor,
        statusColor: statusColor,
      );
    });
  }

  Widget _buildCard(BuildContext context) {
    return Container(
      color: AppColor.primaryLightOpacity6,

      // decoration:
      //     const BoxDecoration(gradient: AppColor.primaryBackgroundLight20),
      child: Obx(() {
        final logic = Get.find<FoodRecordFirstLogic>();
        return Column(
          children: [
            _buildDataCard(context),
            _buildDietCard(
              context,
              FoodType.breakfast,
              DNUAssets.to.images.data.breakfast,
              logic.state.breakfastRecommendCalorieIntake,
              logic.state.breakfastFoods.value,
            ),
            if (logic.state.extraBreakfastFoods.value != null &&
                logic.state.extraBreakfastFoods.value!.isNotEmpty)
              _buildDietCard(
                context,
                FoodType.extraBreakfast,
                DNUAssets.to.images.data.breakfast,
                0,
                logic.state.extraBreakfastFoods.value,
              ),
            _buildDietCard(
              context,
              FoodType.lunch,
              DNUAssets.to.images.data.lunch,
              logic.state.lunchRecommendCalorieIntake,
              logic.state.lunchFoods.value,
            ),
            if (logic.state.extraLunchFoods.value != null &&
                logic.state.extraLunchFoods.value!.isNotEmpty)
              _buildDietCard(
                context,
                FoodType.extraLunch,
                DNUAssets.to.images.data.lunch,
                0,
                logic.state.extraLunchFoods.value,
              ),
            _buildDietCard(
              context,
              FoodType.supper,
              DNUAssets.to.images.data.dinner,
              logic.state.supperRecommendCalorieIntake,
              logic.state.supperFoods.value,
            ),
            if (logic.state.extraSupperFoods.value != null &&
                logic.state.extraSupperFoods.value!.isNotEmpty)
              _buildDietCard(
                context,
                FoodType.extraSupper,
                DNUAssets.to.images.data.dinner,
                0,
                logic.state.extraSupperFoods.value,
              ),
            if (logic.state.hasExtra)
              _buildDietCard(
                context,
                FoodType.extra,
                DNUAssets.to.images.data.snack,
                0,
                logic.state.extraFoods.value,
              ),
          ],
        );
      }),
    );
  }

  Widget _buildDataCard(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: 240.w,
      ),
      decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: [Colors.white, Colors.white.withOpacity(0)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter)),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
      child: IntrinsicHeight(
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildGlucoseTotal(context),
            _buildCalorieProgress(context),
            _buildPurineTotal(context),
          ],
        ),
      ),
    );
  }

  //总升糖负载
  Widget _buildGlucoseTotal(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Obx(() {
          final logic = Get.find<FoodRecordFirstLogic>();
          return Text(
            logic.state.glycemicLoad.value > 0
                ? "${logic.state.glycemicLoad.value}"
                : "--",
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.textPrimary,
            ),
          );
        }),
        Text(
          '总升糖负载',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.textSecondary,
          ),
        ),
        Text(
          '（GI×碳水×重量）',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColor.textDesc,
          ),
        ),
        SizedBox(
          height: 20.w,
        ),
      ],
    );
  }

//总嘌呤负载
  Widget _buildPurineTotal(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Obx(() {
          final logic = Get.find<FoodRecordFirstLogic>();
          return Text(
            logic.state.glycemicLoad.value > 0
                ? "${logic.state.purineLoad.value}"
                : "--",
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.textPrimary,
            ),
          );
        }),
        Text(
          '总嘌呤负载',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.textSecondary,
          ),
        ),
        Text(
          '（嘌呤×重量）',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColor.textDesc,
          ),
        ),
        SizedBox(
          height: 20.w,
        ),
      ],
    );
  }

  //还可以吃多少千卡
  Widget _buildCalorieProgress(BuildContext context) {
    return Expanded(
      // width: 165.w,
      // height: 145.w,
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(children: [
          Obx(() {
            final logic = Get.find<FoodRecordFirstLogic>();
            return AnimatedCircularProgressWidget(
              value: logic.state.takePercentage,
              backgroundStrokeWidth: 15.w,
              progressbarStrokeWidth: 15.w,
              gradient: AppColor.gradientRedOrange,
              duration: const Duration(milliseconds: 300),
            );
          }),
          _buildValue(context)
        ]),
      ),
    );
  }

  Widget _buildValue(BuildContext context) {
    final logic = Get.find<FoodRecordFirstLogic>();
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '还可以吃',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.textPrimary,
            ),
          ),
          Obx(() {
            return Text(
              '${logic.state.canCalorieIntake}',
              style: TextStyle(
                fontSize: 40.sp,
                color: AppColor.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            );
          }),
          Text('千卡',
              style: TextStyle(fontSize: 12.sp, color: AppColor.textDesc)),
        ],
      ),
    );
  }

  // 饮食卡片
  Widget _buildDietCard(
    BuildContext context,
    FoodType foodType,
    String iconImage,
    int suggestedCal,
    DataFood? dataFood,
  ) {
    final logic = Get.find<FoodRecordFirstLogic>();
    int sumCal = dataFood?.calorie ?? 0;
    bool hasFood = dataFood != null && dataFood.items.isNotEmpty;
    int mapIndex = 0;
    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radius),
        boxShadow: [
          BoxShadow(
            color: const Color(0x143947E5),
            blurRadius: 10.w,
            offset: Offset(0, 4.w),
          )
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50.w,
                height: 50.w,
                decoration: !hasFood
                    ? BoxDecoration(
                        borderRadius: BorderRadius.circular(20.w),
                        color: AppColor.textDisable,
                      )
                    : BoxDecoration(
                        borderRadius: BorderRadius.circular(20.w),
                        //添加食物项后
                        gradient: AppColor.gradientBluePurple),
                child: Center(
                  child: Image.asset(
                    iconImage,
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        foodType.title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (dataFood?.time != null)
                        InkWell(
                          onTap: () {
                            _editTime(context, dataFood);
                          },
                          child: Padding(
                            padding: EdgeInsets.only(left: 8.w),
                            child: Text(
                              dataFood!.time.formatDate("HH:mm"),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColor.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (suggestedCal > 0)
                    Text(
                      '建议摄入量$suggestedCal千卡',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColor.textDesc,
                      ),
                    ),
                ],
              ),
              const Spacer(),
              !hasFood
                  ? (logic.state.canAdd
                      ? InkWell(
                          onTap: () {
                            final logic = Get.find<FoodRecordFirstLogic>();
                            logic.gotoAddFood(foodType, dataFood, context);
                          },
                          child: Container(
                            width: 24.w,
                            height: 24.w,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.w),
                                color: AppColor.auxiliaryPurple2),
                            child: Icon(
                              Icons.add,
                              size: 20.w,
                              color: Colors.white,
                            ),
                          ),
                        )
                      : Container())
                  :
                  //添加食物项后
                  Column(
                      children: [
                        Text(
                          '$sumCal',
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: AppColor.textPrimary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '千卡',
                          style: TextStyle(
                              fontSize: 12.sp, color: AppColor.textDesc),
                        ),
                      ],
                    )
            ],
          ),
          if (hasFood)
            ...dataFood.items.map((item) {
              return _buildDietItem(
                context,
                dataFood,
                item,
                mapIndex++,
              );
            }),
          if (hasFood && logic.state.canAdd)
            Container(
              margin: EdgeInsets.only(top: 10.w),
              child: Column(children: [
                Divider(height: 1.w, color: AppColor.textDisable),
                InkWell(
                  onTap: () {
                    final logic = Get.find<FoodRecordFirstLogic>();
                    logic.gotoAddFood(foodType, dataFood, context);
                  },
                  child: Container(
                    padding:
                        EdgeInsets.only(top: 15.w, left: 10.w, right: 10.w),
                    child: Row(
                      children: [
                        Container(
                          width: 24.w,
                          height: 24.w,
                          margin: EdgeInsets.only(right: 10.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12.w),
                              color: AppColor.auxiliaryPurple2),
                          child: Icon(
                            Icons.add,
                            size: 20.w,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          '添加',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColor.primaryLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ]),
            )
        ],
      ),
    );
  }

  Widget _buildDietItem(BuildContext context, DataFood dataFood,
      DataFoodItem foodItem, int index) {
    return Container(
      padding: EdgeInsets.only(top: 8.w, left: 12.w, right: 15.w, bottom: 8.w),
      margin: EdgeInsets.only(left: 60.w, top: 13.w),
      decoration: BoxDecoration(
        color: AppColor.background3,
        borderRadius: BorderRadius.circular(AppTheme.radius),
      ),
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                foodItem.name,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColor.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${foodItem.amount}${foodItem.weightUnit.name}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColor.textSecondary,
                ),
              ),
            ],
          ),
          const Spacer(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text('${foodItem.calorie}',
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: AppColor.textPrimary,
                    fontWeight: FontWeight.w500,
                  )),
              Text(
                '千卡',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.textSecondary,
                ),
              ),
              SizedBox(
                width: 7.w,
              ),
              InkWell(
                onTap: () {
                  final logic = Get.find<FoodRecordFirstLogic>();
                  logic.onDeleteFood(dataFood, foodItem, context, index);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    DNUIconFont.shanchu,
                    size: 20.w,
                    color: AppColor.textDesc,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _editTime(BuildContext context, DataFood dataFood) {
    DateSheetView.show(
      context: context,
      title: '请选择用餐时间',
      initialDateTime: dataFood.time,
      pickerMode: BrnDateTimePickerMode.time,
      dateFormat: "HH:mm",
      // 设置时间格式
      onConfirm: (dateTime, selectedIndex) {
        Log.i("用餐时间: $dateTime");
        DateTime newDateTime = DateTime(
          dataFood.time.year,
          dataFood.time.month,
          dataFood.time.day,
          dateTime.hour,
          dateTime.minute,
        );
        dataFood.time = newDateTime;

        final logic = Get.find<FoodRecordFirstLogic>();
        logic.updateFood(dataFood);
      },
    );
  }
}
