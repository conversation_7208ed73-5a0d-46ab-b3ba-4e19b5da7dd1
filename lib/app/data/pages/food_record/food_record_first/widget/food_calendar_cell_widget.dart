/// FileName: food_calendar_cell_widget
///
/// @Author: ygc
/// @Date: 2025/7/4 15:36
/// @Description:
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/style/font.dart';

class FoodCalendarCellWidget extends StatelessWidget {
  const FoodCalendarCellWidget({
    super.key,
    required this.day,
    required this.focused,
    this.statusColor,
    this.textColor,
  });

  final DateTime day;
  final bool focused;
  final Color? statusColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    List<String> weeks = [
      "一",
      "二",
      "三",
      "四",
      "五",
      "六",
      "日",
    ];
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12.w,
      ),
      width: 40.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.w),
        color: focused
            ? AppColor.primaryOpacity10
            : AppColor.primaryBackgroundLight6,
      ),
      child: Column(
        children: [
          Text(
            weeks[day.weekday - 1],
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: textColor ?? AppColor.textSecondary,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                day.day.toString(),
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w400,
                  color: textColor ?? AppColor.textSecondary,
                  fontFamily: AppFont.ranyBold,
                ),
              ),
            ),
          ),
          Container(
            height: 15.w,
            width: 15.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: statusColor ?? AppColor.textDisable,
            ),
          ),
        ],
      ),
    );
  }
}
