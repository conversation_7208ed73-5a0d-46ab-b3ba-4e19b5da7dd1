import 'package:dnurse/app/data/entity/day_calories_info.dart';
import 'package:dnurse/app/user/service/user_info_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../../user/utils/health_utils.dart';
import '../../../entity/data_food.dart';

class FoodRecordFirstState {
  FoodRecordFirstState() {
    ///Initialize variables
  }

  // final today = DateTime.now().startOfDay().obs;

  final selectedDay = DateTime.now().startOfDay().obs;
  final focusDay = DateTime.now().obs;

  final breakfastFoods = Rx<DataFood?>(null);
  final extraBreakfastFoods = Rx<DataFood?>(null);

  final lunchFoods = Rx<DataFood?>(null);
  final extraLunchFoods = Rx<DataFood?>(null);
  final supperFoods = Rx<DataFood?>(null);
  final extraSupperFoods = Rx<DataFood?>(null);

  final extraFoods = Rx<DataFood?>(null);
  final glycemicLoad = 0.obs;
  final purineLoad = 0.obs;
  final dayIntakeStatus = RxMap<DateTime, DayCaloriesInfo?>({});

  int get recommendCalorieIntake {
    return HealthUtils.getCalorieIntake(
            UserInfoService.to.currentUserInfo.value)
        .toInt();
  }

  int get canCalorieIntake {
    int total = (breakfastFoods.value?.calorie.toInt() ?? 0) +
        (lunchFoods.value?.calorie.toInt() ?? 0) +
        (supperFoods.value?.calorie.toInt() ?? 0) +
        (extraFoods.value?.calorie.toInt() ?? 0);

    int leftCal = recommendCalorieIntake - total;
    return leftCal < 0 ? 0 : leftCal;
  }

  double get takePercentage {
    if (recommendCalorieIntake == 0) {
      return 1;
    }

    return (recommendCalorieIntake - canCalorieIntake) / recommendCalorieIntake;
  }

  int get breakfastRecommendCalorieIntake {
    return (recommendCalorieIntake * 0.2).toInt();
  }

  int get lunchRecommendCalorieIntake {
    return (recommendCalorieIntake * 0.4).toInt();
  }

  int get supperRecommendCalorieIntake {
    return recommendCalorieIntake -
        breakfastRecommendCalorieIntake -
        lunchRecommendCalorieIntake;
  }

  bool get hasExtra {
    return !(extraBreakfastFoods.value != null &&
        extraBreakfastFoods.value!.isNotEmpty &&
        extraLunchFoods.value != null &&
        extraLunchFoods.value!.isNotEmpty &&
        extraSupperFoods.value != null &&
        extraSupperFoods.value!.isNotEmpty);
  }

  bool get canAdd {
    return true;
    final now = DateTime.now();
    return selectedDay.value.day == now.day &&
        selectedDay.value.month == now.month &&
        selectedDay.value.year == now.year;
  }
}
