import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/entity/day_calories_info.dart';
import 'package:dnurse/app/data/pages/food_record/add_food/state.dart';
import 'package:dnurse/app/data/service/food_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:flutter/material.dart';

import '../../../../../router/router.dart';
import '../../../../../router/routes.dart';
import '../../../../common/entity/unit.dart';
import '../../../common/data_utils.dart';
import '../../../common/food_type.dart';
import '../../../db/model/storage/storage_model.dart';
import '../../../entity/data_food.dart';
import '../../../entity/data_food_item.dart';
import '../../../service/extra_data_service.dart';
import 'state.dart';

class FoodRecordFirstLogic extends DNUBaseController {
  final FoodRecordFirstState state = FoodRecordFirstState();

  @override
  void onInit() {
    super.onInit();

    _init();
  }

  void _init() async {
    await loadData(state.selectedDay.value);
    await _loadCalories(state.selectedDay.value);
  }

  void reloadData() {}

  void onShow() {}

  Future _loadCalories(DateTime date) async {
    int weekDay = date.weekday;

    DateTime firstDayOfWeek =
        state.focusDay.value.subtract(Duration(days: weekDay - 1));
    firstDayOfWeek = firstDayOfWeek.startOfDay();
    DateTime lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 7));

    for (var i = 0; i < 7; i++) {
      DateTime day = firstDayOfWeek.add(Duration(days: i));
      state.dayIntakeStatus[day] = null;
    }

    int recommendCal = state.recommendCalorieIntake;

    List<DayCaloriesInfo> dayCaloriesInfoList = await FoodService.to
        .queryIntakeCaloriesByDay(
            AppContext.to.userSn, firstDayOfWeek, lastDayOfWeek);
    for (var dayCaloriesInfo in dayCaloriesInfoList) {
      IntakeStatus status = IntakeStatus.normal;
      if (dayCaloriesInfo.calories > recommendCal) {
        status = IntakeStatus.high;
      } else if (dayCaloriesInfo.calories < recommendCal) {
        status = IntakeStatus.low;
      }
      dayCaloriesInfo.status = status;

      state.dayIntakeStatus[dayCaloriesInfo.date] = dayCaloriesInfo;
    }
  }

  Future setEvent() async {
    _loadCalories(state.focusDay.value);
  }

  Future loadData(DateTime date) async {
    List<DataFood> foodLists = await FoodService.to.getFoodByTimeType(
      AppContext.to.userSn,
      date,
      date.add(const Duration(days: 1)),
    );

    state.breakfastFoods.value = null;
    state.lunchFoods.value = null;
    state.supperFoods.value = null;
    state.extraFoods.value = null;
    state.extraBreakfastFoods.value = null;
    state.extraLunchFoods.value = null;
    state.extraSupperFoods.value = null;
    state.extraFoods.value = null;

    for (var item in foodLists) {
      if (item.foodType == FoodType.breakfast) {
        if (state.breakfastFoods.value == null) {
          state.breakfastFoods.value = item;
        } else {
          state.breakfastFoods.value!.items.addAll(item.items);
          state.breakfastFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.lunch) {
        if (state.lunchFoods.value == null) {
          state.lunchFoods.value = item;
        } else {
          state.lunchFoods.value!.items.addAll(item.items);
          state.lunchFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.supper) {
        if (state.supperFoods.value == null) {
          state.supperFoods.value = item;
        } else {
          state.supperFoods.value!.items.addAll(item.items);
          state.supperFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.extra) {
        if (state.extraFoods.value == null) {
          state.extraFoods.value = item;
        } else {
          state.extraFoods.value!.items.addAll(item.items);
          state.extraFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.extraBreakfast) {
        if (state.extraBreakfastFoods.value == null) {
          state.extraBreakfastFoods.value = item;
        } else {
          state.extraBreakfastFoods.value!.items.addAll(item.items);
          state.extraBreakfastFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.extraLunch) {
        if (state.extraLunchFoods.value == null) {
          state.extraLunchFoods.value = item;
        } else {
          state.extraLunchFoods.value!.items.addAll(item.items);
          state.extraLunchFoods.value!.calorie += item.calorie;
        }
      } else if (item.foodType == FoodType.extraSupper) {
        if (state.extraSupperFoods.value == null) {
          state.extraSupperFoods.value = item;
        } else {
          state.extraSupperFoods.value!.items.addAll(item.items);
          state.extraSupperFoods.value!.calorie += item.calorie;
        }
      }
    }

    _calculateLoad(foodLists);
  }

  void _calculateLoad(List<DataFood> foodLists) async {
    double glycemicLoad = 0;
    double purineLoad = 0;

    RegExp regex = RegExp(r'(\d+\.\d+)|(\d+)');

    for (var dataFood in foodLists) {
      for (var foodItem in dataFood.items) {
        List<StorageModel> storageModels =
            await ExtraDataService.to.getFoodByName(foodItem.name);
        if (storageModels.isEmpty) {
          continue;
        }
        StorageModel storageModel = storageModels.first;
        if (storageModel.did < 0 || storageModel.classValue == '自定义食物') {
          continue;
        }

        num amount = foodItem.amount;
        if (foodItem.weightUnit != Unit.gram) {
          List<UnitInfo> unitInfoList = UnitInfo.getUnitInfo(storageModel.unit);
          for (var unitInfo in unitInfoList) {
            if (unitInfo.unit == foodItem.weightUnit) {
              amount = unitInfo.kilogram * amount;
              break;
            }
          }
        }

        if (storageModel.gi.isNotEmpty &&
            storageModel.carbohydrate.isNotEmpty) {
          final giMatches = regex.allMatches(storageModel.gi);
          final carbohydrateMatches =
              regex.allMatches(storageModel.carbohydrate);
          if (giMatches.isNotEmpty && carbohydrateMatches.isNotEmpty) {
            double giNum = double.tryParse(giMatches.first.group(0)!) ?? 0;
            double carbohydrateNum =
                double.tryParse(carbohydrateMatches.first.group(0)!) ?? 0;

            double fSugar = giNum * carbohydrateNum * amount / 10000;
            glycemicLoad += fSugar;
          }
        }

        if (storageModel.purine.isNotEmpty) {
          final purineMatches = regex.allMatches(storageModel.purine);
          if (purineMatches.isNotEmpty) {
            double purine = double.tryParse(purineMatches.first.group(0)!) ?? 0;

            double fPurine = purine * amount / 100;
            purineLoad += fPurine;
          }
        }
      }
    }

    state.glycemicLoad.value = glycemicLoad.toInt();
    state.purineLoad.value = purineLoad.toInt();
  }

  void onDeleteFood(
    DataFood food,
    DataFoodItem foodItem,
    BuildContext context,
    int index,
  ) {
    DNUDialog.showConfirm(
      title: "删除食物",
      content: "确定要删除 \"${food.foodType.title}  ${foodItem.name}\" 吗？",
      onConfirm: () async {
        _doDeleteFood(food, index);
      },
    );
  }

  void _doDeleteFood(DataFood food, int index) async {
    food.items.removeAt(index);
    food.calorie = food.items.fold(0, (previousValue, element) {
      return previousValue + element.calorie;
    });

    await FoodService.to.saveFood(AppContext.to.userSn, food);
    await loadData(state.selectedDay.value);
    await _loadCalories(state.selectedDay.value);
  }

  void _addExtraFood(BuildContext context) {
    List<FoodType> foodTypes = [];
    if (state.extraBreakfastFoods.value == null ||
        state.extraBreakfastFoods.value!.isEmpty) {
      foodTypes.add(FoodType.extraBreakfast);
    }

    if (state.extraLunchFoods.value == null ||
        state.extraLunchFoods.value!.isEmpty) {
      foodTypes.add(FoodType.extraLunch);
    }

    if (state.extraSupperFoods.value == null ||
        state.extraSupperFoods.value!.isEmpty) {
      foodTypes.add(FoodType.extraSupper);
    }

    // final DataDrug drug = logic.state.drugList[index];

    List<BrnCommonActionSheetItem> actions = foodTypes
        .map((e) => BrnCommonActionSheetItem(
              e.title,
            ))
        .toList();

    // 展示actionSheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return BrnCommonActionSheet(
          title: "选择餐别",
          actions: actions,
          clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
            Future.delayed(const Duration(milliseconds: 1), () {
              DataFood? dataFood;
              final foodType = foodTypes[index];
              if (foodType == FoodType.extraBreakfast) {
                dataFood = state.extraBreakfastFoods.value;
              } else if (foodType == FoodType.extraLunch) {
                dataFood = state.extraLunchFoods.value;
              } else if (foodType == FoodType.extraSupper) {
                dataFood = state.extraSupperFoods.value;
              }

              gotoAddFood(foodType, dataFood, context);
            });
          },
        );
      },
    );
  }

  void gotoAddFood(
      FoodType foodType, DataFood? dataFood, BuildContext context) {
    if (foodType == FoodType.extra) {
      _addExtraFood(context);
      return;
    }

    AppRouter.push(
      Routes.addFood,
      parameters: {
        "foodType": foodType.id.toString(),
      },
    )?.then((result) {
      if (result != null && result is List<FoodItem>) {
        _addFood(foodType, dataFood, result);
        // loadData(state.selectedDay.value);
        // _loadCalories(state.selectedDay.value);
      }
    });
  }

  void _addFood(
      FoodType foodType, DataFood? dataFood, List<FoodItem> foodList) async {
    String sn = AppContext.to.userSn;
    int calorie = 0;

    List<DataFoodItem> items = foodList.map((item) {
      calorie += item.calorie;
      return DataFoodItem(
        name: item.storageModel.name,
        calorie: item.calorie,
        amount: item.selectNum,
        weightUnit: item.unit!,
        fromUser: false,
        imgUrl: item.storageModel.imageUrl,
      );
    }).toList();
    if (dataFood == null) {
      final now = DateTime.now();

      dataFood = DataFood(
        did: DataUtils.generateDidWithPrefix('F'),
        time: DateTime(
          state.selectedDay.value.year,
          state.selectedDay.value.month,
          state.selectedDay.value.day,
          now.hour,
          now.minute,
        ),
        sn: sn,
        calorie: calorie,
        foodType: foodType,
        items: items,
      );
    } else {
      dataFood.items.addAll(items);
      dataFood.calorie += calorie;
    }

    await FoodService.to.saveFood(sn, dataFood);
    await loadData(state.selectedDay.value);
    await _loadCalories(state.selectedDay.value);
  }

  void updateFood(DataFood food) async {
    await FoodService.to.saveFood(AppContext.to.userSn, food);
    await loadData(state.selectedDay.value);
    await _loadCalories(state.selectedDay.value);
  }
}
