import 'package:dnurse/app/data/pages/food_record/add_food/state.dart';
import 'package:get/get.dart';

import '../../../../../ui/widgets/page/loading_page.dart';

class SearchFoodState {
  SearchFoodState() {
    ///Initialize variables
  }

  final queryText = ''.obs;
  final pageState = LoadingPageState.loading.obs;

  final selCalorieSum = 0.obs;
  final selectFoodList = <FoodItem>[].obs;

  final searchHistory = <String>[].obs;

  // final searchCustomList = <String>[].obs;

  final showDelete = false.obs;

  final tabIndex = 0.obs;

  final foodList = <FoodItem>[].obs;

  // bool get isEmpty => selectFoodList.isEmpty;
}
