/// FileName: search_history_widget
///
/// @Author: ygc
/// @Date: 2025/7/11 16:28
/// @Description:
import 'package:dnurse/app/data/pages/food_record/search_food/logic.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../../ui/style/color.dart';

class SearchHistoryWidget extends StatelessWidget {
  SearchFoodLogic get logic => Get.find<SearchFoodLogic>();

  const SearchHistoryWidget({
    super.key,
    this.onItemTap,
  });
  final ValueChanged<String>? onItemTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域隐藏删除按钮
        if (logic.state.showDelete.value) {
          logic.state.showDelete.value = false;
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(context, '常用搜索'),
            _buildComm(context),
            _buildSection(context, '搜索历史'),
            _buildHistory(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: 10.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColor.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (title == '搜索历史')
            GestureDetector(
              onTap: () {
                logic.clearSearchHistory();
              },
              child: Text(
                '清空记录',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColor.textSecondary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 搜索历史
  Widget _buildHistory(BuildContext context) {
    return Obx(() {
      return Container(
        height: 340.h,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: _buildComm2(context, logic.state.searchHistory),
        ),
      );
    });
  }

  Widget _buildComm(BuildContext context) {
    final words = ["米饭", "馒头", "盐", "方便面", "煎饼果子"];
    return _buildComm2(context, words);
  }

  Widget _buildComm2(BuildContext context, List<String> words) {
    return SizedBox(
      width: double.infinity,
      child: Wrap(
          alignment: WrapAlignment.start,
          runAlignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.start,
          children: [
            for (var word in words)
              Padding(
                  padding: EdgeInsets.only(
                    right: 10.w,
                  ),
                  child: GestureDetector(
                      onTap: () {
                        onItemTap?.call(word);
                      },
                      onLongPress: () {
                        logic.state.showDelete.value = true;
                      },
                      child: Stack(
                        children: [
                          Container(
                            constraints: BoxConstraints(
                              minWidth: 60.w,
                              maxWidth: 120.w, // 添加最大宽度限制
                            ),
                            margin: EdgeInsets.only(
                              top: 10.w,
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: 10.w,
                              vertical: 5.w,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20.w),
                              border: Border.all(
                                color: AppColor.primaryLight,
                                width: 1.w,
                              ),
                            ),
                            child: Text(
                              word,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColor.textPrimary,
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
                              maxLines: 1, // 限制为单行
                            ),
                          ),
                          // 显示删除按钮
                          Obx(() => logic.state.showDelete.value &&
                                  logic.state.searchHistory.contains(word)
                              ? Positioned(
                                  left: 0.w,
                                  top: 5.w,
                                  child: GestureDetector(
                                    onTap: () {
                                      logic.deleteSearchHistory(word);
                                    },
                                    child: Container(
                                      width: 16.w,
                                      height: 16.w,
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.4),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 10.w,
                                      ),
                                    ),
                                  ),
                                )
                              : SizedBox()),
                        ],
                      )))
          ]),
    );
  }
}
