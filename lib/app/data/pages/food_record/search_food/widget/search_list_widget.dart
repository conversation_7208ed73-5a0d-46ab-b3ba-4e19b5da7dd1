import 'package:dnurse/app/data/pages/food_record/add_food/state.dart';
import 'package:dnurse/app/data/pages/food_record/add_food/widget/food_select_widget.dart';
import 'package:dnurse/app/data/pages/food_record/search_food/logic.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/button/dnu_button_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class SearchListWidget extends StatelessWidget {
  SearchListWidget({super.key});

  SearchFoodLogic get logic => Get.find<SearchFoodLogic>();
  final smartListKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return _buildCustom(context);
  }

  Widget _buildCustom(BuildContext context) {
    return Obx(() {
      if (logic.state.foodList.isEmpty) {
        return _buildCustomEmpty(context);
      }

      return SmartRefresher(
        enablePullDown: false,
        enablePullUp: true,
        controller: logic.refreshController,
        onRefresh: logic.onRefresh,
        onLoading: logic.onLoading,
        child: ListView.builder(
          itemBuilder: (c, i) => _buildCustomItem(c, i),
          itemExtent: 80.0,
          itemCount: logic.state.foodList.length +
              (logic.state.tabIndex.value == 0 ? 1 : 0),
        ),
      );
    });
  }

  Widget _buildCustomItem(BuildContext context, int index) {
    if (index == logic.state.foodList.length) {
      return _buildCustomAdd(context);
    }

    final item = logic.state.foodList[index];
    return Column(
      children: [
        InkWell(
          onTap: () {
            // logic.saveSearchHistory();
            _buildSelectDialog(context, item, index);
          },
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            color: Colors.white,
            child: Row(children: [
              Expanded(
                child: Row(
                  children: [
                    Flexible(
                      child: Text(
                        item.storageModel.name,
                        style: TextStyle(
                          color: AppColor.textPrimary,
                          fontSize: 16.sp,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '每100g${item.storageModel.calories}千卡',
                      style:
                          TextStyle(color: AppColor.textDesc, fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              if (item.selectNum > 0)
                Text(
                  '${item.selectNum}${item.unit?.name ?? ''}',
                  style: TextStyle(color: AppColor.primary, fontSize: 16.sp),
                ),
              SizedBox(width: 10.w),
              Icon(
                DNUIconFont.qianwang,
                size: 22.w,
                color: AppColor.textDesc,
              )
            ]),
          ),
        ),
        Divider(
          height: 1.w,
          color: AppColor.lineColor,
        ),
      ],
    );
  }

  Widget _buildCustomList(BuildContext context) {
    return Obx(() {
      final foodList = logic.state.foodList;

      return Column(
        children: [
          ...foodList
              .map((item) => _buildCustomItem(context, foodList.indexOf(item))),
          _buildCustomAdd(context),
        ],
      );
    });
  }

  // 添加自定义
  Widget _buildCustomAdd(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColor.lineColor, width: 1.w),
        ),
      ),
      child: DNUButtonText(
        title: '+ 添加自定义',
        titleColor: AppColor.primary,
        fontSize: 16.sp,
        onPressed: () {
          AppRouter.push(Routes.foodCustomAdd);
        },
      ),
    );
  }

// 没有自定义食物
  Widget _buildCustomEmpty(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          DNUAssets.to.images.common.empty,
          width: 100.w,
          height: 100.h,
        ),
        SizedBox(height: 6.w),
        const Text('您还没有添加过自定义食物'),
        SizedBox(height: 6.w),
        // 文字按钮
        GestureDetector(
          onTap: () {
            print('onTap----->');
            AppRouter.push(Routes.foodCustomAdd);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: AppColor.primaryLight,
                size: 18.sp,
              ),
              SizedBox(width: 2.w),
              Text(
                '现在添加',
                style: TextStyle(
                  color: AppColor.primaryLight,
                  fontSize: 13.sp,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  //选择弹框
  void _buildSelectDialog(BuildContext context, FoodItem food, int index) {
    final logic = Get.find<SearchFoodLogic>();

    // food = logic.getSelectedFood(food);
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return FoodSelectWidget(
          storageModel: food.storageModel,
          params: FoodParams(
            unit: food.unit,
            amount: food.selectNum,
            calories: food.calorie,
          ),
          onSelect: (FoodParams selectItem) {
            food.selectNum = selectItem.amount;
            food.unit = selectItem.unit;
            food.calorie = selectItem.calories;
            logic.state.foodList.refresh();
            logic.selectFood(food);
            // logic.state.foodList[index] = selectItem;
            // logic.selectFood(selectItem);
          },
        );
      },
    );
  }
}
