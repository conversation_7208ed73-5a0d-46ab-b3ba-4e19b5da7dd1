import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/food_record/search_food/widget/search_history_widget.dart';
import 'package:dnurse/app/data/pages/food_record/search_food/widget/search_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../router/router.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../../ui/widgets/round_underline_tab_indicator.dart';
import 'logic.dart';

class SearchFoodPage extends StatelessWidget {
  const SearchFoodPage({super.key});

  SearchFoodLogic get logic => Get.find<SearchFoodLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          _buildContent(context),
          _buildBottomBar(context),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: false,
      titleSpacing: 0,
      leading: Padding(
        padding: const EdgeInsets.only(left: 10),
        child: InkWell(
          onTap: () {
            Navigator.maybePop(context);
          },
          child: const Center(
            child: Icon(
              Icons.arrow_back_ios,
              size: 24,
              color: Colors.black,
            ),
          ),
        ),
      ),
      title: _buildSearchBar(context),
      bottom: TabBar(
        controller: logic.tabController,
        // isScrollable: false,
        tabs: logic.tabItems
            .map(
              (item) =>
              Tab(
                text: item,
              ),
        )
            .toList(),
        onTap: (index) {
          logic.state.tabIndex.value = index;
          logic.doSearch();
        },
        indicator: RoundUnderlineTabIndicator(
          fixWidth: 22.w,
          borderSide: const BorderSide(
            width: 6,
            color: AppColor.primary,
          ),
        ),

        labelStyle: TextStyle(fontSize: 18.sp),
        labelColor: AppColor.textPrimary,
        unselectedLabelColor: AppColor.textDesc,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return BrnSearchText(
      innerPadding: const EdgeInsets.symmetric(vertical: 10),
      focusNode: logic.focusNode,
      controller: logic.textController,
      outSideColor: Colors.transparent,
      searchController: logic.searchController,
      onTextClear: () {
        return false;
      },
      innerColor: AppColor.background3,
      maxHeight: 60.w,
      hintText: '输入搜索内容',
      borderRadius: const BorderRadius.all(Radius.circular(30)),
      autoFocus: true,
      onTextCommit: (text) {
        // logic.search();
        logic.saveSearchHistory();
        logic.doSearch();
      },
      onTextChange: (text) {
        logic.onTextChange(text);
      },
      action: GestureDetector(
        child: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 10.w),
          child: Text(
            '取消',
            style: TextStyle(
              color: AppColor.primaryLight,
              fontSize: 16.w,
            ),
          ),
        ),
        onTap: () {
          AppRouter.back();
        },
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: 16.w,
        horizontal: 16.w,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.w),
          topRight: Radius.circular(20.w),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x143947E5),
            offset: Offset(0, -4.w),
            blurRadius: 10.w,
          ),
        ],
      ),
      child: Row(
        children: [
          Obx(
                () =>
                RichText(
                  text: TextSpan(
                    children: <TextSpan>[
                      TextSpan(
                        text: '已选 ',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.textPrimary,
                        ),
                      ),
                      TextSpan(
                        text: '${logic.state.selCalorieSum.value}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.primaryLight,
                        ),
                      ),
                      TextSpan(
                        text: ' 千卡',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
          ),
          const Spacer(),
          Obx(
                () =>
                DNUPrimaryButton(
                  onPressed: () {
                    logic.save();
                  },
                  disable: logic.state.selectFoodList.isEmpty,
                  width: 221.w,
                  gradient: AppColor.primaryGradientHorizontal1,
                  child: Center(
                    child: Text(
                      '确定',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Expanded(
      child: Obx(() {
        if (logic.state.queryText.value.isEmpty) {
          return SizedBox(
            width: double.infinity,
            child: SingleChildScrollView(
              child: _changeTab(context),
            ),
          );
        } else {
          return SearchListWidget();
        }
      }),
    );
  }

  Widget _changeTab(BuildContext context) {
    return Obx(() {
      return Container(
        child: logic.state.tabIndex.value == 0
            ? SearchHistoryWidget(
          onItemTap: (text) {
            logic.state.queryText.value = text;
            logic.textController.text = text;
            logic.doSearch();
          },
        )
            : SearchListWidget(),
      );
    });
  }
}
