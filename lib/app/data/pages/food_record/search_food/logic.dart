import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/food_record/add_food/state.dart';
import 'package:dnurse/app/data/service/extra_data_service.dart';
import 'package:dnurse/app/data/service/food_service.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/framework/service/key_value_storage.dart';
import 'package:dnurse/router/router.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../framework/utils/log.dart';
import '../../../api/dto/food_search/food_search_result_dto.dart';
import '../../../db/model/storage/storage_model.dart';
import '../add_food/logic.dart';
import 'state.dart';

class SearchFoodLogic extends DNUBaseController
    with GetSingleTickerProviderStateMixin {
  final SearchFoodState state = SearchFoodState();

  final BrnSearchTextController searchController = BrnSearchTextController()
    ..isActionShow = true;
  final TextEditingController textController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  final ScrollController scrollController = ScrollController();
  late RefreshController refreshController;
  int _page = 1;
  final int _pageSize = 20;
  final tabItems = ['食物库', '自定义'];
  late TabController tabController;
  int tabIndex = 0;

  @override
  void onInit() {
    super.onInit();

    refreshController = RefreshController(initialRefresh: false);

    tabController = TabController(length: 2, vsync: this);
    getSearchHistory();

    // if(addFoodLogic.state.foodList);

    state.selectFoodList.listen((list) {
      int caloriesForCount = 0;
      for (final food in list) {
        caloriesForCount += food.calorie;
      }
      state.selCalorieSum.value = caloriesForCount;
    });

    final addFoodLogic = Get.find<AddFoodLogic>();
    state.selectFoodList.addAll(addFoodLogic.state.selectFoodList.toList());
  }

  Future<void> doSearch() async {
    await getCustomFoodList();

    _page = 1;
    _searchFoodNet();
  }

  Future<void> _searchFoodNet({
    bool loading = false,
  }) async {
    if (state.queryText.value.isEmpty) {
      return;
    }
    String searchKey = state.queryText.value.trim();

    try {
      FoodSearchResultDTO? result = await FoodService.to.searchFood(
        searchKey,
        _page,
        pageSize: _pageSize,
      );
      if (result != null &&
          state.tabIndex.value == 0 &&
          state.queryText.value.trim() == searchKey) {
        _page++;

        for (final item in result.list) {
          FoodItem? oldItem = state.foodList
              .firstWhereOrNull((ele) => item.name == ele.storageModel.name);
          if (oldItem == null) {
            state.foodList.add(
              FoodItem(
                storageModel: StorageModel(
                    name: item.name,
                    calories: item.calories.toString(),
                    unit: item.unit,
                    imageUrl: item.imageUrl,
                    starNum: item.starNum.round(),
                    fromUser: "0"),
              ),
            );
          }
        }

        if (result.list.isEmpty) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      }
    } catch (e) {
      if (loading) {
        refreshController.loadComplete();
      }

      print(e);
    }
  }

  // 输入框输入时，保存搜索历史
  void onTextChange(String text) async {
    state.queryText.value = text;

    Future.delayed(const Duration(milliseconds: 300), () {
      doSearch();
    });
  }

  void saveSearchHistory() {
    if (state.queryText.value.isEmpty) {
      return;
    }

    List<String> searchHistory =
        KeyValueStorage.to.getList('search_history') ?? [];

    // 如果searchHistory中包含state.queryText.value，则删除
    if (searchHistory.contains(state.queryText.value)) {
      searchHistory.remove(state.queryText.value);
    }

    searchHistory.add(state.queryText.value);

    // 如果searchHistory长度大于10，则删除最后一个

    KeyValueStorage.to.setList('search_history', searchHistory);
    getSearchHistory();
  }

  void getSearchHistory() {
    List<String> searchHistory =
        KeyValueStorage.to.getList('search_history') ?? [];

    // 去重
    searchHistory = searchHistory.toSet().toList();

    state.searchHistory.value = searchHistory;
  }

  // 清空搜索历史
  void clearSearchHistory() {
    KeyValueStorage.to.setList('search_history', []);
    state.searchHistory.value = [];
  }

  // 删除单个搜索历史
  void deleteSearchHistory(String word) {
    List<String> searchHistory =
        KeyValueStorage.to.getList('search_history') ?? [];

    if (searchHistory.contains(word)) {
      searchHistory.remove(word);
      KeyValueStorage.to.setList('search_history', searchHistory);
      getSearchHistory();
    }

    // 如果删除后历史为空，隐藏删除按钮
    if (state.searchHistory.isEmpty) {
      state.showDelete.value = false;
    }
  }

// 获取自定义食物列表
  Future<void> getCustomFoodList() async {
    try {
      if (state.tabIndex.value == 0 && state.queryText.value.isEmpty) {
        return;
      }
      List<StorageModel> data = await ExtraDataService.to
          .queryLibFoodBeansByKey(state.queryText.value,
              category: state.tabIndex.value);
      List<FoodItem> foodList = data.map((item) {
        for (var selItem in state.selectFoodList) {
          if (selItem.storageModel.generalName.isNotEmpty &&
              selItem.storageModel.generalName == item.generalName) {
            return FoodItem(
              storageModel: item,
              calorie: selItem.calorie,
              unit: selItem.unit,
              selectNum: selItem.selectNum,
            );
          }
        }

        return FoodItem(
          storageModel: item,
        );
      }).toList();

      state.foodList.clear();

      // state.foodList.value = foodList;
      state.foodList.addAll(foodList);
      // refreshController = RefreshController(initialRefresh: false);

      print('食物查询结果: ${foodList.length}条');
    } catch (e) {
      Log.i("Error loading data: $e ");
    }
  }

  void selectFood(FoodItem food) {
    bool found = false;
    for (int i = 0; i < state.selectFoodList.length; i++) {
      final item = state.selectFoodList[i];
      if (item.storageModel.generalName.isNotEmpty &&
          item.storageModel.generalName == food.storageModel.generalName) {
        state.selectFoodList[i] = food;
        found = true;
        break;
      }
    }

    if (!found) {
      state.selectFoodList.add(food);
    }
  }

  void save() async {
    if (state.selectFoodList.isEmpty) {
      return;
    }

    // String sn = AppContext.to.userSn;
    // int calorie = 0;
    //
    // List<DataFoodItem> items = state.selectFoodList.map((item) {
    //   calorie += item.calorie;
    //   return DataFoodItem(
    //     name: item.storageModel.name,
    //     calorie: item.calorie,
    //     amount: item.selectNum,
    //     weightUnit: item.unit!,
    //     fromUser: false,
    //     imgUrl: item.storageModel.imageUrl,
    //   );
    // }).toList();
    //
    // DataFood food = DataFood(
    //   did: DataUtils.generateDidWithPrefix('F'),
    //   time: DateTime.now(),
    //   sn: sn,
    //   calorie: calorie,
    //   foodType: _foodType,
    //   items: items,
    // );
    //
    // await FoodService.to.saveFood(AppContext.to.userSn, food);

    AppRouter.back(result: state.selectFoodList.toList());

    // Future.delayed(const Duration(milliseconds: 10), () {
    //   AppRouter.back(result: state.selectFoodList.toList());
    // });
  }

  void onRefresh() async {
    // await _loadData(
    //   refresh: true,
    // );
  }

  void onLoading() async {
    await _searchFoodNet(loading: true);
  }
}
