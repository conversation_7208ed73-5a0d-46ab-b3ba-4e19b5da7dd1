/// FileName: food_select_widget
///
/// @Author: ygc
/// @Date: 2025/7/2 13:22
/// @Description:
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:wheel_slider/wheel_slider.dart';

import '../../../../../../router/router.dart';
import '../../../../../../router/routes.dart';
import '../../../../../../ui/icons/icons.dart';
import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../../common/entity/unit.dart';
import '../../../../db/model/storage/storage_model.dart';
import '../state.dart';

typedef FoodSelectCallback = void Function(
  FoodParams foodParams,
);

class FoodParams {
  final Unit? unit;
  final num amount;
  final int calories;

  FoodParams({
    this.unit,
    required this.amount,
    required this.calories,
  });
}

class FoodSelectWidget extends StatefulWidget {
  const FoodSelectWidget({
    super.key,
    // required this.foodItem,
    this.onSelect,
    required this.storageModel,
    required this.params,
  });

  final StorageModel storageModel;

  // final FoodItem foodItem;

  final FoodSelectCallback? onSelect;

  final FoodParams params;

  @override
  State<FoodSelectWidget> createState() => _FoodSelectWidgetState();
}

class _FoodSelectWidgetState extends State<FoodSelectWidget> {
  double _value = 0;
  int _unitIndex = 0;
  int _optionIndex = 0;
  bool _isInteger = true;
  List<UnitInfo> _units = [];
  late FixedExtentScrollController _valueController;
  late FixedExtentScrollController _unitController;
  final List<num> _options = [];

  @override
  void initState() {
    super.initState();

    _units = UnitInfo.getUnitInfo(widget.storageModel.unit);

    _valueController = FixedExtentScrollController();
    _unitController = FixedExtentScrollController();

    if (widget.params.unit != null) {
      for (int i = 0; i < _units.length; i++) {
        if (widget.params.unit == _units[i].unit) {
          _unitIndex = i;
          break;
        }
      }
    }

    _genOptions();

    if (widget.params.amount > 0) {
      for (int i = 0; i < _options.length; i++) {
        if (widget.params.amount == _options[i]) {
          _optionIndex = i;
          break;
        }
      }
    }

    _calculateValue();
  }

  void _genOptions() {
    _options.clear();

    UnitInfo unitInfo = _units[_unitIndex];
    num mini = unitInfo.min;
    num max = unitInfo.max;
    num interval = unitInfo.interval;
    if (interval < 1) {
      // interval = 1;
      _isInteger = false;
    } else {
      _isInteger = true;
    }

    for (num i = mini; i <= max; i += interval) {
      _options.add(i);
    }

    if (_optionIndex >= _options.length) {
      _optionIndex = _options.length - 1;
    }
  }

  void _calculateValue() {
    UnitInfo unitInfo = _units[_unitIndex];
    num selectValue = _options[_optionIndex];
    _value = selectValue * unitInfo.kilogram;
  }

  @override
  void dispose() {
    _valueController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        bottom: 12.w + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.w),
          topRight: Radius.circular(20.w),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitle(context),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          SizedBox(height: 4.w),
          _buildRecommend("控糖推荐", widget.storageModel.starNum),
          _buildRecommend("降酸推荐", widget.storageModel.uaStarNum),
          SizedBox(height: 12.w),
          Text(
            '${(int.parse(widget.storageModel.calories) / 100 * _value).round()}千卡',
            style: TextStyle(
              fontSize: 16.w,
              color: AppColor.textSecondary,
            ),
          ),
          SizedBox(height: 12.w),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          _buildUnitOptions(context),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          _buildUnitSlider(
            context,
          ),
          SizedBox(height: 10.w),
          DNUPrimaryButton(
            width: 335.w,
            height: 48.w,
            gradient: AppColor.primaryGradientHorizontal1,
            child: Center(
              child: Text(
                '选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
            onPressed: () {
              num amount = _options[_optionIndex];
              int calories =
                  (int.parse(widget.storageModel.calories) / 100 * _value)
                      .round();

              FoodParams food = FoodParams(
                calories: calories,
                unit: _units[_unitIndex].unit,
                amount: amount,
              );

              widget.onSelect?.call(food);
              AppRouter.back();
            },
          )
        ],
      ),
    );
  }

  // 推荐指数
  Widget _buildRecommend(String title, int num) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: 4.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 14.w, color: AppColor.textDesc),
          ),
          SizedBox(
            width: 2.w,
          ),
          ..._buildStarts(num)
        ],
      ),
    );
  }

  // 五颗星
  List<Widget> _buildStarts(int num) {
    List<Widget> widgetList = [];

    for (int i = 0; i < 5; i++) {
      widgetList.add(SizedBox(
        width: 15.w,
        height: 15.w,
        child: Icon(
          DNUIconFont.shoucangxuanding,
          color: i < num ? Colors.orange : Colors.grey,
          size: 15.w,
        ),
      ));
    }

    return widgetList;
  }

  Widget _buildUnitSlider(BuildContext context) {
    TextStyle itemTextStyle = TextStyle(
      fontSize: 16.sp,
      color: AppColor.textSecondary,
    );
    TextStyle selectedItemTextStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColor.primary,
      fontWeight: FontWeight.w500,
    );

    return Container(
      constraints: BoxConstraints(minHeight: 48.w),
      child: WheelSlider.customWidget(
        totalCount: _units.length,
        initValue: _unitIndex,
        isInfinite: false,
        scrollPhysics: const BouncingScrollPhysics(),
        itemSize: 40,
        enableAnimation: false,
        pointerColor: Colors.transparent,
        onValueChanged: (value) {
          _unitIndex = value;
          _genOptions();
          _calculateValue();
          setState(() {});
        },
        children: List.generate(
          _units.length,
          (index) => Center(
            child: Center(
              child: Text(
                _units[index].unit.name,
                style:
                    _unitIndex == index ? selectedItemTextStyle : itemTextStyle,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 48.w),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              AppRouter.back();
            },
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColor.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              widget.storageModel.name,
              style: TextStyle(
                fontSize: 16.w,
                color: AppColor.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          widget.storageModel.classValue == '自定义食物' || widget.storageModel.linkUrl.isEmpty
              ? const SizedBox.shrink()
              : InkWell(
                  onTap: () {
                    AppRouter.back();

                    AppRouter.push(
                      Routes.browser,
                      arguments: widget.storageModel.linkUrl,
                    );
                  },
                  child: Text(
                    '查看详情',
                    style: TextStyle(
                      fontSize: 16.w,
                      color: AppColor.primary,
                    ),
                  ),
                )
        ],
      ),
    );
  }

  Widget _buildUnitOptions(BuildContext context) {
    TextStyle itemTextStyle = TextStyle(
      fontSize: 16.sp,
      color: AppColor.textSecondary,
    );
    TextStyle selectedItemTextStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColor.primary,
      fontWeight: FontWeight.w500,
    );

    return Container(
      constraints: BoxConstraints(minHeight: 48.w),
      child: WheelSlider.customWidget(
        totalCount: _options.length,
        initValue: _optionIndex,
        isInfinite: false,
        scrollPhysics: const BouncingScrollPhysics(),
        itemSize: 80,
        enableAnimation: false,
        pointerColor: Colors.transparent,
        onValueChanged: (value) {
          _optionIndex = value;
          _calculateValue();
          setState(() {});
        },
        children: List.generate(
          _options.length,
          (index) => Center(
            child: Center(
              child: Text(
                _isInteger
                    ? _options[index].toInt().toString()
                    : _options[index].toStringAsFixed(2),
                style: _optionIndex == index
                    ? selectedItemTextStyle
                    : itemTextStyle,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
