import 'dart:convert';

import 'package:get/get.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../common/entity/unit.dart';
import '../../../db/model/storage/storage_model.dart';
import '../../../db/model/storage/unit_range_config_dto.dart';
import '../../../db/model/storage/unit_standard_dto.dart';

class UnitInfo {
  final Unit unit;
  final double kilogram;
  final List<String> options;
  final num min;
  final num max;
  final num interval;

  UnitInfo({
    required this.unit,
    required this.kilogram,
    required this.options,
    required this.min,
    required this.max,
    required this.interval,
  });

  static List<UnitInfo> getUnitInfo(
    String unitJson, {
    bool isInternal = true,
  }) {
    try {
      final data = jsonDecode(unitJson);
      final dataList = data as List;

      return dataList.map((item) {
        UnitStandardDTO unitStandardDTO = UnitStandardDTO.fromJson(item);
        int id = int.tryParse(unitStandardDTO.id) ?? 0;
        double kilogram = double.tryParse(unitStandardDTO.kilogram) ?? 0;
        Unit currentUnit = Unit.getUnitById(id);

        UnitRangeConfigDTO unitRangeConfigDTO = unitStandardDTO.rangeConfig ??
            UnitRangeConfigDTO.fromJson({"min": "10", "max": "500", "x": "5"});
        List<String> valueOptions = [];

        num start = num.tryParse(unitRangeConfigDTO.min) ?? 0;
        num end = double.tryParse(unitRangeConfigDTO.max) ?? 0;
        num increment = double.tryParse(unitRangeConfigDTO.distance) ?? 10;
        if (!isInternal) {
          if (start >= 1 && start < 10) {
            increment = 0.5;
          } else if (start >= 10) {
            increment = 1.0;
          }
        }

        // while (start < end) {
        //   String valueStr = start.toStringAsFixed(isInternal ? 0 : 1);
        //   valueOptions.add(valueStr);
        //
        //   start += increment;
        // }

        return UnitInfo(
          unit: currentUnit,
          kilogram: kilogram,
          options: valueOptions,
          min: start,
          max: end,
          interval: increment,
        );
      }).toList();
    } catch (e) {
      // print(e);
      Log.e("getUnitInfo error:", e);
    }

    return [];
  }
}

class FoodItem {
  final StorageModel storageModel;
  int calorie;
  Unit? unit;
  num selectNum;
  List<UnitInfo> units = [];

  FoodItem({
    required this.storageModel,
    this.calorie = 0,
    this.unit,
    this.selectNum = 0,
  });

  List<UnitInfo> getUnitInfo() {
    if (units.isNotEmpty) {
      return units;
    }

    units = UnitInfo.getUnitInfo(storageModel.unit);
    return units;
  }
}

class AddFoodState {
  AddFoodState() {
    ///Initialize variables
  }

  final clickIndex = 0.obs;
  final foodList = <FoodItem>[].obs;
  final currentValue = 10.obs;

  final selCalorieSum = 0.obs;
  final selectFoodList = <FoodItem>[].obs;
}
