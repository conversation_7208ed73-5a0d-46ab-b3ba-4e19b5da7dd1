import 'package:dnurse/router/router.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../framework/route/base_controller.dart';
import '../../../../../framework/utils/log.dart';
import '../../../../../resource/dnu_assets.dart';
import '../../../common/food_type.dart';
import '../../../db/model/storage/storage_model.dart';
import '../../../service/extra_data_service.dart';
import 'state.dart';

class CustomTab {
  final int index;
  final String text;
  String icon;
  String activeIcon;

  CustomTab({
    required this.index,
    required this.text,
    this.icon = '',
    this.activeIcon = '',
  });
}

class AddFoodLogic extends DNUBaseController
    with GetSingleTickerProviderStateMixin {
  final AddFoodState state = AddFoodState();
  late TabController tabController;
  List<CustomTab> tabs = [];
  Map<String, List<FoodItem>> categorizedFoodList = {};
  FoodType _foodType = FoodType.breakfast;

  @override
  void onInit() {
    super.onInit();

    if (Get.parameters.containsKey("foodType")) {
      _foodType = FoodType.getById(
        int.tryParse(Get.parameters["foodType"]!) ?? 0,
      );
    }

    tabController = TabController(length: 7, vsync: this);
    _initTabs();
    loadDataFromDatabase();

    state.selectFoodList.listen((list) {
      int caloriesForCount = 0;
      for (final food in list) {
        caloriesForCount += food.calorie;
      }
      state.selCalorieSum.value = caloriesForCount;
    });
  }

  void _initTabs() {
    tabs = [
      CustomTab(
        index: 0,
        text: '常用主食',
        icon: DNUAssets.to.images.data.meal,
        activeIcon: DNUAssets.to.images.data.activeMeal,
      ),
      CustomTab(
        index: 1,
        text: '常用蔬果',
        icon: DNUAssets.to.images.data.vegetables,
        activeIcon: DNUAssets.to.images.data.activeVegetables,
      ),
      CustomTab(
        index: 2,
        text: '常用肉蛋豆',
        icon: DNUAssets.to.images.data.meat,
        activeIcon: DNUAssets.to.images.data.activeMeat,
      ),
      CustomTab(
        index: 3,
        text: '常用海鲜',
        icon: DNUAssets.to.images.data.seafood,
        activeIcon: DNUAssets.to.images.data.activeSeafood,
      ),
      CustomTab(
        index: 4,
        text: '常用小吃',
        icon: DNUAssets.to.images.data.snacks,
        activeIcon: DNUAssets.to.images.data.activeSnacks,
      ),
      CustomTab(
        index: 5,
        text: '酒品调料',
        icon: DNUAssets.to.images.data.wine,
        activeIcon: DNUAssets.to.images.data.activeWine,
      ),
      CustomTab(
        index: 6,
        text: '自定义',
        icon: DNUAssets.to.images.data.customize,
        activeIcon: DNUAssets.to.images.data.activeCustomize,
      ),
    ];
  }

  String getTabCategory(String tag) {
    if (tag == '谷薯类') return '常用主食';
    if (['蔬菜类', '水果类', '菌藻类'].contains(tag)) return '常用蔬果';
    if (['禽畜肉类', '蛋奶类', '豆类'].contains(tag)) return '常用肉蛋豆';
    if (tag == '海鲜类') return '常用海鲜';
    if (['坚果类', '小吃糕点', '糖果类'].contains(tag)) return '常用小吃';
    if (['油脂类', '调料类', '饮料酒类'].contains(tag)) return '酒品调料';
    if (tag == '自定义食物') return '自定义';
    return ''; // 如果不属于上述分类，返回空字符串，可根据实际情况调整处理方式
  }

  Future<void> loadDataFromDatabase() async {
    try {
      List<StorageModel> data = await ExtraDataService.to.getFoodList(1);
      List<FoodItem> foodList = data
          .map((item) => FoodItem(
                storageModel: item,
              ))
          .toList();
      for (CustomTab tab in tabs) {
        categorizedFoodList[tab.text] = [];
      }
      // 遍历食物列表，按照分类将食物添加到对应的列表中
      for (FoodItem item in foodList) {
        String category = getTabCategory(item.storageModel.classValue);
        if (categorizedFoodList.containsKey(category)) {
          categorizedFoodList[category]!.add(item);
        }
      }
      state.foodList.addAll(categorizedFoodList['常用主食']!.toList());
    } catch (e) {
      Log.i("Error loading data: $e ");
    }
  }

  // 根据Tab文本获取对应分类的食物数据列表
  List<FoodItem> getFoodListByTab(String tabText) {
    return categorizedFoodList[tabText] ?? [];
  }

  void selectFood(FoodItem food) {
    bool found = false;
    for (int i = 0; i < state.selectFoodList.length; i++) {
      final item = state.selectFoodList[i];
      if (item.storageModel.generalName.isNotEmpty &&
          item.storageModel.generalName == food.storageModel.generalName) {
        state.selectFoodList[i] = food;
        found = true;
        break;
      }
    }

    if (!found) {
      state.selectFoodList.add(food);
    }
  }

  FoodItem getSelectedFood(FoodItem food) {
    for (int i = 0; i < state.selectFoodList.length; i++) {
      final item = state.selectFoodList[i];
      if (item.storageModel.generalName.isNotEmpty &&
          item.storageModel.generalName == food.storageModel.generalName) {
        return item;
      }
    }

    return food;
  }

  void save() async {
    if (state.selectFoodList.isEmpty) {
      return;
    }

    // String sn = AppContext.to.userSn;
    // int calorie = 0;
    //
    // List<DataFoodItem> items = state.selectFoodList.map((item) {
    //   calorie += item.calorie;
    //   return DataFoodItem(
    //     name: item.storageModel.name,
    //     calorie: item.calorie,
    //     amount: item.selectNum,
    //     weightUnit: item.unit!,
    //     fromUser: false,
    //     imgUrl: item.storageModel.imageUrl,
    //   );
    // }).toList();
    //
    // DataFood food = DataFood(
    //   did: DataUtils.generateDidWithPrefix('F'),
    //   time: DateTime.now(),
    //   sn: sn,
    //   calorie: calorie,
    //   foodType: _foodType,
    //   items: items,
    // );
    //
    // await FoodService.to.saveFood(AppContext.to.userSn, food);

    AppRouter.back(result: state.selectFoodList.toList());
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  void onSearchBack(List<FoodItem> list) {
    state.selectFoodList.clear();
    state.selectFoodList.addAll(list);
  }
}
