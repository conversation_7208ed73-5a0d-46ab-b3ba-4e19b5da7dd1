import 'package:dnurse/app/data/pages/food_record/add_food/state.dart';
import 'package:dnurse/app/data/pages/food_record/add_food/widget/food_select_widget.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/widgets/button/dnu_button_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../ui/icons/icons.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/style/theme.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../../ui/widgets/button/dnu_primary_button.dart';
import 'logic.dart';

class AddFoodPage extends StatelessWidget {
  const AddFoodPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
        context: context,
        title: '添加饮食',
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildSearchBar(context),
          _buildTabBar(context),
          _buildFoodList(context),
          _buildBottomBar(context)
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    final logic = Get.find<AddFoodLogic>();

    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 8.w,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 10.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8FE),
          borderRadius: BorderRadius.circular(20.w),
        ),
        child: InkWell(
          onTap: () {
            AppRouter.push(Routes.searchFood)?.then((result) {
              print('res--------->result: $result');
              if (result != null) {
                print(
                    'res--------->result: ${result.length}');
                logic.onSearchBack(result);
                // loadData(state.selectedDay.value);
                // _loadCalories(state.selectedDay.value);
              }
            });
          },
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: Icon(
                  DNUIconFont.sousuo,
                  size: 20.w,
                  color: AppColor.textSecondary,
                ),
              ),
              Expanded(
                child: Text(
                  '请输入食物名称',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColor.textDisable,
                  ),
                ),
                //   child: TextField(
                //     // controller: logic.controller,
                //     // focusNode: logic.focusNode,
                //     decoration: InputDecoration(
                //         hintText: '请输入食物名称',
                //         hintStyle:
                //             TextStyle(fontSize: 14.sp, color: AppColor.textDisable),
                //         border: InputBorder.none,
                //         contentPadding: EdgeInsets.only(left: 10.w)),
                //     style: TextStyle(fontSize: 14.sp, color: AppColor.textPrimary),
                //     onChanged: (value) {},
                //   ),
                // )
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final logic = Get.find<AddFoodLogic>();
    return Container(
      color: Colors.white,
      margin: EdgeInsets.symmetric(vertical: AppTheme.listSectionGap),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
      child: Theme(
        data: Theme.of(context).copyWith(
          tabBarTheme: Theme.of(context).tabBarTheme.copyWith(
            tabAlignment: TabAlignment.start,
            dividerColor: Colors.transparent,
          ),
        ),
        child: TabBar(
          controller: logic.tabController,
          isScrollable: true,
          indicator: const BoxDecoration(),
          tabs:
              logic.tabs.map((item) => _buildTabBarItem(context, item)).toList(),
          onTap: (index) {
            logic.state.clickIndex.value = index;
            final String currentTabText =
                logic.tabs[logic.state.clickIndex.value].text;
            final List<FoodItem> foodListForTab =
                logic.getFoodListByTab(currentTabText);
            logic.state.foodList.clear();

            for (int i = 0; i < foodListForTab.length; i++) {
              final item = foodListForTab[i];
              final oldItem = logic.getSelectedFood(item);
              if (oldItem.unit != null) {
                foodListForTab[i] = oldItem;
              }
            }
            logic.state.foodList.addAll(foodListForTab);
          },
        ),
      ),
    );
  }

  Widget _buildTabBarItem(BuildContext context, CustomTab item) {
    final logic = Get.find<AddFoodLogic>();
    return Obx(() {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Image.asset(
            logic.state.clickIndex.value == item.index
                ? item.activeIcon
                : item.icon,
            width: 28.w,
            fit: BoxFit.cover,
          ),
          SizedBox(height: 2.w),
          Text(
            item.text,
            style: TextStyle(
                fontSize: 16.sp,
                color: logic.state.clickIndex.value == item.index
                    ? AppColor.textPrimary
                    : AppColor.textDesc),
          ),
        ],
      );
    });
  }

  Widget _buildFoodList(BuildContext context) {
    final logic = Get.find<AddFoodLogic>();

    return Obx(() {
      return Expanded(
        child: ListView.builder(
          itemBuilder: (c, i) {
            if (i < logic.state.foodList.length) {
              return _buildListItem(c, i);
            } else if (logic.state.clickIndex.value == 6) {
              return _buildCustomAdd(context);
            }
          },
          itemCount: logic.state.foodList.length + 1,
        ),
      );
    });
  }

  Widget _buildListItem(BuildContext context, int index) {
    final logic = Get.find<AddFoodLogic>();
    final FoodItem food = logic.state.foodList[index];
    return Column(children: [
      InkWell(
        onTap: () {
          Log.i("food------>>>>>>: ${food.storageModel.name} ----- $index");
          _buildSelectDialog(context, food, index);
        },
        child: Container(
          width: double.infinity,
          height: 56.w,
          padding: EdgeInsets.all(16.w),
          color: Colors.white,
          child: Row(children: [
            Text(
              food.storageModel.name,
              style: TextStyle(color: AppColor.textPrimary, fontSize: 16.sp),
            ),
            SizedBox(width: 4.w),
            Text(
              '每100g${food.storageModel.calories}千卡',
              style: TextStyle(color: AppColor.textDesc, fontSize: 14.sp),
            ),
            const Spacer(),
            if (food.selectNum > 0)
              Text(
                '${food.selectNum}${food.unit?.name ?? ''}',
                style: TextStyle(color: AppColor.primary, fontSize: 16.sp),
              ),
            SizedBox(width: 10.w),
            Icon(
              DNUIconFont.qianwang,
              size: 22.w,
              color: AppColor.textDesc,
            )
          ]),
        ),
      ),
      Divider(
        height: 1.w,
        color: AppColor.lineColor,
      ),
    ]);
  }

  Widget _buildBottomBar(BuildContext context) {
    final logic = Get.find<AddFoodLogic>();
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: 16.w,
        horizontal: 16.w,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFF5F5F5),
            width: 1.w,
          ),
        ),
        // borderRadius: BorderRadius.only(
        //   topLeft: Radius.circular(20.w),
        //   topRight: Radius.circular(20.w),
        // ),
      ),
      child: Row(
        children: [
          Obx(
            () => RichText(
              text: TextSpan(
                children: <TextSpan>[
                  TextSpan(
                    text: '已选 ',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.textPrimary,
                    ),
                  ),
                  TextSpan(
                    text: '${logic.state.selCalorieSum.value}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.primaryLight,
                    ),
                  ),
                  TextSpan(
                    text: ' 千卡',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          Obx(
            () => DNUPrimaryButton(
              onPressed: () {
                logic.save();
              },
              disable: logic.state.selectFoodList.isEmpty,
              width: 221.w,
              gradient: AppColor.primaryGradientHorizontal1,
              child: Center(
                child: Text(
                  '确定',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 添加自定义
  Widget _buildCustomAdd(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56.w,
      color: Colors.white,
      child: DNUButtonText(
        title: '添加自定义',
        titleColor: AppColor.primary,
        fontSize: 16.sp,
        onPressed: () {
          AppRouter.push(Routes.foodCustomAdd);
        },
      ),
    );
  }

  //选择弹框
  void _buildSelectDialog(BuildContext context, FoodItem food, int index) {
    final logic = Get.find<AddFoodLogic>();

    // food = logic.getSelectedFood(food);
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return FoodSelectWidget(
          storageModel: food.storageModel,
          params: FoodParams(
            unit: food.unit,
            amount: food.selectNum,
            calories: food.calorie,
          ),
          onSelect: (FoodParams selectItem) {
            food.selectNum = selectItem.amount;
            food.unit = selectItem.unit;
            food.calorie = selectItem.calories;
            logic.selectFood(food);
            // logic.state.foodList[index] = selectItem;
            // logic.selectFood(selectItem);
          },
        );
      },
    );
  }
}
