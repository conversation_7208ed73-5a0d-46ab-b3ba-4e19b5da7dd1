import 'package:dnurse/app/data/entity/device_sync_info.dart';
import 'package:dnurse/app/data/service/glucose_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../framework/route/base_controller.dart';
import '../../../../framework/utils/log.dart';
import '../../entity/Data.dart';
import '../../entity/data_glucose_per_day.dart';
import '../../service/data_service.dart';
import '../../service/device_sync_service.dart';
import 'state.dart';

class BleSyncDataLogic extends DNUBaseController {
  final BleSyncDataState state = BleSyncDataState();

  @override
  void onInit() {
    super.onInit();

    String deviceSn = Get.parameters["deviceSn"] ?? "";
    String mode = Get.parameters["mode"] ?? "";
    if (deviceSn.isEmpty || mode.isEmpty) {
      AppRouter.back();
      return;
    }

    DeviceSyncDevice? device =
        DeviceSyncDevice.getById(int.tryParse(mode) ?? 0);
    if (device == null) {
      AppRouter.back();
      return;
    }

    state.deviceSn.value = deviceSn;
    state.deviceType.value = device;
    if(device == DeviceSyncDevice.contour) {
      state.title.value = "已同步血糖数据";
    } else {
      state.title.value = "已同步笔环数据";
    }

    _loadData();
  }

  void _loadData() async {
    List<String>? didList = await DeviceSyncService.to.getDeviceSyncDataId(
      AppContext.to.userSn,
      state.deviceSn.value,
      state.deviceType.value,
    );

    Log.d("didList: $didList");
    if (didList == null) {
      AppRouter.back();
      return;
    }

    List<DataGlucosePerDay>? dataList =
        await GlucoseService.to.getDeviceSyncDataByDid(
      AppContext.to.userSn,
      didList,
    );
    Log.d("dataList: $dataList");
    if (didList == null) {
      AppRouter.back();
      return;
    }

    state.dataList.addAll(dataList!);
  }

  onDeleteItemClick(BuildContext context, int groupIndex, int itemIndex) {
    DNUDialog.showConfirm(title: "删除", content: "确定删除该血糖数据？", onConfirm: () {

      Data item = state.dataList[groupIndex].glucoseList[itemIndex];
      DataService.to.deleteData(item, AppContext.to.userSn);

      state.dataList[groupIndex].glucoseList.removeAt(itemIndex);
      if(state.dataList[groupIndex].glucoseList.isEmpty) {
        state.dataList.removeAt(groupIndex);
      }
      state.dataList.refresh();
    });
  }
}
