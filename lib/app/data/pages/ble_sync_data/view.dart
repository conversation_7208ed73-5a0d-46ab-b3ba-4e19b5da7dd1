import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';
import '../../entity/Data.dart';
import '../../entity/data_glucose_per_day.dart';
import 'logic.dart';

class BleSyncDataPage extends StatelessWidget {
  const BleSyncDataPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<BleSyncDataLogic>();

    return Obx(() {
      return Scaffold(
        appBar: getAppBar(
          context: context,
          title: logic.state.title.value,
          backgroundColor: Colors.transparent,
        ),
        body: CustomScrollView(
          slivers: <Widget>[
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  return _buildItem(
                      logic.state.dataList[index], context, index);
                },
                childCount: logic.state.dataList.length,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildItem(DataGlucosePerDay data, BuildContext context, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            data.date.formatDate("yyyy-MM-dd"),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: AppColor.textPrimary,
            ),
          ),
          Padding(padding: EdgeInsets.only(top: 12.w)),
          _buildGroupItems(context, data, index),
          Padding(padding: EdgeInsets.only(top: 8.w)),
        ],
      ),
    );
  }

  Widget _buildGroupItems(BuildContext context, DataGlucosePerDay data, int index) {
    List<Widget> children = [];
    int cur = 0;
    for (Data data in data.glucoseList) {
      children.add(
        _buildGroupSlidableItemLayout(context, data, index, cur),
      );

      cur++;
    }

    return Column(
      children: children,
    );
  }

  // Widget _buildGroupSlidableItem(int groupIndex, int itemIndex, Data dataLog) {
  //   return _buildGroupSlidableItemLayout(dataLog, groupIndex, itemIndex);
  // }

  Widget _buildGroupSlidableItemLayout(
      BuildContext context,
      Data data, int groupIndex, int itemIndex) {
    return Row(
      children: [
        Padding(padding: EdgeInsets.only(left: 8.w)),
        Text(
          data.title(),
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
            color: AppColor.textSecondary,
          ),
        ),
        Padding(padding: EdgeInsets.only(left: 8.w)),
        Expanded(
          child: Text(data.message(),
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                color: AppColor.textSecondary,
              )),
        ),
        Text(
          data.stringValue(),
          style: TextStyle(
            fontSize: 21.sp,
            color: AppColor.textPrimary,
            fontFamily: AppFont.ranyBold,
          ),
        ),
        Padding(padding: EdgeInsets.only(left: 4.w)),
        Text(
          data.unit(),
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.textSecondary,
            fontFamily: AppFont.ranyMedium,
          ),
        ),
        Padding(padding: EdgeInsets.only(left: 4.w)),
        InkWell(
          onTap: () {
            final logic = Get.find<BleSyncDataLogic>();
            logic.onDeleteItemClick(context, groupIndex, itemIndex);
          },
          child: Padding(
            padding: EdgeInsets.all(8.0.w),
            child: const Icon(
              DNUIconFont.a_202lajitongshise,
              color: AppColor.textSecondary,
            ),
          ),
        ),
      ],
    );
  }
}
