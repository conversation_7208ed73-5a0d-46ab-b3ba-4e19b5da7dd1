import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../bootstrap/app.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../ui/widgets/progress/arc_progress_widget.dart';
import 'logic.dart';

class DataTestLoadingPage extends StatefulWidget {
  const DataTestLoadingPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DataResultPage();
}

class _DataResultPage extends State<DataTestLoadingPage>
    with TickerProviderStateMixin {
  final logic = Get.find<DataTestLoadingLogic>();
  final state = Get.find<DataTestLoadingLogic>().state;

  late final AnimationController _rotationAnimationCtr = AnimationController(
    duration: const Duration(seconds: 2),
    vsync: this,
  )..repeat();

  late final Animation<double> _rotationAnimation =
  Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: _rotationAnimationCtr,
      curve: Curves.linear,
    ),
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
          context: context,
          title: AppContext.to.getLocale().str_thshcl,
          titleColor: Colors.white),
      body: SafeArea(
        child: Column(
          children: [
            _countdownView(),
            _stateContent(),
            _adviseView(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _rotationAnimationCtr.dispose();
    logic.dispose();

    super.dispose();
  }

  Widget _countdownView() {
    return Container(
      margin: EdgeInsets.only(top: 30.sp),
      child: Center(
        child: Obx(
              () => Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 205.sp,
                height: 205.sp,
                child: RotationTransition(
                  turns: _rotationAnimation,
                  child: ArcProgressWidget(
                    backgroundColor: Colors.white,
                    value: 1,
                    openAngle: 0,
                    gradient: const SweepGradient(
                      colors: [
                        Color(0x0048CFAD),
                        Color(0xFF48CFAD),
                        Color(0xFF4FC1E9),
                      ],
                      transform: GradientRotation(pi / 2),
                    ),
                    strokeCapRound: true,
                    progressbarStrokeWidth: 16.sp,
                  ),
                ),
              ),
              Text(
                '${state.countdownValue.value}',
                style: TextStyle(
                  fontSize: 80.sp,
                  color: const Color(0xFF3BAFDA),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _stateContent() {
    return Obx(
          () => Container(
        margin: EdgeInsets.only(top: 20.h),
        child: Text(
          state.testStateStr.value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColor.textDesc,
          ),
        ),
      ),
    );
  }

  Widget _adviseView() {
    return Expanded(
        child: Container(
          margin: EdgeInsets.only(top: 40.sp),
          color: Colors.white,
        ));
  }
}
