import 'dart:async';

import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/app/data/entity/Data.dart';
import 'package:dnurse/app/data/entity/data_uric_acid.dart';
import 'package:dnurse/app/data/service/uric_acid_service.dart';
import 'package:dnurse_spug_plugin/dnurse_spug_plugin.dart';
import 'package:get/get.dart';

import '../../../../bootstrap/app.dart';
import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../../ui/widgets/dialog/dnu_dialog.dart';
import '../../common/time_point.dart';
import '../../db/model/glucose/glucose_ua_value_model.dart';
import '../../entity/data_from.dart';
import '../../entity/data_glucose.dart';
import '../../entity/data_source.dart';
import '../../event/bs_state_changed.dart';
import '../../event/spug_state_changed.dart';
import '../../service/glucose_service.dart';
import 'state.dart';

class DataTestLoadingLogic extends GetxController {
  final DataTestLoadingState state = DataTestLoadingState();

  late StreamSubscription _spugStateListen;
  late StreamSubscription _bsStateListen;

  @override
  void onInit() {
    super.onInit();

    Map<String, String?> parameters = Get.parameters;
    if (parameters.isNotEmpty) {
      state.testType = int.parse(parameters['testType'] ?? '0');
      if (state.testType == 1) {
        state.testStateStr.value = AppContext.to.getLocale().str_zhzshcnszh;
        state.countdownValue.value = 25;
      } else {
        state.testStateStr.value = AppContext.to.getLocale().str_zhzshchxtzh;
        state.countdownValue.value = 5;
      }
      state.deviceType = int.parse(parameters['deviceType'] ?? '0');
    }

    initListen();
  }

  @override
  void onClose() {
    cancelListen();

    super.onClose();
  }

  void initListen() {
    _spugStateListen = EventService.to.bus.on<SPUGStateChanged>().listen(_onSPUGStateChanged);
    _bsStateListen = EventService.to.bus.on<BSStateChanged>().listen(_onBSStateChanged);
  }

  void cancelListen() {
    _spugStateListen.cancel();
    _bsStateListen.cancel();
  }

  int valueState = 0; //1为高值，2为低值
  void _onSPUGStateChanged(SPUGStateChanged event) {
    Log.d('测量结果页面处理SPUG通知：${event.state}');
    dynamic data = event.data;
    Log.d('测量结果页面处理SPUG通知数据：$data');
    switch (event.state) {
      case 2: {
        int errCode = data['ErrorCode'];
        if (errCode == 0x0101) {
          Log.d('结果Hi值');
          valueState = 1;
        } else if(errCode == 0x0102) {
          Log.d('结果Low值');
          valueState = 2;
        } else {
          DNUDialog.showAlert(title: '发生未处理错误', content: '错误码：$errCode', confirm: '好的');
        }
      }
      break;

      case 4: {
        dynamic value1 = data['TestValue'];
        double value = value1.runtimeType == double ? value1 : double.parse('$value1');
        int dataTime = double.parse('${data['DateTime']}').round();
        int testType = data['TestType'];
        int testUnit = data['Unit'];
        DateTime time = DateTime.fromMillisecondsSinceEpoch(dataTime*1000);
        TimePoint point = GlucoseService.to.getCurUserTimePointByDateTime(time);
        GlucoseUAValueModel model;
        Data dataModel;
        if (testType == 1) {
          dataModel = DataUricAcid(did: DataUtils.generateDidWithPrefix('UA'), sn: AppContext.to.userSn, mmolValue: valueState == 1 ? 1189 : valueState == 2 ? 1 : value.round(), timePoint: point, time: time, source: DataSource.Device_Spug, dataFrom: DataFrom.DataFrom_Device);
          Log.d('测量出尿酸值：$dataModel');
        } else {
          dataModel = DataGlucose(did: DataUtils.generateDidWithPrefix('BS'), sn: AppContext.to.userSn, mmolValue: valueState == 1 ? 33.4 : valueState == 2 ? 0.1 : value, timePoint: point, time: time, source: DataSource.Device_Spug, dataFrom: DataFrom.DataFrom_Device);
          Log.d('测量出血糖值：$dataModel');
        }
        if (testType == 1) {
          UricAcidService.to.saveUaData(dataModel as DataUricAcid, AppContext.to.userSn);
        } else {
          GlucoseService.to.saveGlucose(dataModel as DataGlucose, AppContext.to.userSn);
        }
        AppRouter.replace(Routes.dataResult, arguments: {'data': dataModel});

        DnurseSpugPlugin.getInstance().receiveSPUGData(dataTime);
      }
      break;

      case 5: {
        // state.testStateStr.value = AppContext.to.getLocale().str_shizhizijian;
      }
      break;

      case 11: {
        // state.gifPath.value = _getImgPath('spug_plugin_paper');
        // state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
        // state.testStep.value = 1;
        //
        // state.showSettingBtn.value = false;
        // state.showBuyBtn.value = true;
      }
      break;

      case 13: {
        state.countdownValue.value = data['countdown'];
      }
      break;

      case 98: {
        // state.gifPath.value = _getImgPath('plugin_device');
        // state.testStateStr.value = AppContext.to.getLocale().str_plugin_device;
        // state.testStep.value = 0;
        // state.showSettingBtn.value = false;
        // state.showBuyBtn.value = true;
      }
      break;
    }
  }

  void _onBSStateChanged(BSStateChanged event) {
    Log.d('测量页面处理手机血糖仪通知：${event.state}');
    dynamic data = event.data;
    switch (event.state) {
      case 0: {
        // state.gifPath.value = _getImgPath('plugin_device');
        // state.testStateStr.value = AppContext.to.getLocale().str_plugin_device;
        // state.testStep.value = 0;
        // state.showSettingBtn.value = false;
        // state.showBuyBtn.value = true;
      }
      break;

      case 7: {
        // state.gifPath.value = 'assets/images/data/test/bs_plugin_paper.gif';
        // state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
        // state.testStep.value = 1;
        //
        // state.showSettingBtn.value = false;
        // state.showBuyBtn.value = true;
      }
      break;

      case 8: {
        //开始测量
        state.countdownValue.value = data['test_progress'];
      }
      break;

      case 9: {
        // {test_device_voltage: 2.950000047683716, test_device_sn: 0057006B600A26394E45, test_device_gluco_current: -1, test_date: 1720693523919, test_flag: null, test_value: 25.200000762939453, test_device_temp: 27.700000762939453, TEST_DURATION: 0.0}
        double value = data['test_value'];
        int dataTime = data['test_date'];
        Log.d('手机血糖仪-测量出值：$value--$dataTime');
        DateTime time = DateTime.fromMillisecondsSinceEpoch(dataTime);
        TimePoint point = GlucoseService.to.getCurUserTimePointByDateTime(time);
        DataGlucose dataGlucose = DataGlucose(did: DataUtils.generateDidWithPrefix('BS'), sn: AppContext.to.userSn, mmolValue: value, timePoint: point, time: time);
        GlucoseService.to.saveGlucose(dataGlucose, AppContext.to.userSn);
        AppRouter.replace(Routes.dataResult, arguments: {'data': dataGlucose});
        // _loadBSAdvice();
      }
      break;

      case 18: {
        // state.gifPath.value = 'assets/images/data/test/bs_device_sleep.gif';
        // state.testStateStr.value = AppContext.to.getLocale().str_device_sleep;
        // state.showWakeupBtn.value = true;
      }
      break;
    }
  }
}
