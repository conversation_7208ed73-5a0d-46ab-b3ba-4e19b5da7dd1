import 'package:dnurse/app/data/pages/sport_custom_add/state.dart';
import 'package:dnurse/app/data/service/extra_data_service.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:get/get.dart';

class SportCustomAddLogic extends DNUBaseController {
  final SportCustomAddState state = SportCustomAddState();

  // 监测是否为空
  bool checkSportItem() {
    if (state.sportName.value.trim().isEmpty) {
      DNUToast.show("请输入名称", Get.context!);
      return false;
    }
    if (state.sportTime.value <= 0) {
      DNUToast.show("请输入运动时长", Get.context!);
      return false;
    }
    if (state.sportCalorie.value <= 0) {
      DNUToast.show("请输入消耗卡路里数", Get.context!);
      return false;
    }
    return true;
  }

  Future<bool> checkSportExist() async {
    final sport =
        await ExtraDataService.to.getSportByName(state.sportName.value);
    return sport != null;
  }

  // 清空数据
  void clearData() {
    state.sportName.value = '';
    state.sportTime.value = 0;
    state.sportCalorie.value = 0;
  }

  // 保存自定义运动
  Future<void> saveSport({int type = 0}) async {
    if (!checkSportItem()) {
      return;
    }

    final isExist = await checkSportExist();
    if (isExist) {
      DNUToast.show("运动已存在", Get.context!);
      return;
    }

    try {
      await ExtraDataService.to.saveCustomSport({
        'name': state.sportName.value,
        'time': state.sportTime.value,
        'calorie': state.sportCalorie.value,
      });

      DNUToast.show("保存成功", Get.context!);
      if (type == 0) {
        // 保存并返回了
        Get.back();
      } else {
        // 清空继续添加
        clearData();
      }
    } catch (e) {
      Log.e("SportCustomAddLogic.saveSport error:", e);
      DNUToast.show("保存失败", Get.context!);
    }
    clearData();
  }
}
