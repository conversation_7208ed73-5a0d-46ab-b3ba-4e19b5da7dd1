import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/food_custom_add/brnRow_delegate.dart';

import 'package:dnurse/app/data/pages/sport_custom_add/logic.dart';

import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/theme.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';

class SportCustomAddPage extends StatelessWidget {
  SportCustomAddPage({super.key});

  final controller = Get.find<SportCustomAddLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
          context: context,
          backgroundColor: Colors.white,
          title: '添加运动',
          elevation: 0,
          actions: [
            getAppBarAction(
              iconData: DNUIconFont.zhengchanggou1,
              onPressed: () {
                controller.saveSport(type: 0);
              },
            )
          ]),
      body: _buildBody(context),
      // 底部按钮
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  _buildBody(BuildContext context) {
    return Column(
      children: [
        _buildFormGroup(context),
      ],
    );
  }

  // form group
  _buildFormGroup(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: AppTheme.listSectionGap),
        _buildFormItem(
          context,
          BrnTextInputFormItem(
            isRequire: true,
            title: "运动名称",
            hint: "请输入名称",
            onChanged: (newValue) {
              controller.state.sportName.value = newValue;
            },
          ),
        ),
        _buildFormItem(
          context,
          BrnTextInputFormItem(
            isRequire: true,
            title: "运动时长",
            hint: "0",
            unit: "分钟",
            onChanged: (newValue) {
              if (newValue.isNotEmpty) {
                controller.state.sportTime.value = int.parse(newValue);
              } else {
                controller.state.sportTime.value = 0;
              }
            },
          ),
        ),
        _buildFormItem(
          context,
          BrnTextInputFormItem(
            isRequire: true,
            title: "消耗卡路里数",
            hint: "0",
            unit: "千卡",
            onChanged: (newValue) {
              if (newValue.isNotEmpty) {
                controller.state.sportCalorie.value = int.parse(newValue);
              } else {
                controller.state.sportCalorie.value = 0;
              }
            },
          ),
          isLast: true,
        ),
      ],
    );
  }
}

_buildBottomNavigationBar(BuildContext context) {
  final logic = Get.find<SportCustomAddLogic>();
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
    color: AppColor.backgroundWhite,
    child: DNUPrimaryButton(
      title: '继续添加运动',
      fontSize: 16.sp,
      onPressed: () {
        logic.saveSport(type: 1);
      },
    ),
  );
}

Widget _buildFormItem(BuildContext context, Widget child,
    {bool isLast = false}) {
  return Container(
    width: double.infinity,
    decoration: BoxDecoration(
      color: Colors.white,
      border: isLast
          ? null
          : const Border(
              bottom: BorderSide(color: AppColor.tableBorder),
            ),
    ),
    child: child,
  );
}
