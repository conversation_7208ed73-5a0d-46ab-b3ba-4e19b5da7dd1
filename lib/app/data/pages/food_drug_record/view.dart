import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'logic.dart';

class FoodDrugRecordPage extends StatelessWidget {
  const FoodDrugRecordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final state = Get.find<FoodDrugRecordLogic>().state;
    final logic = Get.put(FoodDrugRecordLogic());
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
        context: context,
        title: '添加记录',
        backgroundColor: Colors.white,
        bottom: TabBar(
            controller: logic.tabController,
            labelColor: AppColor.textPrimary,
            unselectedLabelColor: AppColor.textDesc,
            indicatorColor: AppColor.primaryLight,
            indicatorSize: TabBarIndicatorSize.tab,
            onTap: (index) {
              logic.onTabChanged(context, index);
              //logic.tabList[index].logic().onShow();
            },
            tabs: logic.tabList.map((e) => Tab(text: e.title)).toList()),
      ),
      body: PageView(
        controller: logic.pageController,
        children: logic.tabList.map((e) => e.page()).toList(),
        onPageChanged: (int index) {
          // state.curPage.value = index;
          logic.tabController.index = index;
        },
      ),
    );
  }
}
