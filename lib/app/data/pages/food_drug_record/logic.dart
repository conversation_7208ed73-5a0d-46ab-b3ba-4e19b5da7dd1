import 'package:dnurse/app/data/pages/food_drug_record/pages/drug_record/logic.dart';
import 'package:dnurse/app/data/pages/food_drug_record/pages/drug_record/view.dart';
import 'package:dnurse/app/data/pages/food_drug_record/pages/fdr_base_logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'state.dart';

class FDRTabItem {
  final FDRBaseLogic Function() logic;
  final Widget Function() page;
  final VoidCallback init;
  final String title;

  FDRTabItem({
    required this.logic,
    required this.page,
    required this.init,
    required this.title,
  });
}

class FoodDrugRecordLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final FoodDrugRecordState state = FoodDrugRecordState();
  late TabController tabController;
  late final PageController pageController;
  late List<FDRTabItem> tabList;

  @override
  void onInit() {
    super.onInit();

    _initTab();

    tabController = TabController(length: tabList.length, vsync: this);
    pageController = PageController(initialPage: 0);
  }

  void _initTab() {
    tabList = [
      FDRTabItem(
        logic: () => Get.find<DrugRecordLogic>(tag: kFDRTypeFood),
        page: () => const DrugRecordPage(tag: kFDRTypeFood),
        init: () {
          Get.lazyPut<DrugRecordLogic>(() => DrugRecordLogic(tag: kFDRTypeFood),
              tag: kFDRTypeFood);
        },
        title: '饮食',
      ),
      FDRTabItem(
        logic: () => Get.find<DrugRecordLogic>(tag: kFDRTypeOral),
        page: () => const DrugRecordPage(tag: kFDRTypeOral),
        init: () {
          Get.lazyPut<DrugRecordLogic>(() => DrugRecordLogic(tag: kFDRTypeOral),
              tag: kFDRTypeOral);
        },
        title: '口服药',
      ),
      FDRTabItem(
        logic: () => Get.find<DrugRecordLogic>(tag: kFDRTypeInsulin),
        page: () => const DrugRecordPage(tag: kFDRTypeInsulin),
        init: () {
          Get.lazyPut<DrugRecordLogic>(
              () => DrugRecordLogic(tag: kFDRTypeInsulin),
              tag: kFDRTypeInsulin);
        },
        title: '胰岛素',
      ),
    ];
    for (var item in tabList) {
      item.init();
    }
  }

  void onTabChanged(BuildContext context, int index) {
    if (index != state.curPage.value) {
      int oldIndex = state.curPage.value;

      pageController.jumpToPage(index);
      state.curPage.value = index;

      tabList[oldIndex].logic().onHide();
      tabList[index].logic().onShow();
    }
  }
}
