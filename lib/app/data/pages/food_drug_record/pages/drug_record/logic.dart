import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

import '../../../../../../framework/utils/log.dart';
import '../../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../../../../user/entity/drug_info.dart';
import '../fdr_base_logic.dart';
import 'state.dart';

const kFDRTypeFood = 'food';
const kFDRTypeOral = 'oral';
const kFDRTypeInsulin = 'insulin';

class DrugRecordLogic extends FDRBaseLogic {
  final DrugRecordState state = DrugRecordState();

  DrugRecordLogic({required this.tag});

  final String tag;

  @override
  void onInit() {}

  void onUpdateClicked(BuildContext context) {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
              actions: [
                BrnCommonActionSheetItem(
                  '添加',
                  actionStyle: BrnCommonActionSheetItemStyle.normal,
                ),
                BrnCommonActionSheetItem(
                  '修改时间',
                  actionStyle: BrnCommonActionSheetItemStyle.normal,
                ),
                BrnCommonActionSheetItem(
                  '删除',
                  actionStyle: BrnCommonActionSheetItemStyle.normal,
                ),
              ],
              onItemClickInterceptor:
                  (int index, BrnCommonActionSheetItem actionEle) {
                if (index == 0) {
                  Log.i('添加');
                  Navigator.pop(context);
                  // AppRouter.push(Routes.numberEdit);
                  return true;
                } else if (index == 1) {
                  Log.i('修改时间');
                  DateSheetView.show(
                    context: context,
                    initialDateTime: DateTime.now(),
                    pickerMode: BrnDateTimePickerMode.datetime,
                    dateFormat: 'yyyy年,MM月,dd日,HH时:mm分',
                    // 设置时间格式
                    onConfirm: (dateTime, selectedIndex) {
                      // 处理时间选择确认
                    },
                  );
                  return true;
                } else if (index == 2) {
                  Log.i('删除');
                }
                return false;
              });
        });
  }

  void updateSelectedList(List<DrugInfo> result) {
    if (result.isNotEmpty) {
      List<DrugInfo> selectedInfoList = result.whereType<DrugInfo>().toList();
      state.selectedList.addAll(selectedInfoList);
      update();
    }
  }
}
