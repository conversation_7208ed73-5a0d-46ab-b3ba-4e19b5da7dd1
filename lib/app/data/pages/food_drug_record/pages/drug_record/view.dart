import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../../framework/utils/log.dart';
import '../../../../../../router/router.dart';
import '../../../../../../router/routes.dart';
import '../../../../../../ui/icons/icons.dart';
import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/style/theme.dart';
import '../../../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../../../../user/entity/drug_info.dart';
import 'logic.dart';

class DrugRecordPage extends StatelessWidget {
  const DrugRecordPage({super.key, required this.tag});

  final String tag;

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<DrugRecordLogic>(tag: tag);
    return Column(
      children: [
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    return _buildRecordListItem(context);
                  },
                  childCount: 2,
                ),
              )
            ],
          ),
        ),
        _buildBottomButton(context)
      ],
    );
  }

  Widget _buildRecordListItem(BuildContext context) {
    final logic = Get.find<DrugRecordLogic>(tag: tag);
    return Container(
      // width: MediaQuery.of(context).size.width - 32.w,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radius),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Text('11月28日'),
              const Spacer(),
              const Text('4 粒'),
              SizedBox(
                width: 7.w,
              ),
              InkWell(
                onTap: () {
                  logic.onUpdateClicked(context);
                },
                child: Icon(
                  DNUIconFont.gengduo,
                  size: 20.w,
                  color: AppColor.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5.w,
          ),
          Obx(() {
            int index = 0;
            return Column(
                children: logic.state.selectedList
                    .map((e) => _buildDrugItem(context, e, index: index++))
                    .toList());
          })
        ],
      ),
    );
  }

  Widget _buildDrugItem(BuildContext context, DrugInfo insulinInfo,
      {int index = 0}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: AppColor.background3,
        borderRadius: BorderRadius.circular(AppTheme.radius),
      ),
      child: Row(
        children: [
          Text(insulinInfo.name),
          const Spacer(),
          Text(insulinInfo.controller.text),
          SizedBox(
            width: 7.w,
          ),
          Icon(
            DNUIconFont.shanchu,
            size: 20.w,
            color: AppColor.textDesc,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton(BuildContext context) {
    final logic = Get.find<DrugRecordLogic>(tag: tag);
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(
        bottom: 16.w + MediaQuery.of(context).padding.bottom,
        top: 16.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          DNUPrimaryButton(
            title: '添加记录',
            fontSize: 14.sp,
            width: 200.w,
            height: 35.w,
            onPressed: () async {
              bool isDateSheetViewFinished = false;
              DateSheetView.show(
                context: context,
                initialDateTime: DateTime.now(),
                pickerMode: BrnDateTimePickerMode.datetime,
                dateFormat: 'yyyy年,MM月,dd日,HH时:mm分',
                onConfirm: (dateTime, selectedIndex) {
                  logic.state.currentTime.value = dateTime;
                  logic.state.isConfirmClicked.value = true;
                  Log.i(
                      'don known----${logic.state.currentTime.value}--${logic.state.isConfirmClicked.value}');
                  isDateSheetViewFinished = true;
                },
              );
              while (!isDateSheetViewFinished) {
                await Future.delayed(const Duration(milliseconds: 100));
              }
              //  Log.i('嗨嗨嗨------');
              if (logic.state.isConfirmClicked.value &&
                  tag == kFDRTypeInsulin) {
                Log.i('进来了 insulin');
                final result = await AppRouter.push(Routes.drug);
                if (result != null && result.isNotEmpty) {
                  //logic.updateSelectedList(result);
                  Log.i('记录药物-------$result');
                }
              }
            },
          )
        ],
      ),
    );
  }
}
