import 'package:get/get.dart';

import '../../entity/data_glucose.dart';

class BsDataAddState {
  BsDataAddState() {
    ///Initialize variables
  }

  final dataBS = DataGlucose.getEmpty().obs;
  // final dataDate = DateTime.now().obs;
  // final dataPoint = TimePoint.breakfastBefore.obs;
  // final bsValue = 0.0.obs;

  //血糖值输入框内容监听
  final inputValue = ''.obs;
  // final valueStatus = DataValueStatus.empty.obs;
}
