import 'dart:math';

import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../bootstrap/app.dart';
import '../../../../resource/dnu_assets.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../ui/widgets/progress/arc_progress_widget.dart';
import '../../../user/entity/unit/glucose_unit.dart';
import '../../../user/service/user_config_service.dart';
import '../../common/data_status.dart';
import '../../common/precision_limit_formatter.dart';
import '../../common/time_point.dart';
import '../../entity/data_glucose.dart';
import '../../service/glucose_service.dart';
import '../../widget/edit_progress_widget.dart';
import '../widget/data_time_point_picker_view.dart';
import 'logic.dart';

class BsDataAddPage extends StatelessWidget {
  const BsDataAddPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<BsDataAddLogic>();
    final state = Get.find<BsDataAddLogic>().state;
    return Scaffold(
      appBar: getAppBar(
        context: context,
        title: AppContext.to.getLocale().str_jlxt,
        backgroundColor: Colors.white,
        backButtonColor: AppColor.textPrimary,
        titleColor: AppColor.textPrimary,
        // actions: [
        //   Row(
        //     children: [
        //       SizedBox(
        //         // width: width ?? 30.w,
        //         // height: height ?? 24.w,
        //         child: ShaderMask(
        //           blendMode: BlendMode.srcATop,
        //           shaderCallback: (bounds) {
        //             return const LinearGradient(
        //               begin: Alignment.bottomLeft,
        //               end: Alignment.topRight,
        //               colors: [
        //                 AppColor.primary,
        //                 AppColor.primaryLight,
        //               ],
        //             ).createShader(bounds);
        //           },
        //           child: const Icon(DNUIconFont.shipuhantangliang),
        //         ),
        //       ),
        //       Text(AppContext.to.getLocale().str_bdyj, style: TextStyle(fontSize: 16.sp, color: AppColor.textPrimary, fontWeight: FontWeight.w500),),
        //     ],
        //   )
        // ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Column(
              children: [
                timePoint(context),
                SizedBox(height: 44.sp,),
                // valueView(),
                Obx(() => EditProgressWidget(
                  width: 313.sp,
                  height: 168.sp,
                  progressValue: state.dataBS.value.getProgressValue(),
                  progressGradient: state.dataBS.value.getValueStatus(GlucoseService.to.currentDataTarget.value).gradientColor,
                  hintText: '',
                  unitStr: UserConfigService.to.glucoseUnit.value.name,
                  miniStr: DataGlucose.formatMole(DataGlucose.minValue, UserConfigService.to.glucoseUnit.value, withHighLow: false),
                  maxStr: DataGlucose.formatMole(DataGlucose.maxValue, UserConfigService.to.glucoseUnit.value, withHighLow: false),
                  editTextColor: state.dataBS.value.getValueStatus(GlucoseService.to.currentDataTarget.value).color,
                  decimalPlaces: UserConfigService.to.glucoseUnit.value == GlucoseUnit.mole ? 1 : 0,
                  onEditChanged: logic.onTextChanged,
                  editController: logic.editController,
                ),),
                testBtn(),
                _deviceBtn(),
                saveBtn(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget timePoint(BuildContext context) {
    final logic = Get.find<BsDataAddLogic>();
    final state = Get.find<BsDataAddLogic>().state;
    return InkWell(
      onTap: () {
        Get.bottomSheet(DataTimePointPickerView(
          selectedDate: state.dataBS.value.time,
          selectedPoints: TimePoint.values,
          pointsSelectedIndex: state.dataBS.value.timePoint.id,
          onTimePointSelected: logic.onTimePointSelected,
        ));
      },
      child: Container(
        margin: EdgeInsets.only(left: 44.sp, right: 44.sp, top: 28.sp),
        height: 44.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(22.sp),
          gradient: AppColor.primaryBackground132,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(right: 20.sp, child: Icon(DNUIconFont.sanjiao01, size: 16.sp, color: AppColor.textDisable,),),
            Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                const SizedBox(width: 5,),
                Text(state.dataBS.value.time.formatDate(DateFormat.ABBR_MONTH_DAY), style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                Text(state.dataBS.value.time.formatDate(DateFormat.HOUR24_MINUTE), style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                Text(state.dataBS.value.timePoint.name, style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                const SizedBox(width: 5,),
              ],
            ),),
          ],
        ),
      ),
    );
  }

  Widget valueView() {
    final logic = Get.find<BsDataAddLogic>();
    final state = Get.find<BsDataAddLogic>().state;

    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              // color: Colors.red,
              width: 313.sp,
              height: 168.sp,
              child: Obx(() => ArcProgressWidget(
                backgroundColor: AppColor.background2,
                value: state.dataBS.value.getProgressValue(),
                openAngle: pi,
                gradient: state.dataBS.value.getValueStatus(GlucoseService.to.currentDataTarget.value).gradientColor,
                strokeCapRound: true,
                progressbarStrokeWidth: 16.sp,
              )),
            ),
            Container(
              margin: EdgeInsets.only(top: 36.sp),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F6),
                borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              ),
              width: 124.sp,
              height: 60.sp,
              child: Obx(() => _getTextField('', logic.editController, logic.onTextChanged, 4, state.dataBS.value.getValueStatus(GlucoseService.to.currentDataTarget.value)),),
            ),
            Container(
              margin: EdgeInsets.only(top: 135.sp),
              // decoration: BoxDecoration(
              //   color: const Color(0xFFF0F0F6),
              //   borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              // ),
              // width: 124.sp,
              height: 26.sp,
              child: Text(UserConfigService.to.glucoseUnit.value.name, style: TextStyle(fontSize: 18.sp, color: AppColor.textDesc),),
            ),
          ],
        ),

        SizedBox(width: 313.sp, child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(DataGlucose.formatMole(DataGlucose.minValue, UserConfigService.to.glucoseUnit.value, withHighLow: false), style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),),
            Text(DataGlucose.formatMole(DataGlucose.maxValue, UserConfigService.to.glucoseUnit.value, withHighLow: false), style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),),
          ],
        ),),
      ],
    );
  }

  Widget _getTextField(String hintText, TextEditingController controller,
      ValueChanged<String>? onChanged, int? maxLength, DataValueStatus status) {
    return TextField(
      cursorColor: const Color(0xFF707390),
      cursorWidth: 4,
      // focusNode: editFouFocusNode,
      textAlign: TextAlign.center,
      decoration: InputDecoration(
        hintText: hintText,
        counterText: "",
        iconColor: AppColor.textPrimary,
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: AppColor.textDisable,
          fontSize: 48.sp,
          fontFamily: AppFont.ranyBold,
        ),
      ),
      inputFormatters: [
        PrecisionLimitFormatter(UserConfigService.to.glucoseUnit.value == GlucoseUnit.mole ? 1 : 0),
      ],
      style: TextStyle(
        fontSize: 48.sp,
        color: status.color,
        fontFamily: AppFont.ranyBold,
      ),
      controller: controller,
      onChanged: onChanged,
      maxLength: maxLength,
      keyboardType: TextInputType.numberWithOptions(
          decimal: UserConfigService.to.glucoseUnit.value == GlucoseUnit.mole ? true : false),
    );
  }

  Widget testBtn() {
    return InkWell(
      onTap: () {
        AppRouter.push(Routes.dataTest);
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.sp, right: 20.sp, top: 48.sp),
        height: 64.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(32.sp),
          gradient: AppColor.primaryBackgroundDarkLight10,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Image.asset(DNUAssets.to.images.user.deviceSpug),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(AppContext.to.getLocale().str_thshjc, style: TextStyle(fontSize: 18.sp, color: AppColor.textPrimary, fontWeight: FontWeight.w500),),
                SizedBox(height: 4.sp,),
                Text(AppContext.to.getLocale().str_shjxty_spug, style: TextStyle(fontSize: 12.sp, color: AppColor.textDesc),),
              ],
            ),
            const SizedBox(width: 5,),
            Container(
              width: 36.sp,
              height: 36.sp,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    Color(0x803947E5),
                    Color(0x804284FF),
                  ],
                ),
              ),
              child: const Icon(DNUIconFont.qianwang, color: Colors.white,),
            )
          ],
        ),
      ),
    );
  }

  Widget _deviceBtn() {
    return InkWell(
      onTap: () {
        AppRouter.push(Routes.deviceList, parameters: {'source': 'deviceAdd'});
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.sp, right: 20.sp, top: 48.sp),
        height: 64.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(32.sp),
          gradient: AppColor.primaryBackgroundDarkLight10,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              // width: width ?? 30.w,
              // height: height ?? 24.w,
              child: ShaderMask(
                blendMode: BlendMode.srcATop,
                shaderCallback: (bounds) {
                  return const LinearGradient(
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                    colors: [
                      AppColor.primary,
                      AppColor.primaryLight,
                    ],
                  ).createShader(bounds);
                },
                child: const Icon(DNUIconFont.shipuhantangliang),
              ),
            ),
            Text(
              AppContext.to.getLocale().str_bdyj,
              style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.textPrimary,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget saveBtn() {
    final logic = Get.find<BsDataAddLogic>();
    final state = Get.find<BsDataAddLogic>().state;

    return Obx(
      () => Visibility(
        visible: state.dataBS.value.isValidate,
        child: Container(
          margin: EdgeInsets.only(top: 20.sp),
          padding: EdgeInsets.symmetric(horizontal: 20.sp),
          child: DNUPrimaryButton(
            title: AppContext.to.getLocale().save,
            iconLeft: DNUIconFont.wancheng,
            onPressed: () {
              logic.saveData();
            },
          ),
        ),
      ),
    );
  }
}
