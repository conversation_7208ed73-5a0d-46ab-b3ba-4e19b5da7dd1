/// FileName: reminder_table_widget
///
/// @Author: ygc
/// @Date: 2024/7/31 11:14
/// @Description:
import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:bruno/bruno.dart';
import 'package:dnurse/ui/icons/icon_strings.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../resource/generated/l10n.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../../common/time_point.dart';
import '../../../entity/reminder/reminder_plan.dart';

class ReminderTableWidget extends StatelessWidget {
  ReminderTableWidget({
    super.key,
    this.backgroundColor,
    required this.reminder,
    this.onReminderPlanChanged,
    this.readOnly = false,
  });

  final Color? backgroundColor;
  final bool readOnly;
  final Map<TimePoint, ReminderPlan> reminder;
  final List<TimePoint> _columns = [
    TimePoint.dawn,
    TimePoint.breakfastBefore,
    TimePoint.breakfastAfter,
    TimePoint.lunchBefore,
    TimePoint.lunchAfter,
    TimePoint.supperBefore,
    TimePoint.supperAfter,
    TimePoint.sleep,
  ];

  final ValueChanged<List<ReminderPlan>>? onReminderPlanChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
      ),
      child: Column(
        children: [
          _buildMonitorTableHead(context),
          Table(
            // border: TableBorder.all(color: AppColor.textDisable),
            children: [
              for (int i = 0; i < 7; i++) _buildDataTableIconRow(context, i),
              _buildDataTableTimeRow(context),
            ],
          ),
        ],
      ),
    );
  }

  ReminderPlan _getReminderPlan(TimePoint timePoint) {
    if (!reminder.containsKey(timePoint)) {
      reminder[timePoint] = ReminderPlan.getDefault(timePoint);
    }

    return reminder[timePoint]!;
  }

  Widget _buildDataTableIconCell(
      BuildContext context, int index, int dayIndex, bool marginRight) {
    TimePoint timePoint = _columns[index];
    ReminderPlan reminderPlan = _getReminderPlan(timePoint);
    bool enable = reminderPlan.checkEnable(dayIndex);
    return _buildTableCell(
      "",
      marginRight: marginRight,
      highlight: index % 2 == 0,
      textColor: AppColor.auxiliaryGreen1,
      backgroundColor:
          enable ? AppColor.auxiliaryGreen2.withOpacity(0.1) : null,
      onTap: () {
        Log.i("icon tap: $reminderPlan");
        bool enable = reminderPlan.checkEnable(dayIndex);
        reminderPlan.setEnable(dayIndex, !enable);
        onReminderPlanChanged?.call([reminderPlan]);
      },
      child: AutoSizeText(
        enable ? DNUIconString.shixinxiedi : '',
        minFontSize: 20,
        maxFontSize: 28,
        style: const TextStyle(
          color: AppColor.auxiliaryGreen1,
          fontWeight: FontWeight.w400,
          fontSize: 28,
          fontFamily: AppFont.icon,
        ),
      ),
    );
  }

  TableRow _buildDataTableIconRow(BuildContext context, int rowIndex) {
    List<String> weekName = ["一", "二", "三", "四", "五", "六", "日"];
    return TableRow(children: [
      _buildTableCell(
        weekName[rowIndex],
        onTap: () => onRowHeaderClicked(rowIndex),
      ),
      for (int colIndex = 0; colIndex < _columns.length; colIndex++)
        _buildDataTableIconCell(
          context,
          colIndex,
          rowIndex,
          colIndex < _columns.length - 1,
        ),
    ]);
  }

  Widget _buildDataTableTimeCell(BuildContext context, int index) {
    TimePoint timePoint = _columns[index];
    ReminderPlan reminderPlan = _getReminderPlan(timePoint);
    return _buildTableCell(
      reminderPlan.time,
      highlight: index % 2 == 0,
      textColor: AppColor.primaryLight,
      height: 68.w,
      rotateAngle: pi / 2,
      onTap: () {
        if (readOnly) {
          return;
        }

        Log.i("time tap: $reminderPlan");
        DateTime dateTime =
            DateTime(2024, 1, 1, reminderPlan.hour, reminderPlan.minute);

        DateSheetView.show(
          context: context,
          dateFormat: "HH-mm",
          pickerMode: BrnDateTimePickerMode.time,
          initialDateTime: dateTime,
          onConfirm: (DateTime dateTime, List<int> selectedIndex) {
            if (reminderPlan.hour != dateTime.hour ||
                reminderPlan.minute != dateTime.minute) {
              _onTimeChanged(context, index, reminderPlan, dateTime);
            }
          },
        );
      },
    );
  }

  void _onTimeChanged(BuildContext context, int index,
      ReminderPlan reminderPlan, DateTime dateTime) {
    TimePoint prevTimePoint;
    TimePoint nextTimePoint;
    if (index == 0) {
      prevTimePoint = _columns.last;
      nextTimePoint = _columns[1];
    } else if (index == _columns.length - 1) {
      prevTimePoint = _columns[index - 1];
      nextTimePoint = _columns.first;
    } else {
      prevTimePoint = _columns[index - 1];
      nextTimePoint = _columns[index + 1];
    }
    ReminderPlan prevReminderPlan = _getReminderPlan(prevTimePoint);
    ReminderPlan nextReminderPlan = _getReminderPlan(nextTimePoint);

    int maxMinute = nextReminderPlan.hour * 60 + nextReminderPlan.minute - 30;
    int minMinute = prevReminderPlan.hour * 60 + prevReminderPlan.minute + 30;
    int curMinute = dateTime.hour * 60 + dateTime.minute;

    if (maxMinute > minMinute) {
      if (curMinute < minMinute || curMinute > maxMinute) {
        DNUToast.show(S.of(context).reminderMonitorTimeAdjust, context);
        return;
      }
    } else {
      if (curMinute > maxMinute && curMinute < minMinute) {
        DNUToast.show(
            S.of(context).reminderMonitorTimeError(
                prevTimePoint.name, nextTimePoint.name),
            context);
        return;
      }
    }

    reminderPlan.hour = dateTime.hour;
    reminderPlan.minute = dateTime.minute;
    onReminderPlanChanged?.call([reminderPlan]);
  }

  TableRow _buildDataTableTimeRow(BuildContext context) {
    return TableRow(children: [
      _buildTableCell(
        "时\n间",
        height: 68.w,
      ),
      for (int colIndex = 0; colIndex < _columns.length; colIndex++)
        _buildDataTableTimeCell(context, colIndex),
    ]);
  }

  Widget _buildTableHeadText(BuildContext context, String text) {
    return Center(
      child: AutoSizeText(
        text,
        minFontSize: 10,
        maxFontSize: 14,
        style: const TextStyle(
          fontSize: 14,
          color: AppColor.textSecondary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTableHeadCell(
    BuildContext context, {
    String? text,
    double? height,
    Widget? child,
    VoidCallback? onTap,
    EdgeInsetsGeometry? margin,
    bool marginLeft = true,
    bool marginRight = true,
    bool marginTop = true,
    bool marginBottom = true,
    bool isDouble = false,
  }) {
    return TableCell(
      child: InkWell(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.only(
            left: marginLeft ? 2 : 0,
            right: marginRight ? 2 : 0,
            top: marginTop ? 2 : 0,
            bottom: marginBottom ? 2 : 0,
          ),
          decoration: !isDouble
              ? BoxDecoration(
                  color: const Color(0xFFF8F8FE),
                  borderRadius: BorderRadius.circular(10.w),
                )
              : null,
          height: height ?? 80.w,
          width: double.infinity,
          child: child ?? _buildTableHeadText(context, text ?? ""),
        ),
      ),
    );
  }

  Widget _buildTableHeadDoubleCell(
    BuildContext context,
    String text, {
    String? leftText,
    String? rightText,
    VoidCallback? onLeftTap,
    VoidCallback? onRightTap,
  }) {
    return Column(
      children: [
        Expanded(
          child: _buildHeadCellBackground(
            child: _buildTableHeadText(context, text),
          ),
        ),
        const SizedBox(height: 4),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: onLeftTap,
                  child: _buildHeadCellBackground(
                    child: _buildTableHeadText(context, leftText ?? "前"),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: InkWell(
                  onTap: onRightTap,
                  child: _buildHeadCellBackground(
                    child: _buildTableHeadText(context, rightText ?? "后"),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeadCellBackground({
    Widget? child,
    bool marginLeft = false,
    bool marginRight = false,
    bool marginTop = false,
    bool marginBottom = false,
  }) {
    return _buildCellBackground(
      backgroundColor: const Color(0xFFF8F8FE),
      marginLeft: marginLeft,
      marginRight: marginRight,
      marginTop: marginTop,
      marginBottom: marginBottom,
      child: child,
    );
  }

  Widget _buildCellBackground({
    Widget? child,
    Color? backgroundColor,
    double? height,
    bool marginLeft = true,
    bool marginRight = true,
    bool marginTop = true,
    bool marginBottom = true,
  }) {
    return Container(
      height: height,
      margin: EdgeInsets.only(
        left: marginLeft ? 2 : 0,
        right: marginRight ? 2 : 0,
        top: marginTop ? 2 : 0,
        bottom: marginBottom ? 2 : 0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? const Color(0xFFFAFAFF),
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: child,
    );
  }

  Widget _buildMonitorTableHead(BuildContext context) {
    return Table(
      columnWidths: const {
        2: FlexColumnWidth(2),
        3: FlexColumnWidth(2),
        4: FlexColumnWidth(2),
      },
      border: const TableBorder(),
      // border: const TableBorder(
      //   top: BorderSide(color: AppColor.textDisable),
      //   left: BorderSide(color: AppColor.textDisable),
      //   right: BorderSide(color: AppColor.textDisable),
      //   horizontalInside: BorderSide(color: AppColor.textDisable),
      //   verticalInside: BorderSide(color: AppColor.textDisable),
      // ),
      children: [
        TableRow(children: [
          _buildTableHeadCell(context,
              text: "星\n期",
              marginLeft: false,
              marginTop: false,
              onTap: onWeekdayClicked),
          _buildTableHeadCell(context,
              text: "凌\n晨", marginLeft: false, marginTop: false, onTap: () {
            onHeaderClicked(TimePoint.dawn);
          }),
          _buildTableHeadCell(
            context,
            isDouble: true,
            child: _buildTableHeadDoubleCell(
              context,
              "早餐",
              leftText: "空腹",
              onLeftTap: () => onHeaderClicked(TimePoint.breakfastBefore),
              onRightTap: () => onHeaderClicked(TimePoint.breakfastAfter),
            ),
          ),
          _buildTableHeadCell(
            context,
            isDouble: true,
            child: _buildTableHeadDoubleCell(
              context,
              "午餐",
              onLeftTap: () => onHeaderClicked(TimePoint.lunchBefore),
              onRightTap: () => onHeaderClicked(TimePoint.lunchAfter),
            ),
          ),
          _buildTableHeadCell(
            context,
            isDouble: true,
            child: _buildTableHeadDoubleCell(
              context,
              "晚餐",
              onLeftTap: () => onHeaderClicked(TimePoint.supperBefore),
              onRightTap: () => onHeaderClicked(TimePoint.supperAfter),
            ),
          ),
          _buildTableHeadCell(context,
              text: "睡\n前", marginRight: false, marginTop: false, onTap: () {
            onHeaderClicked(TimePoint.sleep);
          }),
        ]),
      ],
    );
  }

  Widget _buildTableCell(
    String value, {
    bool highlight = false,
    VoidCallback? onTap,
    Color? textColor,
    bool marginRight = true,
    double? height,
    double rotateAngle = 0,
    Color? backgroundColor,
    Widget? child,
  }) {
    return TableCell(
      child: InkWell(
        onTap: onTap,
        child: _buildCellBackground(
          marginRight: marginRight,
          height: height ?? 40.w,
          backgroundColor: backgroundColor,
          child: Transform.rotate(
            angle: rotateAngle,
            child: Center(
              child: child ??
                  AutoSizeText(
                    value,
                    minFontSize: 10,
                    maxFontSize: 14,
                    style: TextStyle(
                      color: textColor ?? AppColor.textSecondary,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
            ),
          ),
        ),
      ),
    );
  }

  void onHeaderClicked(TimePoint timePoint) {
    if (readOnly) {
      return;
    }
    ReminderPlan reminderPlan = _getReminderPlan(timePoint);
    if (reminderPlan.enabled != 0) {
      reminderPlan.enabled = 0;
    } else {
      reminderPlan.enableAll();
    }

    onReminderPlanChanged?.call([reminderPlan]);
  }

  void onRowHeaderClicked(int index) {
    if (readOnly) {
      return;
    }

    int enabledCount = 0;
    for (TimePoint timePoint in _columns) {
      ReminderPlan reminderPlan = _getReminderPlan(timePoint);
      reminderPlan.checkEnable(index);
      if (reminderPlan.checkEnable(index)) {
        enabledCount++;
      }
    }

    bool enabled = (enabledCount == 0 || enabledCount < _columns.length);
    List<ReminderPlan> changedList = [];

    for (TimePoint timePoint in _columns) {
      ReminderPlan reminderPlan = _getReminderPlan(timePoint);
      reminderPlan.setEnable(index, enabled);
      changedList.add(reminderPlan);
    }

    onReminderPlanChanged?.call(changedList);
  }

  void onWeekdayClicked() {
    if (readOnly) {
      return;
    }

    int allEnabledCount = 0;
    for (TimePoint timePoint in _columns) {
      ReminderPlan reminderPlan = _getReminderPlan(timePoint);
      Log.i("onWeekdayClicked: $reminderPlan");

      if (reminderPlan.isAllEnabled) {
        allEnabledCount++;
      }
    }

    bool enableAll = allEnabledCount == 0 || allEnabledCount < _columns.length;

    List<ReminderPlan> changedList = [];

    for (TimePoint timePoint in _columns) {
      ReminderPlan reminderPlan = _getReminderPlan(timePoint);
      if (enableAll) {
        reminderPlan.enableAll();
      } else {
        reminderPlan.enabled = 0;
      }
      changedList.add(reminderPlan);
    }

    onReminderPlanChanged?.call(changedList);
  }
}
