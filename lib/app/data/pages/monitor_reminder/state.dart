import 'package:get/get.dart';

import '../../common/monitor_plan.dart';
import '../../common/time_point.dart';
import '../../entity/reminder/reminder_plan.dart';

class MonitorReminderState {
  MonitorReminderState() {
    ///Initialize variables
  }

  final reminder = <TimePoint, ReminderPlan>{}.obs;
  final reminderPlan = <ReminderPlan>[].obs;

  final plan = 0.obs;
  final reminderEnable = false.obs;
  MonitorPlanType get planType => MonitorPlanType.getOptions()[plan.value];
}
