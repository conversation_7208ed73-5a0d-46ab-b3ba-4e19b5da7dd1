import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/monitor_reminder/widget/reminder_table_widget.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../framework/utils/log.dart';
import '../../../../framework/utils/network_utils.dart';
import '../../../../router/router.dart';
import '../../../../ui/icons/icons.dart';
import '../../../../ui/widgets/dialog/dnu_dialog.dart';
import '../../../user/pages/health/health_guide/widget/single_select_widget.dart';
import '../../common/monitor_plan.dart';
import 'logic.dart';

class MonitorReminderPage extends StatelessWidget {
  const MonitorReminderPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<MonitorReminderLogic>();
    final state = Get.find<MonitorReminderLogic>().state;

    return Scaffold(
      backgroundColor: AppColor.background2,
      appBar: getAppBar(
        backgroundColor: Colors.white,
        context: context,
        title: '测血糖提醒',
        actions: [
          getAppBarAction(
            iconData: DNUIconFont.baocun,
            onPressed: () {
              logic.onSaveClicked(context);
            },
          )
        ],
      ),
      body: WillPopScope(
        onWillPop: () async {
          if (logic.checkChanged()) {
            DNUDialog.showConfirm(
              title: "提示",
              content: "您修改了测血糖提醒\n是否保存？",
              confirm: "保存",
              onConfirm: () {
                NetworkUtils.checkState(
                    context: context,
                    onSuccess: () {
                      logic.onSaveClicked(context);
                    });
              },
              onCancel: () {
                AppRouter.back();
              },
            );
            return false;
          }

          return true;
        },
        child: SingleChildScrollView(
          child: Column(children: [
            _buildReminderPlan(context),
            _buildMonitorTable(context),
            _buildReminderSwitch(context),
          ]),
        ),
      ),
    );
  }

  Widget _buildMonitorTable(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.w),
      child: Obx(() {
        final logic = Get.find<MonitorReminderLogic>();
        return ReminderTableWidget(
          reminder: logic.state.reminder.value,
          onReminderPlanChanged: logic.onReminderChanged,
        );
      }),
    );
  }

  Widget _buildReminderPlan(BuildContext context) {
    final logic = Get.find<MonitorReminderLogic>();
    return Column(
      children: [
        Container(
          color: Colors.white,
          margin: EdgeInsets.only(top: 10.w),
          child: Obx(() {
            return SingleSelectWidget(
              options: MonitorPlanType.getOptions()
                  .map((e) => e.getName(context))
                  .toList(),
              itemWidth: 82.w,
              spacing: 5.w,
              canCancel: false,
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.w),
              selectedIndex: [logic.state.plan.value],
              onSelected: (value) {
                logic.onPlanChanged(value);
              },
            );
          }),
        ),
        Obx(() {
          MonitorPlanType monitorPlanType =
              MonitorPlanType.getOptions()[logic.state.plan.value];
          return Padding(
            padding: EdgeInsets.only(left: 10.w, right: 10.w, top: 10.w),
            child: Text(
              monitorPlanType.getDetail(context),
              style: TextStyle(fontSize: 14.sp, color: AppColor.textDesc),
              textAlign: TextAlign.center,
            ),
          );
        }),
      ],
    );
  }

  Widget _buildReminderSwitch(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 10.w,
              vertical: 10.w,
            ),
            child: Row(
              children: [
                Text(
                  "提醒",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textPrimary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Obx(() {
                  final logic = Get.find<MonitorReminderLogic>();
                  return BrnSwitchButton(
                    value: logic.state.reminderEnable.value,
                    onChanged: (bool value) {
                      Log.i("BrnSwitchButton onChanged: $value");
                      logic.onEnableChanged(value);
                    },
                  );
                })
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            color: AppColor.textDisable,
          ),
          Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 10.w,
                vertical: 10.w,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "温馨提示",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  Text(
                    "1. 如有低血糖表现需随时测血糖；\n"
                    "2. 如出现不可解释的空腹高血糖或夜间低血糖，应监测夜间2-3点血糖；\n"
                    "3. 复诊前1天加测5-7个时间点的血糖。",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.textPrimary,
                      fontWeight: FontWeight.w400,
                    ),
                  )
                ],
              ))
        ],
      ),
    );
  }
}
