import 'package:dnurse/app/data/common/monitor_plan.dart';
import 'package:dnurse/app/data/service/reminder_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/exception/dnu_error.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:dnurse_reminder_plugin/dnurse_reminder_plugin.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../user/pages/health/health_guide/widget/single_select_widget.dart';
import '../../entity/reminder/reminder_plan.dart';
import 'state.dart';

class MonitorReminderLogic extends GetxController {
  final MonitorReminderState state = MonitorReminderState();
   List<ReminderPlan> userCustomPlan = [];

  @override
  void onInit() {
    super.onInit();

    state.reminderEnable.value = ReminderService.to.currentMonitorEnable.value;
    state.plan.value = MonitorPlanType.getOptions()
        .indexOf(ReminderService.to.currentMonitorPlanType.value);

    for (ReminderPlan reminderPlan in ReminderService.to.currentMonitorPlan) {
      ReminderPlan planClone = reminderPlan.copyWith();
      state.reminder[planClone.timePoint] = planClone;
    }

    checkCustomPlan(ReminderService.to.currentMonitorPlan);

    userCustomPlan = ReminderService.to.currentMonitorPlan
        .map((element) => element.copyWith())
        .toList();
    ReminderService.to
        .loadUserCustomReminderPlan(AppContext.to.userSn, userCustomPlan);

    DnurseReminderPlugin.getInstance().requestNotificationPermission();
  }

  void _loadData() async {}

  void checkCustomPlan(List<ReminderPlan> plans) {
    MonitorPlanType nowPlanType = state.planType;
    bool isCustom = true;
    for (MonitorPlanType monitorPlanType in MonitorPlanType.getOptions()) {
      bool isType = true;
      for (ReminderPlan reminder in plans) {
        ReminderPlan newReminder = ReminderPlan.getDefault(reminder.timePoint);
        monitorPlanType.fillReminderPlan(newReminder);

        if (reminder.enabled != newReminder.enabled ) {
          isType = false;
        }
      }

      if (isType) {
        nowPlanType = monitorPlanType;
        isCustom = false;
        break;
      }
    }

    if (isCustom) {
      state.plan.value = MonitorPlanType.getOptions().indexOf(nowPlanType);

      ReminderService.to
          .saveUserCustomReminderPlan(AppContext.to.userSn, plans);
    }
  }

  void onReminderChanged(List<ReminderPlan> changedList) {
    for (ReminderPlan reminder in changedList) {
      state.reminder[reminder.timePoint] = reminder;
    }

    checkPlanType();
  }

  void checkPlanType() {
    if (state.planType != MonitorPlanType.custom) {
      state.plan.value =
          MonitorPlanType.getOptions().indexOf(MonitorPlanType.custom);
    } else {
      List<ReminderPlan> plans = state.reminder.values.toList();
      plans.sort((a, b) => a.timePoint.id.compareTo(b.timePoint.id));

      MonitorPlanType nowPlanType = state.planType;
      for (MonitorPlanType monitorPlanType in MonitorPlanType.getOptions()) {
        bool isType = true;
        for (ReminderPlan reminder in plans) {
          ReminderPlan newReminder =
              ReminderPlan.getDefault(reminder.timePoint);
          monitorPlanType.fillReminderPlan(newReminder);

          if (reminder.enabled != newReminder.enabled ) {
            isType = false;
          }
        }

        if (isType) {
          nowPlanType = monitorPlanType;
          break;
        }
      }

      if (nowPlanType != state.planType) {
        state.plan.value = MonitorPlanType.getOptions().indexOf(nowPlanType);
      }

      saveCustom();
    }
  }

  void saveCustom() {
    if (state.planType != MonitorPlanType.custom) {
      return;
    }

    userCustomPlan = state.reminder.values.map((e) => e.copyWith()).toList();

    ReminderService.to
        .saveUserCustomReminderPlan(AppContext.to.userSn, userCustomPlan);
  }


  void onPlanChanged(SelectedChangedEvent event) {
    state.plan.value = event.clickedIndex;
    MonitorPlanType planType = state.planType;
    if (planType != MonitorPlanType.custom) {
      for (var element in state.reminder.values) {
        planType.fillReminderPlan(element);
      }
      state.reminder.refresh();
    } else {
      state.reminder.clear();
      for (ReminderPlan plan in userCustomPlan) {
        ReminderPlan planClone = plan.copyWith();
        state.reminder[planClone.timePoint] = planClone;
      }
    }
  }

  void onEnableChanged(bool value) {
    state.reminderEnable.value = value;

    ReminderService.to.downloadMonitor();
  }

  void onSaveClicked(BuildContext context) async {
    try {
      if (context.mounted) {
        DNULoading.show(context, content: "保存中...");
      }

      List<ReminderPlan> plans = state.reminder.values.toList();
      plans.sort((a, b) => a.timePoint.id.compareTo(b.timePoint.id));

      await ReminderService.to.saveMonitorPlan(
        plans: plans,
        monitorEnable: state.reminderEnable.value,
        monitorType: state.planType,
      );

      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show("保存成功", context);
      }

      AppRouter.back();
    } on DNUError catch (e) {
      if (context.mounted) {
        DNULoading.dismiss(context);
        DNUToast.show(e.message, context);
      }
    }
  }

  bool checkChanged() {
    if (state.reminderEnable.value !=
        ReminderService.to.currentMonitorEnable.value) {
      return true;
    }
    if (state.plan.value !=
        MonitorPlanType.getOptions()
            .indexOf(ReminderService.to.currentMonitorPlanType.value)) {
      return true;
    }

    List<ReminderPlan> plans = state.reminder.values.toList();
    plans.sort((a, b) => a.timePoint.id.compareTo(b.timePoint.id));

    List<ReminderPlan> oldPlans = [];
    for (ReminderPlan reminderPlan in ReminderService.to.currentMonitorPlan) {
      ReminderPlan planClone = reminderPlan.copyWith();
      oldPlans.add(planClone);
    }
    oldPlans.sort((a, b) => a.timePoint.id.compareTo(b.timePoint.id));
    if (plans.length != oldPlans.length) {
      return true;
    }

    for (int i = 0; i < plans.length; i++) {
      if (plans[i] != oldPlans[i]) {
        return true;
      }
    }
    return false;
  }
}
