import 'package:get/get.dart';

import '../../../../bootstrap/app.dart';
import '../../entity/spug_sample_type.dart';

class DataTestMainState {
  DataTestMainState() {
    ///Initialize variables
  }

  final gifPath = 'assets/images/data/test/plugin_device.gif'.obs;
  final testStateStr = AppContext.to.getLocale().str_plugin_device.obs;
  final showBuyBtn = true.obs;
  final showSettingBtn = false.obs;
  final showWakeupBtn = false.obs;

  final testStep = 0.obs;   //0,请插设备；1,请插试纸；2,请滴血；3,已滴血

  int testType = 0;
  final selCorrectingCode = 601.obs;
  final sampleType = SPUGSampleType.BLOOD.obs;

  bool jumpToResult = true;   //跳转到结果页标识，防止重复跳转
}
