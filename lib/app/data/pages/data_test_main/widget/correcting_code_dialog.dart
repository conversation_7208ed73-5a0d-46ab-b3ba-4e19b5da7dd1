import 'package:bruno/bruno.dart';
import 'package:dnurse/ui/widgets/button/dnu_white_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../ui/style/color.dart';

typedef ConfirmCorrectingCodeCallback = void Function(int selCorrectingCode);

class CorrectingCodeDialog extends StatelessWidget {
  CorrectingCodeDialog({super.key, required int code, this.onConfirm}) {
    if (code < correctingCodeStart || code > _correctingCodeEnd) {
      code = correctingCodeStart;
    }
    correctingCode.value = code;
  }

  final Rx<int> correctingCode = 601.obs;
  final ConfirmCorrectingCodeCallback? onConfirm;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.sp),
        ),
        width: 270.sp,
        height: 295.sp,
        padding: EdgeInsets.zero,
        clipBehavior: Clip.antiAlias,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/data/test/correcting_code_exp.png',
              width: 270.sp,
              height: 150.sp,
              fit: BoxFit.cover,
            ),
            Container(
              margin: EdgeInsets.only(top: 15.sp),
              height: 22.sp,
              child: Text(
                '当前校正码',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColor.textPrimary,
                ),
              ),
            ),
            Obx(
              () => SizedBox(
                height: 63.sp,
                child: Text(
                  '${correctingCode.value}',
                  style: TextStyle(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColor.textPrimary,
                    height: 1.5,
                  ),
                ),
              ),
            ),
            Divider(height: 1.sp, thickness: 1, color: AppColor.lineColor),
            Row(
              children: [
                Expanded(
                  child: DNUWhiteButton(
                    title: '修改校正码',
                    titleColor: AppColor.textDesc,
                    height: 44.sp,
                    borderRadius: 0,
                    backgroundBoxShadow: const [],
                    onPressed: () {
                      showSelectCorrectingCodePicker(context, correctingCode.value - correctingCodeStart, (int v) {
                        correctingCode.value = v;
                      });
                    },
                  ),
                ),
                // Divider(height: 44.sp, thickness: 1,),
                Container(
                  width: 1.sp,
                  height: 44.sp,
                  color: AppColor.lineColor,
                ),
                Expanded(
                  child: DNUWhiteButton(
                    title: '完成',
                    titleColor: AppColor.primary,
                    height: 44.sp,
                    borderRadius: 0,
                    backgroundBoxShadow: const [],
                    onPressed: () {
                      if (onConfirm != null) {
                        Get.back();
                        onConfirm!(correctingCode.value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static showSelectCorrectingCodePicker(BuildContext context, int initSelIndex, ConfirmCorrectingCodeCallback selCallback) {
    if (initSelIndex < 0 || initSelIndex >= _correctingCodeValues.length) {
      initSelIndex = 0;
    }

    BrnMultiDataPicker(
      context: context,
      title: '设置校正码',
      titleTextStyle: TextStyle(
        color: AppColor.textSecondary,
        fontWeight: FontWeight.w400,
        fontSize: 16.sp,
      ),
      delegate: CorrectingCodeDelegate(initSelIndex),
      createItemWidget:
          (bool isSelect, int column, int row, List selectedItems) {
        return Center(
          child: Text(
            '${_correctingCodeValues[row]}',
            style: TextStyle(
              color: isSelect ? AppColor.textPrimary : AppColor.textDesc,
              fontWeight: isSelect ? FontWeight.w500 : FontWeight.w400,
              fontSize: isSelect ? 20.sp : 16.sp,
            ),
          ),
        );
      },
      confirmClick: (list) {
        if (selCallback != null) {
          selCallback(_correctingCodeValues[list.first]);
        }
      },
    ).show();
  }
}

const int correctingCodeStart = 601;
const int _correctingCodeEnd = 699;
final List<int> _correctingCodeValues = List.generate(
    _correctingCodeEnd - correctingCodeStart + 1,
    (i) => correctingCodeStart + i);

class CorrectingCodeDelegate implements BrnMultiDataPickerDelegate {
  final int firstSelectedIndex;

  CorrectingCodeDelegate(this.firstSelectedIndex);

  @override
  int initSelectedRowForComponent(int component) {
    return firstSelectedIndex;
  }

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return _correctingCodeValues.length;
  }

  @override
  double? rowHeightForComponent(int component) {
    return 40.sp;
  }

  @override
  void selectRowInComponent(int component, int row) {}

  @override
  String titleForRowInComponent(int component, int index) {
    return '${_correctingCodeValues[index]}';
  }
}
