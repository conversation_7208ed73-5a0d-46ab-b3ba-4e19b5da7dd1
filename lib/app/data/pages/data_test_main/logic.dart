import 'dart:async';
import 'dart:io';

import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/app/data/pages/data_test_main/widget/correcting_code_dialog.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:dnurse_spug_plugin/dnurse_spug_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../bootstrap/app.dart';
import '../../../../framework/service/dialog_manager.dart';
import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../event/bs_state_changed.dart';
import '../../event/spug_state_changed.dart';
import '../../service/data_service.dart';
import 'state.dart';

class DataTestMainLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final DataTestMainState state = DataTestMainState();

  late TabController pointTabController;

  late StreamSubscription _spugStateListen;
  late StreamSubscription _bsStateListen;

  @override
  void onInit() {
    super.onInit();

    pointTabController =
        TabController(vsync: this, length: TimePoint.values.length);
    pointTabController.addListener(() {});

    //设置SPUG最后状态
    _onSPUGStateChanged(DataService.to.lastSPUGState);

    initListen();

    _initPermission();
  }

  @override
  void onClose() {
    cancelListen();

    super.onClose();
  }

  /// 申请麦克风权限，用于识别手机血糖仪
  Future<void> _initPermission() async {
    if (Platform.isIOS) {
      return;
    }

    const microphonePermission = Permission.microphone; //录音权限
    var status = await microphonePermission.status;
    if (!status.isGranted) {
      bool request = false;
      await DNUDialog.showConfirm(
        title: '申请使用录音权限',
        content: '需要您允许录音权限，才能使用糖护士手机血糖仪',
        cancel: '不允许',
        confirm: '允许',
        onConfirm: () {
          request = true;
        },
      );

      if (!request) {
        return;
      }

      status = await microphonePermission.request();
    }
    Log.d('是否同意麦克风权限：${status.isGranted}');
  }

  List<Tab> getTimePointTabs() {
    List<Tab> mTabs = [];

    for (TimePoint item in TimePoint.values) {
      mTabs.add(Tab(
        height: 32.sp,
        text: item.name,
        // icon: Icon(
        //   DNUIconFont.recipe,
        //   size: 30.w,
        // ),
        // iconMargin: EdgeInsets.only(bottom: 4.w),
      ));
    }

    return mTabs;
  }

  void initListen() {
    _spugStateListen =
        EventService.to.bus.on<SPUGStateChanged>().listen(_onSPUGStateChanged);
    _bsStateListen =
        EventService.to.bus.on<BSStateChanged>().listen(_onBSStateChanged);
  }

  void cancelListen() {
    _spugStateListen.cancel();
    _bsStateListen.cancel();
  }

  void _onSPUGStateChanged(SPUGStateChanged event) {
    Log.d('测量页面处理SPUG通知：${event.state}');
    dynamic data = event.data;
    Log.d('测量页面处理SPUG通知数据：$data');
    switch (event.state) {
      case 1:
        {
          state.gifPath.value = _getImgPath('spug_plugin_paper');
          state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
          state.testStep.value = 1;
        }
        break;

      case 2:
        {
          int errCode = data['ErrorCode'];
          if (errCode == 0x0002) {  //温度超出范围
            state.testStateStr.value = '温度超出范围';
            DNUDialog.showAlert(
                title: '温度超出范围', content: '请将血糖尿酸仪在室温放置15分钟后再使用', confirm: '好的');
          } else if (errCode == 0x0203) {   //温差超出范围
            state.testStateStr.value = '温差超出范围';
            DNUDialog.showAlert(
                title: '温差超出范围', content: '请稍后再使用', confirm: '好的');
          } else if (errCode == 0x0003) {   //试纸已被使用或不能用
            state.testStateStr.value = '请更换试纸';
            DNUDialog.showAlert(
                title: '试条有问题~', content: '用过的或者受潮的试条\n不能再使用了', confirm: '好的');
          } else if (errCode == 0x0004) {   //通信失败
            state.testStateStr.value = '通信失败';
            DNUDialog.showAlert(
                title: '通信失败', content: '通信失败\n请确认连接糖护士血糖尿酸仪', confirm: '好的');
          } else if (errCode == 0x0104) {   //校正码参数错误
            state.testStateStr.value = '校正码参数错误';
            DNUDialog.showAlert(
                title: '校正码参数错误', content: '校正码参数错误\n请联系官方客服', confirm: '好的');
          } else if (errCode == 0x0101 || errCode == 0x0102) {   //hi，low值
            //不处理
            // state.testStateStr.value = '请更换试纸';
            // DNUDialog.showAlert(
            //     title: '试条有问题~', content: '用过的或者受潮的试条\n不能再使用了', confirm: '好的');
          } else if (errCode == 0x0011) {   //吸样等待超时
            state.testStateStr.value = '请更换试条';
            DNUDialog.showAlert(
                title: '请更换试条~', content: '吸样等待超时\n请更换试条', confirm: '好的');
          } else {
            DNUDialog.showAlert(
                title: '发生未处理错误', content: '错误码：$errCode', confirm: '好的');
          }
        }
        break;

      case 3:
        {
          state.testType = data['TestType'];
          if (state.testType == 1) {
            state.gifPath.value = _getImgPath('spug_drop_blood_ua');
            int code = data['RectifyCode'];
            if (code == -1) {
              code = 601;
            }
            state.selCorrectingCode.value = code;

            showCorrectingCodeDialog();
          } else {
            state.gifPath.value = _getImgPath('spug_drop_blood');
          }
          state.testStateStr.value = AppContext.to.getLocale().str_qingdixue;
          state.testStep.value = 2;
          state.showBuyBtn.value = false;
          state.showSettingBtn.value = true;
        }
        break;

      case 4:
        {
          double value = data['TestValue'];
          int dataTime = data['DateTime'];
          DnurseSpugPlugin.getInstance().receiveSPUGData(dataTime);
          Log.d('dnurseSpugPlugin-测量出值：$value');
        }
        break;

      case 5:
        {
          state.testStateStr.value = AppContext.to.getLocale().str_shizhizijian;
        }
        break;

      case 10:
        {
          if (!state.jumpToResult) {
            Log.d('重复接收到测量消息，不跳转界面');
            return;
          }

          state.jumpToResult = false;
          AppRouter.replace(Routes.dataTestLoading,
              parameters: {'testType': '${state.testType}', 'deviceType': '2'});
        }
        break;

      case 11:
        {
          state.gifPath.value = _getImgPath('spug_plugin_paper');
          state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
          state.testStep.value = 1;

          state.showSettingBtn.value = false;
          state.showBuyBtn.value = true;
        }
        break;

      case 98:
        {
          state.gifPath.value = _getImgPath('plugin_device');
          state.testStateStr.value =
              AppContext.to.getLocale().str_plugin_device;
          state.testStep.value = 0;
          state.showSettingBtn.value = false;
          state.showBuyBtn.value = true;
        }
        break;
    }
  }

  showCorrectingCodeDialog() {
    DialogManager.to.showDialog(
      CorrectingCodeDialog(
        code: state.selCorrectingCode.value,
        onConfirm: (int v) {
          Log.d('确认选择校正码：$v');
          state.selCorrectingCode.value = v;

          DnurseSpugPlugin.getInstance().setRectifyCodeAndSampleType({
            'code': '${state.selCorrectingCode.value}',
            'type': '1'
          });
        },
      ),
      barrierDismissible: false,
      routeSettings: const RouteSettings(name: 'CorrectingCodeDialog'),
    );
  }

  showSelectCorrectingCodePicker() {
    CorrectingCodeDialog.showSelectCorrectingCodePicker(Get.global(null).currentContext!, state.selCorrectingCode.value - correctingCodeStart, (selCorrectingCode) {
      state.selCorrectingCode.value = selCorrectingCode;

      DnurseSpugPlugin.getInstance().setRectifyCodeAndSampleType({
        'code': '${state.selCorrectingCode.value}',
        'type': '1'
      });
    });
  }

  String _getImgPath(String name) {
    if (Platform.isIOS) {
      name = '${name}_ios';
    }

    return 'assets/images/data/test/$name.gif';
  }

  void _onBSStateChanged(BSStateChanged event) {
    Log.d('测量页面处理手机血糖仪通知：${event.state}');
    dynamic data = event.data;
    switch (event.state) {
      case 0:
        {
          state.gifPath.value = _getImgPath('plugin_device');
          state.testStateStr.value =
              AppContext.to.getLocale().str_plugin_device;
          state.testStep.value = 0;
          state.showSettingBtn.value = false;
          state.showBuyBtn.value = true;
          state.showWakeupBtn.value = false;
        }
        break;

      case 1:
        {
          state.testStateStr.value = AppContext.to.getLocale().str_shibiexuety;
          state.showWakeupBtn.value = false;
        }
        break;

      case 3:
        {
          state.gifPath.value = 'assets/images/data/test/bs_plugin_paper.gif';
          state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
          state.testStep.value = 1;
        }
        break;

      case 5:
        {
          state.testStateStr.value =
              AppContext.to.getLocale().str_shizhiyishiyong;
        }
        break;

      case 6:
        {
          state.gifPath.value = 'assets/images/data/test/bs_drop_blood.gif';
          state.testStateStr.value = AppContext.to.getLocale().str_qingdixue;
          state.testStep.value = 2;
          state.showBuyBtn.value = false;
          // state.showSettingBtn.value = true;
        }
        break;

      case 7:
        {
          state.gifPath.value = 'assets/images/data/test/bs_plugin_paper.gif';
          state.testStateStr.value = AppContext.to.getLocale().str_plugin_paper;
          state.testStep.value = 1;

          state.showSettingBtn.value = false;
          state.showBuyBtn.value = true;
        }
        break;

      case 8:
        {
          if (!state.jumpToResult) {
            Log.d('重复接收到测量消息，不跳转界面');
            return;
          }

          state.jumpToResult = false;
          AppRouter.replace(Routes.dataTestLoading,
              parameters: {'testType': '0', 'deviceType': '1'});
        }
        break;

      case 9:
        {
          // double value = data['TestValue'];
          // int dataTime = data['DateTime'];
          // Log.d('手机血糖仪-测量出值：$value');
        }
        break;

      case 18:
        {
          state.gifPath.value = 'assets/images/data/test/bs_device_sleep.gif';
          state.testStateStr.value = AppContext.to.getLocale().str_device_sleep;
          state.showWakeupBtn.value = true;
        }
        break;

      case 23:
        state.testStateStr.value = AppContext.to.getLocale().str_audio_rec_deny;
        break;
    }
  }
}
