import 'package:dnurse/app/common/utils/router_utils.dart';
import 'package:dnurse/framework/utils/network_utils.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/button/dnu_outline_button.dart';
import 'package:dnurse_bs_test_plugin/dnurse_bs_test_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../bootstrap/app.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../ui/widgets/round_underline_tab_indicator.dart';
import 'logic.dart';

class DataTestMainPage extends StatelessWidget {
  DataTestMainPage({Key? key}) : super(key: key);

  final logic = Get.find<DataTestMainLogic>();
  final state = Get.find<DataTestMainLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
          context: context,
          title: AppContext.to.getLocale().str_thshcl,
          backgroundColor: Colors.white,
          backButtonColor: AppColor.textPrimary,
          titleColor: AppColor.textPrimary),
      body: SafeArea(
        child: Column(
          children: [
            _timePoint(),
            _gifContent(),
            _stateContent(),
            _stepContent(),
            _wakeupDeviceBtn(),
            const Spacer(),
            _buttonContent(context),
            _settingBtn(),
          ],
        ),
      ),
    );
  }

  Widget _timePoint() {
    return TabBar(
      controller: logic.pointTabController,
      tabs: logic.getTimePointTabs(),
      labelStyle: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        height: 1.5
      ),
      labelColor: AppColor.primary,
      unselectedLabelColor: AppColor.textPrimary,
      isScrollable: true,
      indicatorColor: Colors.transparent,
      // indicatorWeight: 1,
      indicator: const RoundUnderlineTabIndicator(
          fixWidth: 6,
          borderSide: BorderSide(width: 6, color: AppColor.primary)),
      labelPadding: EdgeInsets.symmetric(horizontal: 15.sp),
      padding: EdgeInsets.only(bottom: 6.sp, top: 0),
    );
  }

  Widget _gifContent() {
    return Container(
      width: double.infinity,
      height: 235.h,
      color: AppColor.background1,
      child: Obx(
        () => Image.asset(
          state.gifPath.value,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _stateContent() {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(top: 60.h),
        child: Text(
          state.testStateStr.value,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w500,
            color: AppColor.textPrimary,
          ),
        ),
      ),
    );
  }

  Widget _stepContent() {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(top: 17.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _stepItem('assets/images/data/test/step1_plugined_device.png',
                state.testStep > 0),
            SizedBox(
              width: 45.w,
              child: Icon(
                DNUIconFont.qianwang,
                size: 24.sp,
                color: AppColor.textDisable,
              ),
            ),
            _stepItem(
                state.testStep > 0
                    ? 'assets/images/data/test/step2_plugined_paper.png'
                    : 'assets/images/data/test/step2_plugin_paper.png',
                state.testStep > 1),
            SizedBox(
              width: 45.w,
              child: Icon(
                DNUIconFont.qianwang,
                size: 24.sp,
                color: AppColor.textDisable,
              ),
            ),
            _stepItem(
                state.testStep > 1
                    ? 'assets/images/data/test/step3_droped_blood.png'
                    : 'assets/images/data/test/step3_drop_blood.png',
                state.testStep > 2),
          ],
        ),
      ),
    );
  }

  Widget _stepItem(String imgPath, bool done) {
    return Stack(
      children: [
        Image.asset(
          imgPath,
          width: 55.sp,
          height: 55.sp,
        ),
        Visibility(
          visible: done,
          child: Image.asset(
            'assets/images/data/test/step_ok.png',
            width: 55.sp,
            height: 55.sp,
          ),
        ),
      ],
    );
  }

  Widget _buttonContent(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: state.showBuyBtn.value,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 15.sp),
          child: Row(
            children: [
              Expanded(
                child: DNUPrimaryButton(
                  height: 40.sp,
                  title: AppContext.to.getLocale().str_buy_now,
                  disable: false,
                  onPressed: () {
                    NetworkUtils.checkState(
                      context: context,
                      onSuccess: () {
                        RouterUtils.gotoGlucoseList();
                      },
                    );
                  },
                ),
              ),
              SizedBox(
                width: 20.sp,
              ),
              Expanded(
                child: DNUPrimaryButton(
                  backgroundColor: AppColor.auxiliaryGreen1,
                  height: 40.sp,
                  title: AppContext.to.getLocale().str_use_guide,
                  disable: false,
                  onPressed: () {
                    AppRouter.push(Routes.spugGuide);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _settingBtn() {
    return Obx(
      () => Visibility(
        visible: state.showSettingBtn.value,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 15.sp),
          child: Row(
            children: [
              Expanded(
                child: DNUOutlineButton(
                  height: 40.sp,
                  borderRadius: 5.sp,
                  title:
                      '${AppContext.to.getLocale().str_jiaozhengma}: ${state.selCorrectingCode.value}',
                  disable: false,
                  onPressed: () {
                    logic.showSelectCorrectingCodePicker();
                  },
                ),
              ),
              SizedBox(
                width: 20.sp,
              ),
              Expanded(
                child: DNUOutlineButton(
                  height: 40.sp,
                  borderRadius: 5.sp,
                  title: AppContext.to.getLocale().str_yangbenleixing,
                  iconRight: state.sampleType.value.icon,
                  disable: false,
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _wakeupDeviceBtn() {
    return Obx(
      () => Visibility(
        visible: state.showWakeupBtn.value,
        child: Container(
          margin: EdgeInsets.only(top: 20.sp),
          child: DNUPrimaryButton(
            height: 40.sp,
            width: 200.w,
            title: AppContext.to.getLocale().str_wakeup_device,
            disable: false,
            onPressed: () {
              DnurseBsTestPlugin.getInstance().wakeupDevice();
            },
          ),
        ),
      ),
    );
  }
}
