import 'dart:async';

import 'package:dnurse/app/data/api/data_api.dart';
import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../framework/network/response.dart';
import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../common/web/entity/webview_title_info.dart';
import '../../../common/web/web_utils.dart';
import '../../../common/web/webview_handler.dart';
import '../../../user/event/data_sync_state_changed.dart';
import '../../entity/Data.dart';
import 'state.dart';

class DataResultLogic extends GetxController {
  final DataResultState state = DataResultState();

  late WebViewController webViewController;
  late WebInterfaceHandler webInterfaceHandler;
  bool _hasLoaded = false;
  late Data testBSResult;
  late StreamSubscription dataSyncStateChangedSubscription;

  @override
  void onInit() {
    super.onInit();

    Map<String, dynamic> parameters = Get.arguments;
    if (parameters.isNotEmpty) {
      Data data = parameters['data'];
      testBSResult = data;
    } else {
      testBSResult = DataGlucose.getEmpty();
    }

    _initWeb();
    _initWebHandler();

    dataSyncStateChangedSubscription = EventService.to.bus.on<DataSyncStateChanged>().listen(_handleSyncStateChanged);
    // Future.delayed(const Duration(seconds: 3), _loadBSAdvice);
    // _loadBSAdvice();
  }



  @override
  void dispose() {
    dataSyncStateChangedSubscription.cancel();
    super.dispose();
  }

  void _handleSyncStateChanged(DataSyncStateChanged stateChanged) async {
    if (stateChanged.syncState == SyncState.syncEnd) {
      _loadBSAdvice();
    }
  }

  // void onTimePointSelected(DateTime time, TimePoint point) {
  //   Log.d('选择时间：$time');
  //   Log.d('选择时间段：$point');
  //
  //   state.testBSResult.update((val) {
  //     val!.time = time;
  //     val.timePoint = point;
  //   });
  // }

  Future _loadBSAdvice() async {
    DNULoading.show(Get.context!, content: "正在获取控糖建议");
    Map<String, dynamic> cData = {
      'did': testBSResult.did,
      'point': '${testBSResult.getTimePoint().id}',
      'value': testBSResult.originalValue(),
      'device': "1",
      // (isGuess || _isAddData == NO ? @(2) : @(1)), // 测量设备 1：手输 2：机测
      'need_notice': "1"
      // 是否需要展示广告位 0：不需要 1：需要（测试结果页传1）
    };

    JsonResponse response;
    if (testBSResult.runtimeType == DataGlucose) {
      response = await DataApi.getRecommend(cData);
    } else {
      response = await DataApi.getUARecommend(cData);
    }
    DNULoading.dismiss(Get.context!);
    Log.d('血糖建议：$response');
    if (response.successful && response.hasData) {
      Map<String, dynamic>? data = response.dataMap;
      if (data != null) {
        String htmlStr = state.htmlTmp.replaceAll('%@', data['result']);
        webViewController.loadHtmlString(htmlStr);
      }
    }
  }

  void _initWeb() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setOnJavaScriptAlertDialog((request) async {
        Log.d("setOnJavaScriptAlertDialog: ${request.message}, ${request.url}");

        webInterfaceHandler.handleMessage(request.message);
      });

    webViewController.setNavigationDelegate(
        WebUtils.getNavigationDelegate(onPageFinished: (String url) {
          _hasLoaded = true;
          Log.d('网页加载完成');
          resizeWeb();
        }, onNavigationRequest: (NavigationRequest request) {
          if (request.url.startsWith('http')) {
            AppRouter.push(Routes.browser, arguments: request.url);
          } else {
            launchUrl(Uri.parse(request.url));
          }

          return NavigationDecision.prevent;
        }));

    WebUtils.setUserAgent(webViewController);
    WebUtils.setTokenCookie();
    WebUtils.initGrant(webViewController);
  }

  Future<void> resizeWeb() async {
    // var originalHeight = await webViewController.runJavaScriptReturningResult("document.documentElement.clientHeight;");
    var originalHeight = await webViewController.runJavaScriptReturningResult("document.body.scrollHeight;");
    Log.d('JS运行结果：$originalHeight-> ${originalHeight.runtimeType}');
    state.pageH.value = (double.tryParse('$originalHeight') ?? 300) + 5;
  }

  void _initWebHandler() {
    webInterfaceHandler = WebInterfaceHandler(
      webViewController: webViewController,
      onTitleChanged: _onWebViewTitleChanged,
    );
  }

  void _onWebViewTitleChanged(
      WebViewTitleInfo? titleInfo, Map<String, Object?> appJson) {
    // ShopUpdateHeaderDTO shopUpdateHeaderDTO =
    // ShopUpdateHeaderDTO.fromJson(appJson);
    //
    // if (shopUpdateHeaderDTO.left != null &&
    //     shopUpdateHeaderDTO.left!.has &&
    //     shopUpdateHeaderDTO.left!.child.isNotEmpty) {
    //   ShopHeaderItemDTO itemDTO = shopUpdateHeaderDTO.left!.child.first;
    //   if (itemDTO.iconUrl.isNotEmpty) {
    //     state.headImageUrl.value = itemDTO.iconUrl;
    //   }
    // }
    //
    // WebViewTitleInfo? titleInfo =
    // WebViewInterface.parseTitle(appJson, webViewController, null);
    //
    // state.rightAction.clear();
    // if (titleInfo != null && titleInfo.actions != null) {
    //   state.rightAction.addAll(titleInfo.actions!);
    // }
  }
}
