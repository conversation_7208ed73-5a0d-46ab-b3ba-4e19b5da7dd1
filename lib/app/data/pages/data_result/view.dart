import 'dart:math';

import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/widgets/progress/arc_progress_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../bootstrap/app.dart';
import '../../../../ui/icons/icons.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'logic.dart';

class DataResultPage extends StatefulWidget {
  const DataResultPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DataResultPage();
}

class _DataResultPage extends State<DataResultPage>
    with TickerProviderStateMixin {
  final logic = Get.find<DataResultLogic>();
  final state = Get.find<DataResultLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background1,
      appBar: getAppBar(
          context: context,
          title: AppContext.to.getLocale().str_thshcl,
          backgroundColor: AppColor.background1,
          backButtonColor: AppColor.textPrimary,
          titleColor: AppColor.textPrimary,
          // actions: [
          //   _navActionItem(DNUIconFont.quxian, () {
          //     Log.d('点击曲线');
          //   }),
          //   _navActionItem(DNUIconFont.yuanxingfenxiang, () {
          //     Log.d('点击分享');
          //   }),
          // ],
          onBackPressed: () {
            Navigator.maybePop(context, logic.testBSResult);
          }),
      body: SafeArea(
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            children: [
              _valueView(),
              _timePoint(context),
              _adviseView(),
            ],
          ),
        ),
        // child: ListView(
        //   children: [
        //     _valueView(),
        //     _timePoint(context),
        //     _adviseView(),
        //   ],
        // ),
      ),
    );
  }

  @override
  void dispose() {
    logic.dispose();

    super.dispose();
  }

  Widget _navActionItem(IconData icon, VoidCallback callback) {
    return InkWell(
      onTap: callback,
      child: SizedBox(
        width: 44.sp,
        height: 44.sp,
        child: Icon(
          icon,
          color: AppColor.textPrimary,
          size: 24.sp,
        ),
      ),
    );
  }

  Widget _valueView() {
    return Column(
      children: [
        SizedBox(
          height: 25.sp,
        ),
        Stack(
          alignment: Alignment.topCenter,
          children: [
            SizedBox(
              // color: Colors.red,
              width: 280.sp,
              height: 139.sp,
              child: ArcProgressWidget(
                backgroundColor: AppColor.background2,
                value: logic.testBSResult.getProgressValue(),
                openAngle: pi,
                gradient: logic.testBSResult.getStatusGradientColor(),
                strokeCapRound: true,
                progressbarStrokeWidth: 16.sp,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 44.sp),
              // decoration: BoxDecoration(
              //   color: const Color(0xFFF0F0F6),
              //   borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              // ),
              width: 124.sp,
              child: Text(
                logic.testBSResult.stringValue(),
                style: TextStyle(
                  fontSize: 48.sp,
                  fontWeight: FontWeight.w700,
                  color: logic.testBSResult.getStatusColor(),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 105.sp),
              // decoration: BoxDecoration(
              //   color: const Color(0xFFF0F0F6),
              //   borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              // ),
              // width: 124.sp,
              child: Text(
                logic.testBSResult.unit(),
                style: TextStyle(fontSize: 18.sp, color: AppColor.textDesc),
              ),
            ),
          ],
        ),
        SizedBox(
          width: 269.sp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            // crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                ' ${logic.testBSResult.getMiniValueStr()}',
                style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),
              ),
              Container(
                height: 28.sp,
                padding: EdgeInsets.symmetric(horizontal: 5.sp),
                decoration: BoxDecoration(
                  color: AppColor.background2,
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                ),
                alignment: Alignment.center,
                // width: 124.sp,
                child: Text(
                  logic.testBSResult.sourceStr(),
                  style: TextStyle(fontSize: 14.sp, color: AppColor.textDesc),
                  textAlign: TextAlign.center,
                ),
              ),
              Text(
                logic.testBSResult.getMaxValueStr(),
                style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _timePoint(BuildContext context) {
    return InkWell(
      // onTap: () {
      //   Get.bottomSheet(DataTimePointPickerView(
      //     selectedDate: state.testBS Result.value.time,
      //     selectedPoints: TimePoint.values,
      //     pointsSelectedIndex: state.testBS Result.value.timePoint.id,
      //     onTimePointSelected: logic.onTimePointSelected,
      //   ));
      // },
      child: Container(
        margin: EdgeInsets.only(top: 21.sp),
        height: 31.sp,
        width: 269.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(22.sp),
          gradient: AppColor.primaryBackground132,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
              right: 20.sp,
              child: Icon(
                DNUIconFont.sanjiao01,
                size: 16.sp,
                color: AppColor.textDisable,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                const SizedBox(
                  width: 5,
                ),
                Text(
                  logic.testBSResult.time.formatDate(DateFormat.ABBR_MONTH_DAY),
                  style: TextStyle(fontSize: 16.sp, color: AppColor.primary),
                ),
                Text(
                  logic.testBSResult.time.formatDate(DateFormat.HOUR24_MINUTE),
                  style: TextStyle(fontSize: 16.sp, color: AppColor.primary),
                ),
                Text(
                  logic.testBSResult.getTimePoint().name,
                  style: TextStyle(fontSize: 16.sp, color: AppColor.primary),
                ),
                const SizedBox(
                  width: 5,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _adviseView() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 28.sp, horizontal: 16.sp),
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(20.sp)),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
              color: AppColor.background1,
              borderRadius: BorderRadius.all(Radius.circular(12.sp)),
            ),
            alignment: Alignment.center,
            // width: 124.sp,
            child: Text(
              '以下由糖护士IDSS™智能决策系统\n为您专属定制，仅供参考，请遵医嘱',
              style: TextStyle(fontSize: 14.sp, color: AppColor.textDesc),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(
            height: 28.sp,
          ),
          Obx(
            () => SizedBox(
              height: state.pageH.value,
              child: WebViewWidget(controller: logic.webViewController),
            ),
          ),
          // WebViewWidget(controller: logic.webViewController),
          // Obx(() => AutoSizeWebViewWidget(webViewController: logic.webViewController, initialHeight: state.pageH.value,),),
        ],
      ),
    );
  }
}
