
import 'package:get/get.dart';

class DataResultState {
  DataResultState() {
    ///Initialize variables
  }

  String htmlTmp = '''
  <!doctype html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <title>Document</title>
      <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,width=device-width">
      <style>
          body,p,span,div{margin: 0;padding: 0 ;font: 12px/1.5 "Microsoft Yahei",Thmano,Arial;}
          img{width: 100%}
          i{font-style: normal}
          .proposal {
              padding-bottom: 10px;
              font-size: 18px;
          }
          .proposal p {
              font-size: 18px;
              color: #434a54;
              line-height: 25px;
              margin:10px 0;
          }
           .proposal p span {
              font-size: 18px;
          }
          .proposal p:nth-child(1){
              margin-top: 0;
          }
          .proposal .img-three {
              width: 100%;
              float: left;
              margin-bottom: 10px;
          }
          .proposal .img-three ,
          .proposal .img-line {
              text-align: center;
          }
          .proposal .img-three span {
              width: 32.5%;
              display: inline-block;
          }
          .proposal .img-three span:nth-child(1) {
              float: left;
          }
          .proposal .img-three span:nth-child(3) {
              float: right;
          }
          .proposal .img-line {
              width: 100%;
          }
          .last-line {
              text-align: center;
              font-size: 13px;
              color: #949699;
              background: #f5f7fa;
              padding: 5px 0;
              border-radius: 10px;
              margin:15px 0;
          }
      </style>
  </head>
  <body class="dnurse-box-main">
      <!--
      <div class="last-line">
          以下内容为糖护士IDSS™智能决策系统<br>为您专属定制，仅供参考，请遵医嘱
      </div>
      -->
      %@
      <script>
          \$(function(){
              \$("a").click(function(){
                  \$(this).removeClass('click-after');
                  \$(this).addClass("click-before");
              });
          })
      </script>
  </body>
  </html>
  ''';
  final pageH = 200.0.obs;
}
