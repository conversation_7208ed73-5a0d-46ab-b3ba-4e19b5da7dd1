
import 'package:dnurse/app/data/service/uric_acid_service.dart';
import 'package:dnurse/app/user/entity/unit/uric_acid_unit.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../bootstrap/app.dart';
import '../../../../resource/dnu_assets.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../user/service/user_config_service.dart';
import '../../common/time_point.dart';
import '../../entity/data_uric_acid.dart';
import '../../widget/edit_progress_widget.dart';
import '../widget/data_time_point_picker_view.dart';
import 'logic.dart';

class UaDataAddPage extends StatelessWidget {
  const UaDataAddPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<UaDataAddLogic>();
    final state = Get.find<UaDataAddLogic>().state;
    return Scaffold(
      appBar: getAppBar(
        context: context,
        title: AppContext.to.getLocale().str_jlxt,
        backgroundColor: Colors.white,
        backButtonColor: AppColor.textPrimary,
        titleColor: AppColor.textPrimary,
        // actions: [
        //   Row(
        //     children: [
        //       SizedBox(
        //         // width: width ?? 30.w,
        //         // height: height ?? 24.w,
        //         child: ShaderMask(
        //           blendMode: BlendMode.srcATop,
        //           shaderCallback: (bounds) {
        //             return const LinearGradient(
        //               begin: Alignment.bottomLeft,
        //               end: Alignment.topRight,
        //               colors: [
        //                 AppColor.primary,
        //                 AppColor.primaryLight,
        //               ],
        //             ).createShader(bounds);
        //           },
        //           child: const Icon(DNUIconFont.shipuhantangliang),
        //         ),
        //       ),
        //       Text(AppContext.to.getLocale().str_bdyj, style: TextStyle(fontSize: 16.sp, color: AppColor.textPrimary, fontWeight: FontWeight.w500),),
        //     ],
        //   )
        // ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Column(
              children: [
                timePoint(context),
                SizedBox(height: 44.sp,),
                // valueView(),
                Obx(() => EditProgressWidget(
                  width: 313.sp,
                  height: 168.sp,
                  progressValue: state.dataBS.value.getProgressValue(),
                  progressGradient: state.dataBS.value.getValueStatus(UricAcidService.to.currentUricAcidDataTarget.value).gradientColor,
                  hintText: '',
                  unitStr: UserConfigService.to.uricAcidUnit.value.name,
                  miniStr: DataUricAcid.formatMole(DataUricAcid.minValue, UserConfigService.to.uricAcidUnit.value, withHighLow: false),
                  maxStr: DataUricAcid.formatMole(DataUricAcid.maxValue, UserConfigService.to.uricAcidUnit.value, withHighLow: false),
                  editTextColor: state.dataBS.value.getValueStatus(UricAcidService.to.currentUricAcidDataTarget.value).color,
                  decimalPlaces: UserConfigService.to.uricAcidUnit.value == UricAcidUnit.mole ? 0 : 1,
                  onEditChanged: logic.onTextChanged,
                  editController: logic.editController,
                ),),
                // testBtn(),
                saveBtn(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget timePoint(BuildContext context) {
    final logic = Get.find<UaDataAddLogic>();
    final state = Get.find<UaDataAddLogic>().state;
    return InkWell(
      onTap: () {
        Get.bottomSheet(DataTimePointPickerView(
          selectedDate: state.dataBS.value.time,
          selectedPoints: TimePoint.values,
          pointsSelectedIndex: state.dataBS.value.timePoint.id,
          onTimePointSelected: logic.onTimePointSelected,
        ));
      },
      child: Container(
        margin: EdgeInsets.only(left: 44.sp, right: 44.sp, top: 28.sp),
        height: 44.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(22.sp),
          gradient: AppColor.primaryBackground132,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(right: 20.sp, child: Icon(DNUIconFont.sanjiao01, size: 16.sp, color: AppColor.textDisable,),),
            Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                const SizedBox(width: 5,),
                Text(state.dataBS.value.time.formatDate(DateFormat.ABBR_MONTH_DAY), style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                Text(state.dataBS.value.time.formatDate(DateFormat.HOUR24_MINUTE), style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                Text(state.dataBS.value.timePoint.name, style: TextStyle(fontSize: 16.sp, color: AppColor.primary),),
                const SizedBox(width: 5,),
              ],
            ),),
          ],
        ),
      ),
    );
  }

  Widget testBtn() {
    return InkWell(
      onTap: () {
        AppRouter.push(Routes.dataTest);
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.sp, right: 20.sp, top: 48.sp),
        height: 64.sp,
        decoration: BoxDecoration(
          // color: Colors.red,
          // shape: BoxShape.circle,
          borderRadius: BorderRadius.circular(32.sp),
          gradient: AppColor.primaryBackgroundDarkLight10,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Image.asset(DNUAssets.to.images.user.deviceSpug),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(AppContext.to.getLocale().str_thshjc, style: TextStyle(fontSize: 18.sp, color: AppColor.textPrimary, fontWeight: FontWeight.w500),),
                SizedBox(height: 4.sp,),
                Text(AppContext.to.getLocale().str_shjxty_spug, style: TextStyle(fontSize: 12.sp, color: AppColor.textDesc),),
              ],
            ),
            const SizedBox(width: 5,),
            Container(
              width: 36.sp,
              height: 36.sp,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    Color(0x803947E5),
                    Color(0x804284FF),
                  ],
                ),
              ),
              child: const Icon(DNUIconFont.qianwang, color: Colors.white,),
            )
          ],
        ),
      ),
    );
  }

  Widget saveBtn() {
    final logic = Get.find<UaDataAddLogic>();
    final state = Get.find<UaDataAddLogic>().state;

    return Obx(
      () => Visibility(
        visible: state.dataBS.value.isValidate,
        child: Container(
          margin: EdgeInsets.only(top: 20.sp),
          padding: EdgeInsets.symmetric(horizontal: 20.sp),
          child: DNUPrimaryButton(
            title: AppContext.to.getLocale().save,
            iconLeft: DNUIconFont.wancheng,
            onPressed: () {
              logic.saveData();
            },
          ),
        ),
      ),
    );
  }
}
