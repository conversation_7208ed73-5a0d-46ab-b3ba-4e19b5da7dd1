import 'package:dnurse/app/data/service/uric_acid_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../../../../framework/utils/log.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../user/entity/unit/uric_acid_unit.dart';
import '../../../user/service/user_config_service.dart';
import '../../common/time_point.dart';
import '../../entity/data_uric_acid.dart';
import '../../service/glucose_service.dart';
import 'state.dart';

class UaDataAddLogic extends GetxController {
  final UaDataAddState state = UaDataAddState();

  late TextEditingController editController;

  @override
  void onInit() {
    super.onInit();

    editController = TextEditingController();

    Map<String, dynamic>? arg = Get.arguments;
    if (arg != null && arg.isNotEmpty) {
      DataUricAcid data = arg['editData'];
      if (data != null && data.runtimeType == DataUricAcid) {
        state.dataBS.value = data;

        editController.text = state.dataBS.value.stringValue();
      }
    }

    if (!state.dataBS.value.isValidate) {
      initDataPoint();
    }

    debounce(state.inputValue, (String newValue) {
      checkInputValue(newValue);
    }, time: const Duration(milliseconds: 100));

    // debounce(state.dataBS, (callback) {
    //   Log.d('血糖数据被修改');
    //   state.valueStatus.value = state.dataBS.value.getValueStatus(GlucoseService.to.currentDataTarget.value);
    // }, time: const Duration(milliseconds: 100));
  }

  void initDataPoint() {
    state.dataBS.update((val) {
      val!.timePoint = GlucoseService.to.getCurUserTimePointByDateTime(DateTime.now());
    });
  }

  void onTimePointSelected(DateTime time, TimePoint point) {
    Log.d('选择时间：$time');
    Log.d('选择时间段：$point');

    state.dataBS.update((val) {
      val!.time = time;
      val.timePoint = point;
    });
  }

  void onTextChanged(String value) {
    state.inputValue.value = value;
  }

  void checkInputValue(String strValue) {
    String? replaceValue;
    int inputValue = 0;

    if (strValue.trim() != '') {
      try {
        if (UserConfigService.to.uricAcidUnit.value == UricAcidUnit.mole) {
          inputValue = int.parse(strValue);
        } else {
          double inputValueI = double.parse(strValue);
          inputValue = (inputValueI / DataUricAcid.moleToMgRatio).round();
        }
      } catch (e) {
        if (kDebugMode) {
          Log.d('输入血糖值格式化错误：$e');
        }
      }
    }

    if (inputValue > DataUricAcid.maxValue) {
      inputValue = DataUricAcid.maxValue;
      replaceValue = DataUricAcid.formatMole(inputValue, UserConfigService.to.uricAcidUnit.value, withHighLow: false);
    }
    //else-if 为新加的，处理小于最小值时数据当无效处理
    else if (inputValue < DataUricAcid.minValue) {
      inputValue = 0;
    }
    state.dataBS.update((val) {
      val!.mmolValue = inputValue;
    });
    // state.dataBS.value.mmolValue = inputValue;

    if (replaceValue != null) {
      editController.value = TextEditingValue(
          text: replaceValue,
          selection: TextSelection.collapsed(offset: replaceValue.length));
    }
  }

  void saveData() async {
    Log.d('保存血糖值到数据库：${state.dataBS.value}');
    if (state.dataBS.value.did.isEmpty) {
      Log.d('数据的did为空，生成did');
      state.dataBS.value.generateDid();
    }
    // GlucoseProvider.instance.insert(GlucoseDataConvertor.toModel(state.dataBS.value, AppContext.to.userSn));

    DataUricAcid data = await UricAcidService.to.saveUaData(state.dataBS.value, AppContext.to.userSn);

    AppRouter.replace(Routes.dataResult, arguments: {'data': data});
  }
}
