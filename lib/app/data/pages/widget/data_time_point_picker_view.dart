
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/widgets/button/dnu_white_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../../bootstrap/app.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';
import '../../common/time_point.dart';

typedef TimePointSelected = void Function(DateTime time, TimePoint point);

class DataTimePointPickerView extends StatefulWidget {
  DataTimePointPickerView({
    Key? key,
    required this.selectedDate,
    DateTime? minimumDate,
    DateTime? maximumDate,
    Color? backgroundColor,
    required this.selectedPoints,
    required this.pointsSelectedIndex,
    required this.onTimePointSelected,
  })  : minimumDate = minimumDate ?? DateTime(2022, 5, 1),
        maximumDate = maximumDate ?? DateTime.now(),
        super(key: key);

  final DateTime selectedDate;

  final DateTime minimumDate;

  final DateTime maximumDate;

  final List<TimePoint> selectedPoints;

  final int pointsSelectedIndex;

  final TimePointSelected onTimePointSelected;

  @override
  State<DataTimePointPickerView> createState() =>
      _DataTimePointPickerViewState();
}

class _DataTimePointPickerViewState extends State<DataTimePointPickerView> {
  List<DateTime> dateItem = [];
  List<int> hours = [];
  List<int> minutes = [];
  List<TimePoint> selectedPoints = [];

  int dateSelectedIndex = 0;
  int hourSelectedIndex = 0;
  int minuteSelectedIndex = 0;
  int pointsSelectedIndex = 0;

  late DateTime selectedDateTime;

  final timeChanged = 0.obs;

  late FixedExtentScrollController _dateController;

  /// This widget's month selection and animation state.
  late FixedExtentScrollController _hourController;

  /// This widget's day selection and animation state.
  late FixedExtentScrollController _minuteController;

  /// This widget's day selection and animation state.
  late FixedExtentScrollController _pointsController;

  @override
  void initState() {
    super.initState();

    selectedPoints = widget.selectedPoints;

    selectedDateTime = widget.selectedDate;

    pointsSelectedIndex = widget.pointsSelectedIndex;

    DateTime start = DateTime(widget.minimumDate.year, widget.minimumDate.month,
        widget.minimumDate.day);

    while (start.isBefore(widget.maximumDate)) {
      dateItem.add(start);
      start = start.add(const Duration(days: 1));
    }

    for (var i = 0; i < 24; i++) {
      hours.add(i);
    }

    for (var i = 0; i < 60; i++) {
      minutes.add(i);
    }

    DateTime selDate = DateTime(
        selectedDateTime.year, selectedDateTime.month, selectedDateTime.day);
    dateSelectedIndex = dateItem.indexOf(selDate);
    if (dateSelectedIndex == -1) {
      dateSelectedIndex = dateItem.length - 1;
    }

    hourSelectedIndex = selectedDateTime.hour;
    minuteSelectedIndex = selectedDateTime.minute;

    _dateController =
        FixedExtentScrollController(initialItem: dateSelectedIndex);
    _hourController =
        FixedExtentScrollController(initialItem: hourSelectedIndex);
    _minuteController =
        FixedExtentScrollController(initialItem: minuteSelectedIndex);
    _pointsController =
        FixedExtentScrollController(initialItem: pointsSelectedIndex);

    debounce(timeChanged, (_) {
      _onTimeChanged();
    }, time: const Duration(milliseconds: 300));
  }

  void _onTimeChanged() {
    DateTime selectDate = dateItem[dateSelectedIndex];

    DateTime current = DateTime(selectDate.year, selectDate.month,
        selectDate.day, hourSelectedIndex, minuteSelectedIndex);
    if (current.isBefore(widget.minimumDate)) {
      if (hourSelectedIndex < widget.minimumDate.hour) {
        _hourController.animateToItem(
          widget.minimumDate.hour,
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );

        if (minuteSelectedIndex < widget.minimumDate.minute) {
          _minuteController.animateToItem(
            widget.minimumDate.minute,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease,
          );
        }
      } else {
        if (minuteSelectedIndex < widget.minimumDate.minute) {
          _minuteController.animateToItem(
            widget.minimumDate.minute,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease,
          );
        }
      }
    } else if (current.isAfter(widget.maximumDate)) {
      if (hourSelectedIndex > widget.maximumDate.hour) {
        _hourController.animateToItem(
          widget.maximumDate.hour,
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );

        if (minuteSelectedIndex > widget.maximumDate.minute) {
          _minuteController.animateToItem(
            widget.maximumDate.minute,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease,
          );
        }
      } else {
        if (minuteSelectedIndex > widget.maximumDate.minute) {
          _minuteController.animateToItem(
            widget.maximumDate.minute,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: _getEdit(context),
    );
  }

  @override
  void dispose() {
    _disposeController();
    super.dispose();
  }

  @override
  void didUpdateWidget(DataTimePointPickerView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  void _disposeController() {
    _dateController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    _pointsController.dispose();
  }

  Widget _getEdit(BuildContext context) {
    return Container(
      height: 300.sp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp), topRight: Radius.circular(20.sp))
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(
        children: [
          _getTitleRow(context, '选择时间'),
          _getDateRow(context),
        ],
      ),
    );
  }

  Widget _getTitleRow(BuildContext context, String title) {
    return Container(
      height: 44.sp,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColor.lineColor, // 边框颜色
            width: 0.5.sp, // 边框宽度
          ),
        ),
      ),
      child: Row(
        children: [
          DNUWhiteButton(
            width: 80.sp,
            title: AppContext.to.getLocale().cancel,
            titleColor: AppColor.textSecondary,
            backgroundBoxShadow: const [],
            onPressed: () {
              Get.back();
            },
          ),
          Expanded(child: Text(title, style: TextStyle(fontSize: 16.sp, color: AppColor.textPrimary, fontWeight: FontWeight.w600), textAlign: TextAlign.center,)),
          DNUWhiteButton(
            width: 80.sp,
            title: AppContext.to.getLocale().ok,
            titleColor: AppColor.primary,
            backgroundBoxShadow: const [],
            onPressed: () {
              DateTime now = DateTime.now();
              DateTime newDateTime = DateTime(
                  dateItem[dateSelectedIndex].year,
                  dateItem[dateSelectedIndex].month,
                  dateItem[dateSelectedIndex].day,
                  hourSelectedIndex,
                  minuteSelectedIndex,
                  now.second, now.millisecond, now.microsecond);
              // selectedDateTime = newDateTime;
              if (widget.onTimePointSelected != null) {
                widget.onTimePointSelected(newDateTime, widget.selectedPoints[pointsSelectedIndex]);
              }
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  Widget _getDateRow(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 256.sp,
      padding: EdgeInsets.all(15.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _getScrollView(context, 0, (int index) {
            setState(() {
              dateSelectedIndex = index;
              upDateTime();
              timeChanged.value = DateTime.now().millisecondsSinceEpoch;
            });
          }, _dateController),
          Row(
            children: [
              _getScrollView(context, 1, (int index) {
                setState(() {
                  hourSelectedIndex = index;
                  upDateTime();
                  timeChanged.value = DateTime.now().millisecondsSinceEpoch;
                });
              }, _hourController),
              Text(
                ":",
                style: TextStyle(
                  fontSize: 20.sp,
                  fontFamily: AppFont.ranyBold,
                  color: AppColor.textPrimary,
                ),
              ),
              _getScrollView(context, 2, (int index) {
                setState(() {
                  minuteSelectedIndex = index;
                  upDateTime();
                  timeChanged.value = DateTime.now().millisecondsSinceEpoch;
                });
              }, _minuteController),
            ],
          ),
          _getScrollView(context, 3, (int index) {
            setState(() {
              pointsSelectedIndex = index;
            });
          }, _pointsController),
        ],
      ),
    );
  }

  Widget _getScrollView(BuildContext context, int type,
      ValueChanged<int> onChanged, FixedExtentScrollController controller) {
    int length = dateItem.length;
    double itemWidth = 150.sp;
    if (type == 1) {
      length = hours.length;
      itemWidth = 54.sp;
    } else if (type == 2) {
      length = minutes.length;
      itemWidth = 54.sp;
    } else if (type == 3) {
      itemWidth = 80.sp;
      length = selectedPoints.length;
    }
    //_getScrollViewWidth(context, type);
    return SizedBox(
      width: itemWidth,
      child: ListWheelScrollView.useDelegate(
        itemExtent: 45.w,
        diameterRatio: 5.0,
        physics: const FixedExtentScrollPhysics(),
        perspective: 0.003,
        offAxisFraction: 0,
        squeeze: 1,
        onSelectedItemChanged: onChanged,
        controller: controller,
        childDelegate: ListWheelChildListDelegate(
          children: List<Widget>.generate(
            length,
            (index) => _buildDateView(index: index, type: type),
          ),
        ),
      ),
    );
  }

  Widget _buildDateView({required int index, int type = 0}) {
    return SizedBox(
      height: 45.w,
      child: Align(
        child: Text(
          _getShowValue(index, type),
          style: _getItemTextStyle(index, type),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void upDateTime() {
    DateTime newDateTime = DateTime(
        dateItem[dateSelectedIndex].year,
        dateItem[dateSelectedIndex].month,
        dateItem[dateSelectedIndex].day,
        hourSelectedIndex,
        minuteSelectedIndex);

    if (newDateTime != selectedDateTime) {
      selectedDateTime = newDateTime;
      // widget.onDateTimeChanged(newDateTime);
    }
  }

  // double _getScrollViewWidth(BuildContext context, int type) {
  //   String _longestText = '00';
  //   if (type == 0) {
  //     _longestText = '22${AppContext.to.getLocale().str_month}32${AppContext.to.getLocale().str_ri}';
  //   } else if (type == 3) {
  //     _longestText = AppContext.to.getLocale().str_zaocanhou;
  //   }
  //
  //   final TextPainter _painter = TextPainter(
  //     text: TextSpan(
  //       style: TextStyle(
  //         fontFamily: AppFont.ranyBold,
  //         fontSize: 20.sp,
  //         color: const Color(0xFFB9BDD8),
  //       ),
  //       text: _longestText,
  //     ),
  //     textDirection: Directionality.of(context),
  //   );
  //   _painter.layout();
  //
  //   final TextPainter _painter2 = TextPainter(
  //     text: TextSpan(
  //       style: TextStyle(
  //         fontSize: 20.sp,
  //         fontWeight: FontWeight.w600,
  //         color: AppColor.title,
  //       ),
  //       text: _longestText,
  //     ),
  //     textDirection: Directionality.of(context),
  //   );
  //   _painter2.layout();
  //
  //   return max(_painter.size.width, _painter2.size.width) + 2;
  // }

  TextStyle _getItemTextStyle(int index, int type) {
    bool isSelected = false;
    if (type == 0) {
      isSelected = (index == dateSelectedIndex);
    } else if (type == 1) {
      isSelected = (index == hourSelectedIndex);
    } else if (type == 2) {
      isSelected = (index == minuteSelectedIndex);
    } else if (type == 3) {
      isSelected = (index == pointsSelectedIndex);
    }

    return !isSelected
        ? TextStyle(
            // fontFamily: AppFont.ranyBold,
            fontSize: 16.sp,
            color: AppColor.textSecondary,
          )
        : TextStyle(
            fontSize: 18.sp,
            // fontFamily: AppFont.ranyBold,
            color: AppColor.primary,
          );
  }

  String _formatDate(DateTime destTime) {
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    DateTime destDate = DateTime(destTime.year, destTime.month, destTime.day);

    if (destDate == today) {
      return AppContext.to.getLocale().str_today;
    }
    return destTime.formatDate(DateFormat.YEAR_ABBR_MONTH_DAY);
  }

  String _formatTime(DateTime destTime) {
    return destTime.formatDate('HH:mm');
  }

  String _getShowValue(int index, int type) {
    if (type == 0) {
      return _formatDate(dateItem[index]);
    } else if (type == 3) {
      return selectedPoints[index].name;
    } else {
      int v = 0;
      if (type == 2) {
        v = minutes[index];
      } else if (type == 1) {
        v = hours[index];
      }

      return v < 10 ? "0$v" : "$v";
    }
  }
}
