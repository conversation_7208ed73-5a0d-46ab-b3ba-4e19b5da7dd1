import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../ui/style/color.dart';
import '../../../common/link/deep_link.dart';
import '../../../user/service/user_config_service.dart';
import 'logic.dart';

class UricAcidTargetPage extends StatelessWidget {
  const UricAcidTargetPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
        context: context,
        title: '尿酸目标',
        titleColor: Colors.white,
        darkMode: true,
        backButtonColor: Colors.white,
        // backgroundColor: Colors.white,
        backgroundColor: AppColor.primaryLight,
      ),
      body: SingleChildScrollView(
          child: Column(
        children: [
          Container(
            height: 35.w,
            color: Colors.white,
            child: Center(child: Obx(() {
              return Text(
                  '正常范围(${UserConfigService.to.uricAcidUnit.value.name})');
            })),
          ),
          _buildTable(context),
          _buildTip(context)
        ],
      )),
    );
  }

  Widget _buildTable(BuildContext context) {
    final logic = Get.put(UricAcidTargetLogic());
    return Obx(() {
      return Table(
          border: TableBorder.all(color: AppColor.textDisable),
          columnWidths: {
            0: FixedColumnWidth(60.w),
          },
          children: [
            TableRow(children: [
              _buildTableTextCell(context,
                  title: '男性',
                  height: 35.w,
                  bold: true,
                  backgroundColor: Colors.white),
              _buildTableTextCell(context,
                  title: logic.uricAcidTargetRange,
                  backgroundColor: AppColor.background2,
                  textColor: AppColor.primaryLight)
            ]),
            TableRow(children: [
              _buildTableTextCell(context,
                  title: '女性',
                  height: 35.w,
                  bold: true,
                  backgroundColor: Colors.white),
              _buildTableTextCell(context,
                  title: logic.uricAcidTargetRange,
                  backgroundColor: AppColor.background2,
                  textColor: AppColor.primaryLight)
            ])
          ]);
    });
  }

  Widget _buildTableCell(
    BuildContext context, {
    Widget? child,
    Color? backgroundColor,
    double? height,
  }) {
    return Container(
      height: height ?? 35.w,
      color: backgroundColor,
      child: Center(child: child ?? Container()),
    );
  }

  Widget _buildTableTextCell(
    BuildContext context, {
    String? title = '',
    Color? backgroundColor,
    double? height,
    bool bold = false,
    Color? textColor,
  }) {
    return _buildTableCell(
      context,
      backgroundColor: backgroundColor,
      height: height,
      child: Text(
        title ?? '',
        style: TextStyle(
          color: textColor ?? AppColor.textPrimary,
          fontSize: 16.sp,
          fontWeight: bold ? FontWeight.bold : FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildTip(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.w),
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10.w, left: 10.w),
            child: Row(
              children: [
                Container(
                  height: 13.w,
                  width: 6.w,
                  color: AppColor.primaryLight,
                ),
                SizedBox(
                  width: 4.w,
                ),
                Text('温馨提示', style: TextStyle(fontSize: 12.sp))
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.w,
              right: 10.w,
              top: 10.w,
              bottom: 20.w,
            ),
            child: Text.rich(
              TextSpan(children: [
                const TextSpan(
                    text: "1.尿酸是嘌呤代谢的产物，高尿酸血症(HUA)是嘌呤代谢紊乱引起的代谢异常综合征。\n"),
                const TextSpan(text: "2.近年来，HUA的患病率呈明显上升和年轻化趋势。\n"),
                const TextSpan(
                    text: "3.HUA与痛风、代谢综合征、2型糖尿病、高血压、心血管疾病、慢性肾病等关系密切。\n"),
                const TextSpan(text: "4.临床检测UA浓度主要用于痛风诊断，评判药物有效性。\n"),
                const TextSpan(
                    text:
                        "5.尿酸高要限制高嘌呤食物，少吃动物内脏、海鲜，少喝酒；多喝水，多吃蔬菜；适量运动，控制体重等，并定期检测尿酸。\n"),
                const TextSpan(
                    text: "6.将尿酸控制在合理范围内，有助于减少相关代谢疾病风险。不同情况下，控制标准不同：详情参照"),
                TextSpan(
                  text: "什么是痛风？得了痛风后，尿酸要控制在多少以内？",
                  style: const TextStyle(
                    color: AppColor.primaryLight,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      DeepLink.handle(
                          "dnurseapp://com.dnurse/openwith?act=KL:24852");
                    },
                ),
              ]),
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColor.textPrimary,
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
          )
        ],
      ),
    );
  }
}
