import 'package:get/get.dart';

import '../../../user/service/user_config_service.dart';
import '../../common/data_target.dart';
import '../../entity/data_uric_acid.dart';
import '../../service/uric_acid_service.dart';
import 'state.dart';

class UricAcidTargetLogic extends GetxController {
  final UricAcidTargetState state = UricAcidTargetState();

  PointTarget get pointTarget {
    PointTarget old =
        UricAcidService.to.currentUricAcidDataTarget.value.getPointTarget();

    return PointTarget(
      DataUricAcid.displayChartValue(
        old.mini,
        UserConfigService.to.uricAcidUnit.value,
      ),
      DataUricAcid.displayChartValue(
          old.max, UserConfigService.to.uricAcidUnit.value),
    );
  }

  String get uricAcidTargetRange {
    return "${DataUricAcid.formatMole(pointTarget.mini.toInt(), UserConfigService.to.uricAcidUnit.value)} ~ ${DataUricAcid.formatMole(pointTarget.max.toInt(), UserConfigService.to.uricAcidUnit.value)}";
  }
}
