import 'package:dnurse/app/data/db/model/storage/storage_model.dart';
import 'package:dnurse/app/data/pages/sport_record/add_sport_record/state.dart';
import 'package:dnurse/app/data/pages/sport_record/add_sport_record/widget/sport_select_widget.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../../resource/dnu_assets.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/icons/icons.dart';
import '../../../../../ui/widgets/button/dnu_primary_button.dart';
import 'logic.dart';

class AddSportRecordPage extends StatelessWidget {
  AddSportRecordPage({super.key});

  final logic = Get.find<AddSportRecordLogic>();
  final state = Get.find<AddSportRecordLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
        context: context,
        title: '添加运动',
        backgroundColor: AppColor.backgroundWhite,
        elevation: 0,
      ),
      body: Obx(
        () => Container(
          color: AppColor.background3,
          child: Stack(
            children: [
              CustomScrollView(
                slivers: [
                  // SliverAppBar(
                  //   pinned: true,
                  //   automaticallyImplyLeading: false,
                  //   title: _buildSearch(context),
                  // ),
                  // SliverPadding(padding: EdgeInsets.symmetric(vertical: 5.w)),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      _buildSportItem,
                      childCount: state.localSportList.length + 1,
                    ),
                  ),
                  SliverPadding(padding: EdgeInsets.symmetric(vertical: 30.w))
                ],
              ),
              Positioned(
                bottom: 0.w,
                right: 0.w,
                left: 0.w,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(
                    vertical: 16.w,
                    horizontal: 16.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white, // 容器的背景颜色
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.w), // 左上角的圆角半径
                      topRight: Radius.circular(20.w), // 右上角的圆角半径
                    ),
                  ),
                  child: state.sportCount == 0
                      ? _buildEmptyStyle(context)
                      : _buildSelectStyle(context),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void _buildSelectDialog(
    BuildContext context,
    SportSelectionItem data,
    int index,
  ) {
    state.currentValue.value = 30;
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return SportSelectWidget(
          storageModel: data.data,
          duration: data.duration,
          onSelect: (int value) {
            logic.selectSport(index, data, value);
          },
        );
      },
    );
  }

  Widget _buildSelectStyle(BuildContext context) {
    return Row(
      children: [
        Text(
          '已选',
          style: TextStyle(
            fontSize: 16.w,
          ),
        ),
        Text(
          ' ${state.sportCount} ',
          style: TextStyle(
            fontSize: 16.w,
            color: AppColor.primaryLight,
          ),
        ),
        Text(
          '个运动共',
          style: TextStyle(
            fontSize: 16.w,
          ),
        ),
        Text(
          ' ${logic.state.totalCalorie} ',
          style: TextStyle(
            fontSize: 16.w,
            color: AppColor.primaryLight,
          ),
        ),
        Text(
          '千卡',
          style: TextStyle(
            fontSize: 16.w,
          ),
        ),
        const Spacer(),
        DNUPrimaryButton(
          width: 130.w,
          onPressed: () {
            logic.save();
          },
          child: Center(
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildEmptyStyle(BuildContext context) {
    return Row(
      children: [
        Text(
          '请点击运动来记录',
          style: TextStyle(
            fontSize: 16.w,
            color: AppColor.textDesc,
          ),
        ),
        const Spacer(),
        DNUPrimaryButton(
          width: 130.w,
          backgroundColor: AppColor.background2,
          child: Center(
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColor.textDesc,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildAddSportItem(BuildContext context) {
    return InkWell(
      onTap: () {
        AppRouter.push(Routes.sportCustomAdd);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 20.w,
        ),
        decoration: BoxDecoration(
          color: AppColor.backgroundWhite,
          border: Border(
            bottom: BorderSide(
              color: AppColor.background3, // 底部边框的颜色
              width: 1.w, // 底部边框的宽度
            ),
          ),
        ),
        child: Text(
          "添加自定义运动",
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColor.primaryLight,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildSportItem(BuildContext context, int index) {
    return Obx(() {
      if (index == state.localSportList.length) {
        return _buildAddSportItem(context);
      }

      SportSelectionItem item = state.localSportList[index];
      StorageModel dataItem = item.data;

      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 20.w,
        ),
        decoration: BoxDecoration(
          color: AppColor.backgroundWhite,
          border: Border(
            bottom: BorderSide(
              color: AppColor.background3, // 底部边框的颜色
              width: 1.w, // 底部边框的宽度
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 44.w,
              height: 44.w,
              decoration: BoxDecoration(
                // gradient: AppColor.gradientPurplePink,
                borderRadius: BorderRadius.all(
                  Radius.circular(10.w),
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: dataItem.imageUrl.isNotEmpty
                  ? DNUImage.network(
                      dataItem.imageUrl,
                      fit: BoxFit.cover,
                      useCache: true,
                    )
                  : Image.asset(
                      DNUAssets.to.images.data.defaultSport,
                      fit: BoxFit.cover,
                    ),
            ),
            SizedBox(width: 10.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  dataItem.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            const Spacer(),
            if (item.duration > 0)
              InkWell(
                onTap: () {
                  // state.currentValue.value = 0;
                  _buildSelectDialog(context, item, index);
                },
                child: Text(
                  '${item.duration}分钟',
                  style: TextStyle(
                    color: AppColor.primary,
                    fontSize: 16.sp,
                  ),
                ),
              ),
            SizedBox(width: 5.w),
            if (item.duration == 0)
              InkWell(
                onTap: () {
                  // state.currentValue.value = 0;
                  _buildSelectDialog(context, item, index);
                },
                child: Icon(
                  DNUIconFont.tianjiatupian,
                  color: const Color(0xFF333333),
                  size: 20.sp,
                ),
              )
            else
              InkWell(
                onTap: () {
                  logic.deleteSport(index, item);
                },
                child: Icon(
                  DNUIconFont.fenxiangshanchu,
                  color: AppColor.primaryLight,
                  size: 20.sp,
                ),
              ),
          ],
        ),
      );
    });
  }
}
