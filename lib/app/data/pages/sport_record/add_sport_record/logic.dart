import 'package:dnurse/app/data/db/model/sport/sport_data_model.dart';
import 'package:dnurse/app/data/db/model/storage/storage_model.dart';
import 'package:dnurse/app/data/entity/data_sport.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/router/router.dart';
import 'package:get/get.dart';

import '../../../../user/entity/from_type.dart';
import '../../../service/extra_data_service.dart';
import 'state.dart';

class AddSportRecordLogic extends DNUBaseController {
  final AddSportRecordState state = AddSportRecordState();

  Future<void> _loadLocalSportList() async {
    List<StorageModel> list = await ExtraDataService.to.getSportList();
    final selectedItem =
        state.localSportList.where((item) => item.duration > 0).toList();
    state.localSportList.clear();

    state.localSportList.addAll(list.map((e) {
      int duration = selectedItem
              .firstWhereOrNull((item) => item.data.name == e.name)
              ?.duration ??
          0;
      return SportSelectionItem(e, duration);
    }));
  }

  @override
  void onResume() {
    super.onResume();
    _loadLocalSportList();
  }

  void selectSport(int index, SportSelectionItem item, int time) {
    state.localSportList[index] = SportSelectionItem(item.data, time);
    state.localSportList.refresh();

    // SportDataModel dataModel = SportDataModel(
    //   id: 0,
    //   uid: AppContext.to.userSn,
    //   did: '',
    //   upid: 0,
    //   imgUrl: data.imageUrl,
    //   calorie: (double.parse(data.calories) * time).round(),
    //   name: data.name,
    //   unit: '分钟',
    //   num: time,
    //   time: 0,
    //   timePoint: 0,
    // );
    //
    // state.selectedSportList.add(dataModel);
  }

  void deleteSport(int index, SportSelectionItem item) {
    state.localSportList[index] = SportSelectionItem(item.data, 0);
    state.localSportList.refresh();

    // state.selectedSportList.removeWhere((element) => element.name == item.name);
    // print(state.selectedSportList);
  }

  int getTotalCalorie() {
    int total = 0;

    for (SportDataModel item in state.selectedSportList) {
      total += item.calorie;
    }

    return total;
  }

  void save() {
    List<DataSport> result =
        state.localSportList.where((item) => item.duration > 0).map((item) {
      int calorie =
          ((double.tryParse(item.data.calories) ?? 0) * item.duration).round();
      return DataSport(
        did: DataSport.generateDid(),
        time: DateTime.now(),
        sn: AppContext.to.userSn,
        name: item.data.name,
        calorie: calorie,
        duration: item.duration,
        fromType: FromType.defaultValue,
      );
    }).toList();

    AppRouter.back(result: result.isEmpty ? null : result);
  }
}
