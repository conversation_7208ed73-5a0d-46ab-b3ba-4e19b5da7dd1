import 'package:get/get.dart';

import '../../../db/model/sport/sport_data_model.dart';
import '../../../db/model/storage/storage_model.dart';

class SportSelectionItem {
  final StorageModel data;
  final int duration;

  SportSelectionItem(this.data, this.duration);
}

class AddSportRecordState {
  final currentValue = 0.obs;

  final localSportList = <SportSelectionItem>[].obs;
  final selectedSportList = <SportDataModel>[].obs;

  int get sportCount {
    return localSportList.where((item) => item.duration > 0).length;
  }

  int get totalCalorie {
    return localSportList.where((item) => item.duration > 0).fold(
        0,
        (sum, item) =>
            sum +
            ((double.tryParse(item.data.calories) ?? 0) * item.duration).round());
  }
}
