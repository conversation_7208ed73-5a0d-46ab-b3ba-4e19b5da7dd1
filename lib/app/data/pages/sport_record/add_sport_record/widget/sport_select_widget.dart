/// FileName: sport_select_widget
///
/// @Author: ygc
/// @Date: 2025/7/16 14:20
/// @Description:
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:wheel_slider/wheel_slider.dart';

import '../../../../../../router/router.dart';
import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../db/model/storage/storage_model.dart';

class SportSelectWidget extends StatefulWidget {
  const SportSelectWidget({
    super.key,
    required this.storageModel,
    required this.duration,
    this.onSelect,
  });

  final StorageModel storageModel;
  final int duration;
  final ValueChanged<int>? onSelect;

  @override
  State<SportSelectWidget> createState() => _SportSelectWidgetState();
}

class _SportSelectWidgetState extends State<SportSelectWidget> {
  int _value = 0;
  int _optionIndex = 0;
  late FixedExtentScrollController _valueController;
  late FixedExtentScrollController _unitController;
  final List<int> _options = [];

  @override
  void initState() {
    super.initState();

    _valueController = FixedExtentScrollController();
    _unitController = FixedExtentScrollController();

    _genOptions();
    int duration = widget.duration;
    if(duration == 0) {
      duration = 30;
    }

    for (int i = 0; i < _options.length; i++) {
      if (duration == _options[i]) {
        _optionIndex = i;
        break;
      }
    }

    _calculateValue();
  }

  void _calculateValue() {
    int selectValue = _options[_optionIndex];
    double calories = double.tryParse(widget.storageModel.calories) ?? 0;
    if(widget.storageModel.fromUser == "1" ) {
      double time = double.tryParse(widget.storageModel.sportTime) ?? 0;
      if(time > 0) {
        calories = calories / time ;
      }
    }

    _value =
        (selectValue *calories)
            .round();
  }

  void _genOptions() {
    _options.clear();

    int mini = 5;
    int max = 180;
    int interval = 5;

    for (int i = mini; i <= max; i += interval) {
      _options.add(i);
    }
  }

  @override
  void dispose() {
    _valueController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        bottom: 12.w + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.w),
          topRight: Radius.circular(20.w),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitle(context),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          SizedBox(height: 4.w),
          SizedBox(height: 12.w),
          Text(
            '$_value千卡/${_options[_optionIndex]}分钟',
            style: TextStyle(
              fontSize: 16.w,
              color: AppColor.textSecondary,
            ),
          ),
          SizedBox(height: 12.w),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          _buildUnitOptions(context),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          SizedBox(height: 10.w),
          Text(
            '分钟',
            style: TextStyle(
              fontSize: 16.w,
              color: AppColor.textSecondary,
            ),
          ),
          SizedBox(height: 10.w),
          DNUPrimaryButton(
            width: 335.w,
            height: 48.w,
            gradient: AppColor.primaryGradientHorizontal1,
            child: Center(
              child: Text(
                '选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
            onPressed: () {
              int amount = _options[_optionIndex];

              widget.onSelect?.call(amount);
              AppRouter.back();
            },
          )
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 48.w),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
      ),
      child: Center(
        child: Text(
          widget.storageModel.name,
          style: TextStyle(
            fontSize: 16.w,
            color: AppColor.textPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildUnitOptions(BuildContext context) {
    TextStyle itemTextStyle = TextStyle(
      fontSize: 16.sp,
      color: AppColor.textSecondary,
    );
    TextStyle selectedItemTextStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColor.primary,
      fontWeight: FontWeight.w500,
    );

    return Container(
      constraints: BoxConstraints(minHeight: 48.w),
      child: WheelSlider.customWidget(
        totalCount: _options.length,
        initValue: _optionIndex,
        isInfinite: false,
        scrollPhysics: const BouncingScrollPhysics(),
        itemSize: 80,
        enableAnimation: false,
        pointerColor: Colors.transparent,
        onValueChanged: (value) {
          _optionIndex = value;
          _calculateValue();
          setState(() {});
        },
        children: List.generate(
          _options.length,
          (index) => Center(
            child: Center(
              child: Text(
                _options[index].toInt().toString(),
                style: _optionIndex == index
                    ? selectedItemTextStyle
                    : itemTextStyle,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
