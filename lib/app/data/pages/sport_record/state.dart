import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../user/service/user_info_service.dart';
import '../../../user/utils/health_utils.dart';
import '../../entity/data_sport.dart';
import '../../entity/day_calories_info.dart';

class SportRecordState {
  final sportList = <DataSport>[].obs;

  final selectedDay = DateTime.now().startOfDay().obs;
  final focusDay = DateTime.now().obs;

  final dayConsumeCalories = RxMap<DateTime, DayCaloriesInfo?>({});

  int get recommendConsumeCalorie {
    return HealthUtils.getCalorieConsume(
            UserInfoService.to.currentUserInfo.value)
        .toInt();
  }
}

class SportItem {
  final String name;
  final int time;
  final int calorie;

  SportItem({required this.name, required this.time, required this.calorie});
}
