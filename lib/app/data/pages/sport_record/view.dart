import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/entity/data_sport.dart';
import 'package:dnurse/app/data/pages/sport_record/widget/sport_calendar_cell_widget.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:dnurse/ui/widgets/dnu_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../framework/utils/log.dart';
import '../../../../resource/dnu_assets.dart';
import '../../../../router/routes.dart';
import '../../../../ui/icons/icons.dart';
import '../../../../ui/widgets/dnu_toast.dart';
import '../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../entity/day_calories_info.dart';
import 'logic.dart';

class SportRecordPage extends StatelessWidget {
  SportRecordPage({super.key});

  final logic = Get.find<SportRecordLogic>();
  final state = Get.find<SportRecordLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: getAppBar(
        context: context,
        title: '每日运动',
        backgroundColor: AppColor.backgroundWhite,
        bottom: PreferredSize(
          preferredSize: Size(Get.width, 50.w),
          child: _getCalendar(),
        ),
        elevation: 0,
      ),
      body: Container(
        color: AppColor.background3,
        child: Stack(
          children: [
            CustomScrollView(
              slivers: [
                // Obx(
                //   () {
                //     return SliverToBoxAdapter(
                //       child: Container(
                //         padding: EdgeInsets.symmetric(vertical: 10.w),
                //         width: double.infinity,
                //         color: AppColor.backgroundWhite,
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //           children: logic.state.weekData
                //               .map((e) => _buildStepNumberArcProgress(e))
                //               .toList(),
                //         ),
                //       ),
                //     );
                //   },
                // ),
                SliverToBoxAdapter(
                  child: _buildStepNumberCard(context),
                ),
                Obx(
                  () => SliverList(
                    delegate: SliverChildBuilderDelegate(
                      _buildSportDataListItem,
                      childCount: state.sportList.length,
                    ),
                  ),
                ),
                SliverPadding(padding: EdgeInsets.symmetric(vertical: 30.w))
              ],
            ),
            Positioned(
              bottom: 10.w,
              right: 15.w,
              left: 15.w,
              child: _buildAddSportButton(context),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildStepNumberCard(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 15.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 20.w,
      ),
      decoration: BoxDecoration(
        color: AppColor.backgroundWhite,
        borderRadius: BorderRadius.all(Radius.circular(20.w)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 55.w,
                height: 55.w,
                decoration: BoxDecoration(
                  gradient: AppColor.gradientBluePurple,
                  borderRadius: BorderRadius.all(
                    Radius.circular(30.w),
                  ),
                ),
                child: Image.asset(
                  DNUAssets.to.images.data.stepNumber,
                ),
              ),
              SizedBox(width: 10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '运动步数',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 5.w),
                  RichText(
                      text: TextSpan(
                    children: [
                      TextSpan(
                        text: '0',
                        style: TextStyle(
                          color: AppColor.primary,
                          fontSize: 18.sp,
                        ),
                      ),
                      TextSpan(
                        text: ' 步',
                        style: TextStyle(
                          color: AppColor.textDesc,
                          fontSize: 18.sp,
                        ),
                      ),
                    ],
                  ))
                ],
              ),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '0',
                    style: TextStyle(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 5.w),
                  Text(
                    '千卡',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColor.textDesc,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 10.w),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.w),
            decoration: BoxDecoration(
              color: AppColor.background3,
              borderRadius: BorderRadius.all(Radius.circular(10.w)),
            ),
            child: Center(
              child: Text(
                '自动同步“运动”步数,累计消耗60千卡可自动完成任务',
                style:
                    TextStyle(fontSize: 13.sp, color: AppColor.textSecondary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSportDataListItem(BuildContext context, int index) {
    DataSport dataItem = state.sportList[index];

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 5.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 20.w,
      ),
      decoration: BoxDecoration(
        color: AppColor.backgroundWhite,
        borderRadius: BorderRadius.all(Radius.circular(20.w)),
      ),
      child: Row(
        children: [
          Container(
            width: 44.w,
            height: 44.w,
            decoration: BoxDecoration(
              // gradient: AppColor.gradientPurplePink,
              borderRadius: BorderRadius.all(
                Radius.circular(10.w),
              ),
            ),
            clipBehavior: Clip.antiAlias,
            child: dataItem.imageUrl.isNotEmpty
                ? DNUImage.network(
                    dataItem.imageUrl,
                    fit: BoxFit.cover,
                    useCache: true,
                  )
                : Image.asset(
                    DNUAssets.to.images.data.defaultSport,
                    fit: BoxFit.cover,
                  ),
          ),
          SizedBox(width: 10.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${dataItem.name} ',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.black,
                      ),
                    ),
                    TextSpan(
                      text: dataItem.sportTime(),
                      style: TextStyle(
                        color: AppColor.primary,
                        fontSize: 16.sp,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _gotoEditTime(index, context);
                        }
                    ),
                  ],
                ),
              ),
              Text(
                '${dataItem.duration} 分钟',
                style: TextStyle(
                  color: AppColor.textDesc,
                  fontSize: 14.sp,
                ),
              )
            ],
          ),
          const Spacer(),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${dataItem.calorie}',
                  style: TextStyle(
                    color: AppColor.primary,
                    fontSize: 16.sp,
                  ),
                ),
                TextSpan(
                  text: ' 千卡',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textDesc,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 5.w),
          InkWell(
            onTap: () {
              logic.onDeleteRecord(context, index);
            },
            child: Icon(
              DNUIconFont.shanchu,
              color: AppColor.primary,
              size: 20.sp,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildAddSportButton(BuildContext context) {
    return DNUPrimaryButton(
      onPressed: () async {
        _gotoAddSport(context);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Spacer(),
          Icon(
            DNUIconFont.tianjiatupian,
            size: 22.sp,
            color: Colors.white,
          ),
          SizedBox(width: 5.w),
          Text(
            '添加运动记录',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  void _gotoAddSport(BuildContext context) {
    DateSheetView.show(
      context: context,
      title: '请选择运动时间',
      initialDateTime: DateTime.now(),
      pickerMode: BrnDateTimePickerMode.time,
      dateFormat: "HH:mm",
      // 设置时间格式
      onConfirm: (dateTime, selectedIndex) {
        Log.i("运动时间: $dateTime");
        DateTime now = logic.state.selectedDay.value.startOfDay();
        DateTime newDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          dateTime.hour,
          dateTime.minute,
        );

        if (newDateTime.isAfter(DateTime.now())) {
          DNUToast.show(
            '不能选择未来的时间',
            context,
          );
          return;
        }

        Future.delayed(const Duration(milliseconds: 1), () {
          AppRouter.push(Routes.addSportRecord)?.then((result) {
            if (result != null && result.isNotEmpty) {
              try {
                List<DataSport> drugList = result as List<DataSport>;
                logic.addSport(drugList, newDateTime);
              } catch (e) {
                print(e);
                Log.i('add drug error', e);
              }
            }
          });
        });
      },
    );
  }

  Widget _getCalendar() {
    return Obx(() {
      return Container(
        // color: Colors.white,
        padding: EdgeInsets.symmetric(
          vertical: 10.w,
        ),
        child: TableCalendar(
          focusedDay: logic.state.focusDay.value,
          firstDay: DateTime(2012, 1, 1),
          lastDay: DateTime.now(),
          startingDayOfWeek: StartingDayOfWeek.monday,
          availableCalendarFormats: const {CalendarFormat.week: 'Week'},
          headerVisible: false,
          daysOfWeekVisible: false,
          calendarFormat: CalendarFormat.week,
          rowHeight: 50.w,
          selectedDayPredicate: (day) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            day = day.subtract(timeZoneOffset).toLocal();
            return isSameDay(logic.state.selectedDay.value, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            selectedDay = selectedDay.subtract(timeZoneOffset).toLocal();
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();
            // controller.loadSolution(selectedDay.startOfDay());
            logic.loadData(selectedDay.startOfDay());
            if (!isSameDay(logic.state.selectedDay.value, selectedDay)) {
              logic.state.selectedDay.value = selectedDay.startOfDay();
              logic.state.focusDay.value = focusedDay.startOfDay();
            }
          },
          onPageChanged: (focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();
            logic.state.focusDay.value = focusedDay;
            // DnuDateTime.fromDateTime(focusedDay);
            // controller.state.focusDay.value = focusedDay;
            logic.setEvent();
          },
          calendarBuilders:
              CalendarBuilders(selectedBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: true,
            );
          }, todayBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: false,
            );
          }, disabledBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
              disabled: true,
            );
          }, outsideBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }, defaultBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }),
        ),
      );
    });
  }

  Widget _buildCalendarCellWidget({
    required DateTime day,
    bool focused = false,
    bool selected = false,
    bool disabled = false,
  }) {
    return Obx(() {
      Color textColor = AppColor.textPrimary;
      if (disabled) {
        textColor = AppColor.textDisable;
      } else {
        if (selected) {
          textColor = AppColor.primary;
        } else {
          textColor = AppColor.textSecondary;
        }
      }

      var timeZoneOffset = DateTime.now().timeZoneOffset;
      DateTime dayLocal = day.subtract(timeZoneOffset).toLocal();

      DayCaloriesInfo? dayCaloriesInfo =
          logic.state.dayConsumeCalories[dayLocal];

      return SportCalendarCellWidget(
        day: dayLocal,
        focused: focused,
        textColor: textColor,
        value: dayCaloriesInfo?.value ?? 0,
      );
    });
  }

  void _gotoEditTime(int index, BuildContext context) {
    DataSport dataItem = state.sportList[index];

    DateSheetView.show(
      context: context,
      title: '修改运动时间',
      initialDateTime: dataItem.time,
      pickerMode: BrnDateTimePickerMode.time,
      dateFormat: "HH:mm",
      // 设置时间格式
      onConfirm: (dateTime, selectedIndex) {
        Log.i("运动时间: $dateTime");
        DateTime now = dataItem.time.startOfDay();
        DateTime newDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          dateTime.hour,
          dateTime.minute,
        );

        if (newDateTime.isAfter(DateTime.now())) {
          DNUToast.show(
            '不能选择未来的时间',
            context,
          );
          return;
        }

        logic.onEditRecord(context, index, newDateTime);
      },
    );
  }
}
