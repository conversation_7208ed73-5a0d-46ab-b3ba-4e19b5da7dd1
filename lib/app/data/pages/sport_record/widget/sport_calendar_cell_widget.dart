/// FileName: sport_calendar_cell_widget
///
/// @Author: ygc
/// @Date: 2025/7/16 15:40
/// @Description:
import 'dart:math';

import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/style/font.dart';
import '../../../../../ui/widgets/progress/arc_progress_widget.dart';

class SportCalendarCellWidget extends StatelessWidget {
  const SportCalendarCellWidget({
    super.key,
    required this.day,
    required this.focused,
    this.textColor,
    this.value = 0.0,
  });

  final DateTime day;
  final bool focused;
  final Color? textColor;
  final double value;

  @override
  Widget build(BuildContext context) {
    DateTime today = DateTime.now().startOfDay();
    String dayText = day.day.toString();
    if (day == today) {
      dayText = '今';
    }

    return SizedBox(
      width: 44.w,
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(
          children: [
            Transform(
              transform: Matrix4.rotationZ(pi),
              alignment: Alignment.center,
              child: ArcProgressWidget(
                value: value,
                backgroundStrokeWidth: 5.w,
                progressbarStrokeWidth: 6.w,
                gradient: const LinearGradient(
                  colors: [AppColor.primaryLight, AppColor.primaryLight],
                ),
                openAngle: 0,
              ),
            ),
            Center(
              child: Text(
                dayText,
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w400,
                  color: textColor ?? AppColor.textSecondary,
                  fontFamily: AppFont.ranyBold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
