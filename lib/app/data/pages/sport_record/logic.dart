import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../ui/widgets/dialog/dnu_dialog.dart';
import '../../entity/data_sport.dart';
import '../../entity/day_calories_info.dart';
import '../../service/extra_data_service.dart';
import '../../service/sport_service.dart';
import 'state.dart';

class SportRecordLogic extends GetxController {
  final SportRecordState state = SportRecordState();

  @override
  void onInit() {
    super.onInit();

    _init();
  }

  void _init() async {
    await loadData(state.selectedDay.value);
    await _loadCalories(state.selectedDay.value);
  }

  Future<void> loadData(DateTime date) async {
    List<DataSport> sports = await SportService.to.getSportList(
      AppContext.to.userSn,
      start: date,
      end: date.add(const Duration(days: 1)),
    );

    List<DataSport> sportsImage = [];
    for (var element in sports) {
      final imgUrl =
          await ExtraDataService.to.getSportImageByName(element.name);
      sportsImage.add(element.copyWith(imageUrl: imgUrl));
    }

    state.sportList.clear();
    state.sportList.addAll(sportsImage);
  }

  List<DateTime> getCurrentWeekDates() {
    final now = DateTime.now();
    final int currentWeekday = now.weekday;
    final monday = DateTime(now.year, now.month, now.day)
        .subtract(Duration(days: currentWeekday - 1));
    return List.generate(7, (index) => monday.add(Duration(days: index)));
  }

  void onDeleteRecord(BuildContext context, int index) {
    DNUDialog.showConfirm(
      title: "提示",
      content: "你确定要删除这条记录吗？",
      onConfirm: () async {
        await SportService.to.deleteSport(state.sportList[index]);
        SportService.to.syncDataUpload(AppContext.to.userSn);
        _init();
      },
    );
  }

  void onEditRecord(BuildContext context, int index, DateTime newDateTime) async {
    DataSport dataItem = state.sportList[index];

    DataSport dataItemNew =dataItem.copyWith(
      time: newDateTime,
    );

    await SportService.to.saveSport(dataItemNew);

    state.sportList[index] = dataItemNew;

    await loadData(state.selectedDay.value);

    SportService.to.syncDataUpload(AppContext.to.userSn);
  }

  void addSport(List<DataSport> drugList, DateTime newDateTime) async {
    for (DataSport sport in drugList) {
      sport.time = newDateTime;
      DataSport sportItem = sport.copyWith(
        sn: AppContext.to.userSn,
        time: newDateTime,
      );
      await SportService.to.saveSport(sportItem);
    }

    _init();

    SportService.to.syncDataUpload(AppContext.to.userSn);
  }

  Future setEvent() async {
    _loadCalories(state.focusDay.value);
  }

  Future _loadCalories(DateTime date) async {
    int weekDay = date.weekday;

    DateTime firstDayOfWeek =
        state.focusDay.value.subtract(Duration(days: weekDay - 1));
    firstDayOfWeek = firstDayOfWeek.startOfDay();
    DateTime lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 7));

    for (var i = 0; i < 7; i++) {
      DateTime day = firstDayOfWeek.add(Duration(days: i));
      state.dayConsumeCalories[day] = null;
    }

    int recommendCal = state.recommendConsumeCalorie;

    List<DayCaloriesInfo> dayCaloriesInfoList = await SportService.to
        .queryConsumeCaloriesByDay(
            AppContext.to.userSn, firstDayOfWeek, lastDayOfWeek);
    for (var dayCaloriesInfo in dayCaloriesInfoList) {
      double percent = 0;
      if (recommendCal > 0) {
        percent = dayCaloriesInfo.calories / recommendCal;
        percent = percent > 1 ? 1 : percent;
      }

      dayCaloriesInfo.value = percent;

      state.dayConsumeCalories[dayCaloriesInfo.date] = dayCaloriesInfo;
    }
  }
}
