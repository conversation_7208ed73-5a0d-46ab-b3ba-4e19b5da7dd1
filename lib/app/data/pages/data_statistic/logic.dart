import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/data_statistic_base_page.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/glucose_table/glucose_table_logic.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/glucose_table/glucose_table_page.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/log/data_log_logic.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/log/data_log_page.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/uric_acid_table/uric_acid_table_logic.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/uric_acid_table/uric_acid_table_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../resource/generated/l10n.dart';
import '../../../../ui/widgets/keep_alive_wrapper.dart';
import 'pages/statistic/glucose_statistic_logic.dart';
import 'pages/statistic/glucose_statistic_page.dart';
import 'state.dart';

class DataStatisticLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final DataStatisticState state = DataStatisticState();

  late List<DataStatisticPageItem> pages;
  late TabController tabController;
  late final PageController pageController;
  final tabs = <BadgeTab>[].obs;
  final curPage = 0.obs;
  final backgroundColor = Colors.white.obs;
  final showBackgroundGradient = true.obs;

  @override
  void onInit() {
    super.onInit();

    _initPages();

    for (var element in pages) {
      tabs.add(BadgeTab(text: element.getTitle(Get.context!)));
    }

    tabController = TabController(length: tabs.length, vsync: this);

    pageController = PageController(initialPage: 0);

    pages[0].logic().onShow();
    backgroundColor.value = pages[0].logic().getBackgroundColor();
    showBackgroundGradient.value = pages[0].logic().getShowBackgroundGradient();
  }

  void _initPages() {
    pages = [
      DataStatisticPageItem(
        name: dataStatisticGlucose,
        logic: () => Get.find<GlucoseStatisticLogic>(),
        page: () => const KeepAliveWrapper(
          child: GlucoseStatisticPage(),
        ),
        init: () => Get.lazyPut(() => GlucoseStatisticLogic()),
        getTitle: (BuildContext context) {
          return S.of(context).dataStatisticGlucose;
        },
      ),
      DataStatisticPageItem(
        name: dataStatisticLog,
        logic: () => Get.find<DataLogLogic>(tag: "statistic"),
        page: () => const KeepAliveWrapper(
          child: DataLogPage(tag: "statistic"),
        ),
        init: () => Get.lazyPut(() => DataLogLogic(), tag: "statistic"),
        getTitle: (BuildContext context) {
          return S.of(context).dataStatisticDataLog;
        },
      ),
      DataStatisticPageItem(
        name: dataStatisticGlucoseTable,
        logic: () => Get.find<GlucoseTableLogic>(),
        page: () => const KeepAliveWrapper(
          child: GlucoseTablePage(),
        ),
        init: () => Get.lazyPut(() => GlucoseTableLogic()),
        getTitle: (BuildContext context) {
          return S.of(context).dataStatisticGlucoseTable;
        },
      ),
      DataStatisticPageItem(
        name: dataStatisticUricAcidTable,
        logic: () => Get.find<UricAcidTableLogic>(),
        page: () => const KeepAliveWrapper(
          child: UricAcidTablePage(),
        ),
        init: () => Get.lazyPut(() => UricAcidTableLogic()),
        getTitle: (BuildContext context) {
          return S.of(context).dataStatisticUATable;
        },
      ),
    ];

    for (var element in pages) {
      element.init();
    }
  }

  void onTabChanged(BuildContext context, int index) {
    if (index != curPage.value) {
      int oldIndex = curPage.value;

      // pageController.animateToPage(
      //   index,
      //   duration: const Duration(milliseconds: 300),
      //   curve: Curves.easeInOutQuart,
      // );
      pageController.jumpToPage(index);
      curPage.value = index;

      pages[oldIndex].logic().onHide();
      pages[index].logic().onShow();
      backgroundColor.value = pages[index].logic().getBackgroundColor();
      showBackgroundGradient.value =
          pages[index].logic().getShowBackgroundGradient();
    }
  }
}
