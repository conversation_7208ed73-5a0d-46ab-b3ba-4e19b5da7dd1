/// FileName: uric_acid_table_page
///
/// @Author: ygc
/// @Date: 2024/8/13 17:42
/// @Description:
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../framework/utils/log.dart';
import '../../../../common/time_point.dart';
import '../../../../entity/data_uric_acid.dart';
import '../../../../widget/table/data_table_head_widget.dart';
import '../../../../widget/table/data_table_row_widget.dart';
import 'uric_acid_table_logic.dart';

class UricAcidTablePage extends StatelessWidget {
  const UricAcidTablePage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<UricAcidTableLogic>();
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          DataTableHeadWidget(topPadding: 20.w,),
          Expanded(
            child: Obx(() {
              return SmartRefresher(
                enablePullDown: true,
                enablePullUp: true,
                controller: logic.refreshController,
                onRefresh: logic.onRefresh,
                onLoading: logic.onLoading,
                child: CustomScrollView(
                  slivers: [
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                            (c, i) {
                          return _buildItem(c, i);
                        },
                        childCount: logic.items.length,
                      ),
                    )
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(BuildContext context, int index) {
    final logic = Get.find<UricAcidTableLogic>();

    return Obx(() {
      DataTableRowData glucoseDataItem = logic.items[index];

      DataTableItem item = DataTableItem.fromRowData(glucoseDataItem);
      DateTime nextDay = item.dateTime.addDay(-1);
      return DataTableRowWidget(
        separate: nextDay.month != item.dateTime.month,
        value: item,
        highlight: index %2 == 1,
        onTap: (DateTime dateTime, TimePoint timePoint, dynamic tag) {
          Log.i("DataTableRowWidget: $dateTime, $timePoint, $tag");
          if (tag != null && tag.runtimeType == DataUricAcid) {
            (tag as DataUricAcid).gotoEdit();
          }
        },
      );
    });
  }
}
