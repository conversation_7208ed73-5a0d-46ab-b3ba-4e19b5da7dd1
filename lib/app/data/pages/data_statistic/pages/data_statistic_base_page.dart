/// FileName: data_statistic_base_page
///
/// @Author: ygc
/// @Date: 2024/8/13 17:03
/// @Description:
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DataStatisticPageItem {
  DataStatisticPageItem({
    required this.logic,
    required this.page,
    required this.init,
    required this.getTitle,
    required this.name,
  });

  final DataStatisticBasePageLogic Function() logic;
  final Widget Function() page;
  final String Function(BuildContext context) getTitle;
  final VoidCallback init;
  final String name;
}

class DataStatisticBasePageLogic extends GetxController {
  void onShow() {}

  void onHide() {}

  Color getBackgroundColor() {
    return Colors.white;
  }

  bool getShowBackgroundGradient() {
    return true;
  }
}
