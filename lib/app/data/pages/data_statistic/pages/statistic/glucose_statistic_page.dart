/// FileName: glucose_statistic_page
///
/// @Author: ygc
/// @Date: 2024/8/13 17:08
/// @Description:
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/food_and_sport_chart_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_cgm_section_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_daily_curve_section_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_daily_fluctuation_chart_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_glucose_agp_chart_section_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_glucose_chart_seciont_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_glucose_summary_section_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../resource/dnu_assets.dart';
import 'glucose_statistic_logic.dart';

class GlucoseStatisticPage extends StatelessWidget {
  const GlucoseStatisticPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<GlucoseStatisticLogic>();
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            left: 16.w,
            right: 16.w,
            bottom: 26.w,
          ),
          child: Column(
            children: [
              SizedBox(
                height: 16.w,
              ),
              const StatisticCgmSectionWidget(),
              const StatisticGlucoseChartSectionWidget(),
              StatisticGlucoseSummarySectionWidget(
                title: "最近一周统计",
                iconPath: DNUAssets.to.images.data.data7day,
              ),
              StatisticGlucoseSummarySectionWidget(
                title: "最近一月统计",
                iconPath: DNUAssets.to.images.data.data30day,
              ),
              // SizedBox(
              //   height: 16.w,
              // ),
//              日趋势图
              StatisticGlucoseAGPChartSectionWidget(),
              //日波动超标
              const StatisticDailyFluctuationChartWidget(),
              //每日血糖曲线
              StatisticDailyCurveSectionWidget(),
              //饮食摄入与运动消耗
              const FoodAndSportChartWidget()
            ],
          ),
        ),
      ),
    );
  }
}
