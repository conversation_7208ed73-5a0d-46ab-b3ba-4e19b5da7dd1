import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/app/data/widget/chart/data_chart_widget.dart';
import 'package:dnurse/app/data/widget/chart/painter/dnu_dot_icon_painter.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// class DataChartValue {
//   DataChartValue({
//     required this.dateTime,
//     this.value = 0,
//     required this.timePoint,
//     this.tag,
//   });

//   final DateTime dateTime;
//   final double value;
//   final TimePoint timePoint;
//   final dynamic tag;

//   @override
//   String toString() {
//     // TODO: implement toString
//     return "GlucoseChartData{dateTime:$dateTime, value:$value, timePoint:$timePoint, tag:$tag}";
//   }
// }

// class DataChart2Line {
//   DataChart2Line(
//     this.values, {
//     this.lineColor,
//     this.lineWidth = 0,
//     this.dotColor,
//     this.dotSize,
//     this.dotIcon,
//     this.dotType = DataChartDotType.circle,
//     this.dotColorCallback,
//     this.showDot = true,
//     this.isCurved = false,
//   });

//   final Color? lineColor;
//   final double lineWidth;
//   final bool isCurved;
//   final bool showDot;

//   final Color? dotColor;
//   final double? dotSize;
//   final IconData? dotIcon;
//   final DataChartDotType? dotType;
//   final DataLinePointColor? dotColorCallback;

//   final List<DataChartValue> values;
// }

typedef DataLinePointColor = Color Function(
  int pointTime,
  int lineIndex,
  int dataIndex,
  dynamic tag,
);
typedef DataLineTipMessage = String Function(
  int lineIndex,
  DateTime dateTime,
  DataChartValue data,
);

// 折线图表的线条
LineChartBarData get _emptyData => LineChartBarData(
      barWidth: 0,
      belowBarData: BarAreaData(show: false),
    );

// 默认按照时间生成折线数据
List<LineChartBarData> getLineData(
  List<DataChartLine> lines,
  double miniY,
) {
  List<LineChartBarData> result = [_emptyData];
  int lineIndex = 0;
  for (DataChartLine e in lines) {
    result.add(LineChartBarData(
      isCurved: e.isCurved,
      color: e.lineColor,
      barWidth: e.lineWidth,
      dashArray: e.dashArray,

      // preventCurveOvershootingThreshold: 0.35,
      // isStrokeCapRound: true,
      dotData: e.showDot
          ? FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                Color c;
                if (e.dotColorCallback != null) {
                  c = e.dotColorCallback!(
                      spot.x.toInt(), lineIndex, index, e.values[index].tag);
                } else {
                  c = e.dotColor ?? Colors.white;
                }

                DataChartDotType dotType = e.dotType;
                if (dotType == DataChartDotType.icon) {
                  return DNUDotIconPainter(
                    icon: e.dotIcon ?? DNUIconFont.shixinxiedi,
                    size: e.dotSize ?? 20,
                    color: c,
                  );
                } else {
                  return FlDotCirclePainter(
                    radius: (e.dotSize ?? 6),
                    color: c,
                    strokeColor: c,
                  );
                }
                // return FlDotCirclePainter(
                //   radius: 4,
                //   color: Colors.white,
                //   strokeWidth: 1.5.w,
                //   strokeColor: Colors.deepOrangeAccent.withOpacity(0.8),
                // );
              },
            )
          : const FlDotData(show: false),
      belowBarData: BarAreaData(show: false),
      spots: e.values
          .map((e) => FlSpot(e.dateTime.millisecondsSinceEpoch.toDouble(),
              e.value == 0 ? miniY : e.value))
          .toList(),
    ));
  }

  return result;
}

// 默认的gridData: 灰色虚线
// yAxisMin和yAxisMax不生效， 需要的话使用buildExtraLinesData
FlGridData buildCommonGridData(double yAxisInterval) {
  return FlGridData(
    drawHorizontalLine: true,
    horizontalInterval: yAxisInterval,
    drawVerticalLine: false,
    getDrawingHorizontalLine: (value) {
      return FlLine(
        color: const Color(0xFFE5E5E5),
        strokeWidth: 1.w,
        dashArray: [2, 2],
      );
    },
  );
}

// 顶部和底部的虚线线条
ExtraLinesData buildExtraLinesData({
  required double yAxisMax,
  bool? yAxisMinDashLine,
  double? yAxisMin,
}) {
  return ExtraLinesData(
    horizontalLines: [
      HorizontalLine(
        y: yAxisMax,
        color: const Color(0xFFE5E5E5),
        strokeWidth: 1.w,
        dashArray: [2, 2],
      ),
      if (yAxisMinDashLine != null)
        HorizontalLine(
          y: yAxisMin ?? 0,
          color: const Color(0xFFE5E5E5),
          strokeWidth: 1.w,
          dashArray: [2, 2],
        ),
    ],
  );
}

// 边框配置,只有底部
FlBorderData buildCommonBorderData() {
  return FlBorderData(
    show: true,
    border: Border(
      bottom: BorderSide(color: const Color(0xFFE5E5E5), width: 1.w),
    ),
  );
}

// 坐标轴字体样式
TextStyle axisTextStyle() {
  return TextStyle(
    color: const Color(0xFF808080),
    fontSize: 10.sp,
    fontFamily: AppFont.rany,
    height: 1.4,
  );
}

// 标题配置，仅有底部和左部
FlTitlesData buildCommonTitlesData(
    {required double xAxisInterval,
    required double yAxisInterval,
    required DateTime xAxisMax,
    xAxisDateFormat = "HH:mm",
    List<int>? timePoints}) {
  return FlTitlesData(
    bottomTitles: AxisTitles(
      sideTitles: SideTitles(
        showTitles: true,
        reservedSize: 20.w,
        interval: xAxisInterval,
        getTitlesWidget: (double value, TitleMeta meta) {
          Widget widget;

          if (timePoints != null) {
            if (timePoints!.contains(value.toInt())) {
              DateTime currentTime =
                  DateTime.fromMillisecondsSinceEpoch(value.toInt());
              widget = Text(
                currentTime.formatDate(xAxisDateFormat ?? "HH:mm"),
                style: axisTextStyle(),
              );

              return SideTitleWidget(
                axisSide: meta.axisSide,
                space: 6.w,
                child: widget,
              );
            }
          }

          double diff = xAxisMax.millisecondsSinceEpoch - value;
          if (diff > 0 && diff < xAxisInterval) {
            widget = Container();
          } else {
            DateTime currentTime =
                DateTime.fromMillisecondsSinceEpoch(value.toInt());
            widget = Text(currentTime.formatDate(xAxisDateFormat ?? "HH:mm"),
                style: axisTextStyle());
          }

          return SideTitleWidget(
            axisSide: meta.axisSide,
            space: 6.w,
            child: widget,
          );
        },
      ),
    ),
    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
    topTitles: const AxisTitles(
      sideTitles: SideTitles(showTitles: false),
    ),
    leftTitles: AxisTitles(
      sideTitles: SideTitles(
        getTitlesWidget: (double value, TitleMeta meta) {
          NumberFormat numberFormat = NumberFormat("####", "en_US");
          String text = numberFormat.format(value);

          return Text(
            text,
            style: axisTextStyle(),
          );
        },
        showTitles: true,
        interval: yAxisInterval,
        reservedSize: 20,
      ),
    ),
  );
}

// 触摸交互行为
// 默认触摸交互行为，设计图暂无，后期可扩展
LineTouchData defaultLineTouchData() => LineTouchData();

// 隐藏自带的触摸交互样式，用于自定义触摸交互
LineTouchData customLineTouchData(
        {required void Function(FlTouchEvent, LineTouchResponse?) touchCallback,
        LineTouchTooltipData? touchTooltipData}) =>
    LineTouchData(
      touchSpotThreshold: 20, // 增加触摸阈值
      getTouchLineStart: (_, __) => -double.infinity,
      getTouchLineEnd: (_, __) => double.infinity,
      getTouchedSpotIndicator:
          (LineChartBarData barData, List<int> spotIndexes) {
        return spotIndexes.map((index) {
          return TouchedSpotIndicatorData(
            FlLine(color: Colors.blue, strokeWidth: 0.w),
            FlDotData(
              getDotPainter: (spot, percent, barData, index) =>
                  FlDotCirclePainter(radius: 4, color: Colors.transparent),
            ),
          );
        }).toList();
      },
      touchTooltipData: touchTooltipData ?? defaultTooltipData(),
      // touchCallback为触摸回调，包含 touchEvent和toucheResponse
      // event为触摸事件，response为触摸到的点的数据
      touchCallback: touchCallback,
    );

LineTouchTooltipData defaultTooltipData() {
  return LineTouchTooltipData(
    getTooltipColor: (touchedSpot) => Colors.transparent,
    showOnTopOfTheChartBoxArea: true,
    getTooltipItems: defaultLineTooltipItem,
  );
}
