import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../../ui/style/color.dart';
import '../../../../../../../ui/style/font.dart';

class ItemData {
  final double value;
  final String content;

  const ItemData({required this.value, required this.content});
}

class BarChartSample57 extends StatefulWidget {
  final Map<int, double> mainItems;
  final double barWidth;
  final List<ItemData> dataList;

  const BarChartSample57({
    super.key,
    this.mainItems = const <int, double>{
      0: 8,
      1: 6.5,
      2: 6,
      3: 6.5,
      4: 9,
      5: 10,
      6: 4,
    },
    this.barWidth = 13,
    this.dataList = const [
      ItemData(value: 8, content: '6月21'),
      ItemData(value: 6.5, content: '6月22'),
      ItemData(value: 6, content: '6月23'),
      ItemData(value: 6.5, content: '6月24'),
      ItemData(value: 9, content: '6月25'),
      ItemData(value: 4, content: '6月26'),
      ItemData(value: 5, content: '6月27'),
    ],
  });

  @override
  State<StatefulWidget> createState() => BarChartSample57State();
}

class BarChartSample57State extends State<BarChartSample57> {
  late double barWidth;
  late Map<int, double> mainItems;
  int touchedIndex = -1;

  @override
  void initState() {
    mainItems = widget.mainItems;
    barWidth = widget.barWidth;
    super.initState();
  }

  Widget bottomTitles(double value, TitleMeta meta) {
    final style = TextStyle(
        color: const Color(0xFF808080),
        fontSize: 10.sp,
        fontFamily: AppFont.rany);
    // 直接从 widget.dataList 获取内容
    String text = widget.dataList[value.toInt()].content;
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(text, style: style),
    );
  }

  Widget leftTitles(double value, TitleMeta meta) {
    TextStyle style =
        TextStyle(color: const Color(0xFF808080), fontSize: 10.sp);
    String text;
    if (value == 0) {
      text = '0';
    } else {
      text = '${value.toInt()}';
    }
    return Padding(
      padding: EdgeInsets.only(right: 11.w),
      child: SideTitleWidget(
        angle: 0,
        axisSide: meta.axisSide,
        space: 4,
        child: Text(
          text,
          style: style,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  BarChartGroupData generateGroup(int x, double value) {
    final isTouched = touchedIndex == x;
    return BarChartGroupData(
      x: x,
      groupVertically: true,
      showingTooltipIndicators: isTouched ? [0] : [],
      barRods: [
        BarChartRodData(
          toY: value,
          width: barWidth,
          gradient: const LinearGradient(
            colors: [Color(0xFFED5F7E), Color(0xFFF07A2B)],
            stops: [0.0, 1.0],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: isTouched
              ? const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                )
              : BorderRadius.zero,
          rodStackItems: [
            BarChartRodStackItem(
              0,
              value,
              Colors.green,
              BorderSide(
                color: Colors.white,
                width: isTouched ? 2 : 0,
              ),
            ),
          ],
        ),
      ],
    );
  }

  bool isShadowBar(int rodIndex) => rodIndex == 1;

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.center,
        maxY: 12,
        minY: 0,
        groupsSpace: 24.w,
        barTouchData: BarTouchData(
          handleBuiltInTouches: false,
          touchCallback: (FlTouchEvent event, barTouchResponse) {
            if (!event.isInterestedForInteractions ||
                barTouchResponse == null ||
                barTouchResponse.spot == null) {
              setState(() {
                touchedIndex = -1;
              });
              return;
            }
            final rodIndex = barTouchResponse.spot!.touchedRodDataIndex;
            if (isShadowBar(rodIndex)) {
              setState(() {
                touchedIndex = -1;
              });
              return;
            }
            setState(() {
              touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;
            });
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 20.w,
              getTitlesWidget: bottomTitles,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: leftTitles,
              interval: 4,
              reservedSize: 28.w,
            ),
          ),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        gridData: FlGridData(
          show: true,
          drawHorizontalLine: true,
          //水平网格线的间隔可以是固定的数值，也可以根据图表的数据动态计算得出。
          horizontalInterval: 4,
          drawVerticalLine: false,
          getDrawingHorizontalLine: (value) {
            return FlLine(
                color: const Color(0xFFE5E5E5),
                strokeWidth: 1.w,
                dashArray: [2, 2]);
          },
        ),

        //图表边框
        borderData: FlBorderData(
          show: true, // 显示边框
          border: const Border(
            left: BorderSide.none,
            bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1),
            right: BorderSide.none, // 右边框不显示
            top: BorderSide.none, // 上边框不显示
          ),
        ),
        barGroups: widget.dataList.asMap().entries.map((entry) {
          return generateGroup(entry.key, entry.value.value);
        }).toList(),
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            HorizontalLine(
              y: 12,
              color: const Color(0xFFE5E5E5),
              strokeWidth: 1.w,
              dashArray: [2, 2],
            ),
          ],
        ),
      ),
    );
  }
}
