import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/utils/chart_config_utils.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_title_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:dnurse/app/data/widget/chart/dashed_divider.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:fl_chart/fl_chart.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../resource/dnu_assets.dart';

import '../../../../../widget/chart/data_chart2_widget.dart';
import '../../../../../widget/chart/triangle_widget.dart';
import '../glucose_statistic_logic.dart';

class StatisticDailyCurveSectionWidget extends StatelessWidget {
  StatisticDailyCurveSectionWidget({
    Key? key,
  }) : super(key: key);

  GlucoseStatisticLogic get logic => Get.find<GlucoseStatisticLogic>();

  final Rx<List<LineBarSpot>?> touchedSpot = null.obs;
  final Rx<Offset?> touchPosition = null.obs;

  final List<String> dates = [
    '6月21',
    '6月22',
    '6月23',
    '6月24',
    '6月25',
    '6月26',
    '6月27',
    '6月28'
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StatisticSectionTitleWidget(
            title: "每日血糖曲线",
            margin: EdgeInsets.only(top: 24.w),
            icon: StatisticTitleIconWidget(
              path: DNUAssets.to.images.data.dataPurplecurve,
            ),
            tailWidget: Image.asset(DNUAssets.to.images.data.dataArrowright)),
        Container(
            // height: 344.w,
            padding: EdgeInsets.only(top: 10.w, bottom: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFEEE6FA),
                width: 1.w,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x0A3947E5),
                  offset: Offset(0, 4),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                _buildChart(),
                Container(
                  width: double.infinity,
                  height: 40.w,
                  margin: EdgeInsets.only(left: 20.w, right: 15.w, top: 16.w),
                  child: Wrap(
                    spacing: 12.w,
                    runSpacing: 8.w,
                    children: List.generate(
                      dates.length,
                      (index) => _dateItem(index, dates),
                    ),
                  ),
                )
              ],
            )),
      ],
    );
  }

  Widget _buildChart() {
    DateTime now = DateTime.now();
    // 定义分段时间点
    final List<String> timeStrings = [
      "00:00",
      "04:00",
      "08:00",
      "12:00",
      "16:00",
      "20:00",
      "23:59"
    ];

    List<int> timePoints = timeStrings.map((t) {
      final parts = t.split(":");
      return DateTime(now.year, now.month, now.day, int.parse(parts[0]),
              int.parse(parts[1]))
          .millisecondsSinceEpoch;
    }).toList();

    const double yAxisInterval = 4;
    const double yAxisMin = 0;
    const double yAxisMax = 16;

    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: 112.w, left: 10.w, right: 15.w),
          height: 150.w,
          child: LineChart(
            LineChartData(
              minX: logic.agpglucoseStartTime.value.millisecondsSinceEpoch
                  .toDouble(),
              maxX: logic.agpglucoseEndTime.value.millisecondsSinceEpoch
                  .toDouble(),
              minY: yAxisMin,
              maxY: yAxisMax,
              lineBarsData: [],
              titlesData: buildCommonTitlesData(
                xAxisInterval: 4 * 60 * 60 * 1000,
                yAxisInterval: yAxisInterval,
                xAxisMax: logic.agpglucoseEndTime.value,
                timePoints: timePoints,
              ),
              gridData: buildCommonGridData(yAxisInterval),
              extraLinesData: buildExtraLinesData(
                  yAxisMax: yAxisMax, yAxisMinDashLine: false),
              borderData: buildCommonBorderData(),
            ),
          ),
        ),
        _tooltipCard(touchedSpot.value, touchPosition.value),
        Positioned(
          top: 68.w,
          bottom: 0.w,
          left: touchPosition.value == null
              ? 22.w
              : touchPosition.value!.dx + 20.w,
          child: Column(
            children: [
              TriangleWidget(
                width: 16.w,
                height: 8.w,
                color: AppColor.primaryBackgroundLight6,
              ),
              DashedVerticalLine(
                color: AppColor.primaryLight,
                height: 166.w,
              ),
            ],
          ),
        ),
      ],
    );

    // return DataChart2Widget(
    //   pointTarget: logic.pointTarget,
    //   // 根据需要设置
    //   lines: [],
    //   startTime: logic.agpglucoseStartTime.value,
    //   endTime: logic.agpglucoseEndTime.value,
    //   xAxisInterval: logic.agpglucoseXAxisInterval.toDouble(),
    //   // 4小时
    //   yAxisMin: 0,
    //   yAxisMax: 16,
    //   yAxisInterval: 4,
    //   lineTipMessage: (lineIndex, dateTime, data) {
    //     return "${dateTime.formatDate("M/d HH:mm")} - ${data.value}";
    //   },
    //   xAxisDateFormat: "HH:mm",
    //   showGrid: true,
    //   betweenColor: Colors.transparent,
    //   timePoints: timePoints,
    //   showCustomTooltip: true,
    // );
  }

  Widget _tooltipCard(List<LineBarSpot>? touchedSpot, Offset? touchPosition) {
    return Container(
      height: 68.w,
      width: 323.w,
      margin: EdgeInsets.only(left: 10.w, right: 10.w),
      // padding: EdgeInsets.only(left: 12.w, right: 21.w),
      decoration: BoxDecoration(
        color: AppColor.primaryBackgroundLight6,
        borderRadius: BorderRadius.circular(12.w),
      ),
      child: Center(
        child: Wrap(
          spacing: 5.w,
          runSpacing: 8.w,
          children: List.generate(
            dates.length,
            (index) => Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _dateItem(index, dates),
                SizedBox(
                  width: 25.w,
                  child: Text(
                    '9.9',
                    style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: AppFont.rany,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _dateItem(int index, List<String> dates) {
    // 颜色列表，您可以根据需要自定义或随机生成颜色
    final List<Color> colors = [
      AppColor.auxiliaryPink1,
      AppColor.auxiliaryBlue1,
      AppColor.auxiliaryGreen1,
      AppColor.auxiliaryOrange1,
      AppColor.auxiliaryRed1,
      AppColor.auxiliaryPurple1,
      const Color(0xFFA5D63F),
      const Color(0xFFFFEB3B),
    ];

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircleAvatar(
          backgroundColor: colors[index % colors.length],
          radius: 4.w, // 根据需要调整大小
        ),
        SizedBox(width: 2.w),
        SizedBox(
          width: 36.w,
          child: Text(
            dates[index],
            style: TextStyle(
              fontFamily: AppFont.rany,
              color: const Color(0xFF383838),
              fontSize: 12.sp,
              height: 16 / 12,
            ),
          ),
        ),
      ],
    );
  }
}
