import 'dart:math';

import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_subtitle_widget.dart';

import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../resource/dnu_assets.dart';
import 'chat5test2copy.dart';

class StatisticDailyFluctuationChartWidget extends StatelessWidget {
  const StatisticDailyFluctuationChartWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StatisticSectionSubTitleWidget(
            title: "日波动超标",
            subtitle: '(＞4.4)',
            subtitleColor: const Color(0xFF808080),
            margin: EdgeInsets.only(top: 24.w),
            icon: StatisticTitleIconWidget(
              path: DNUAssets.to.images.data.dataBodong,
            ),
            tailWidget: Image.asset(DNUAssets.to.images.data.dataArrowright)),
        Container(
          height: 175.w,
          width: 343.w,
          padding:
              EdgeInsets.only(right: 10.w, left: 10.w, top: 38.w, bottom: 12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFFEEE6FA),
              width: 1.w,
            ),
            boxShadow: const [
              BoxShadow(
                color: Color(0x0A3947E5),
                offset: Offset(0, 4),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: const BarChartSample57(),
        ),
      ],
    );
  }
}
