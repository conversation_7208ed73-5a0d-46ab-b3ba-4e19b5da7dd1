import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_title_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:dnurse/app/user/service/user_config_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../resource/dnu_assets.dart';
import '../../../../../common/data_status.dart';
import '../../../../../entity/data_glucose.dart';
import '../../../../../service/glucose_service.dart';
// import '../../../../../widget/chart/data_chart_widget.dart';
import '../glucose_statistic_logic.dart';
import '../utils/chart_config_utils.dart';

class StatisticGlucoseChartSectionWidget extends StatelessWidget {
  const StatisticGlucoseChartSectionWidget({
    Key? key,
  }) : super(key: key);

  GlucoseStatisticLogic get logic => Get.find<GlucoseStatisticLogic>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StatisticSectionTitleWidget(
          title: "连续血糖曲线",
          margin: EdgeInsets.only(top: 24.w),
          icon: StatisticTitleIconWidget(
            path: DNUAssets.to.images.main.scanDevice,
          ),
        ),
        Container(
          height: 186.w,
          padding:
              EdgeInsets.only(right: 16.w, left: 10.w, top: 24.w, bottom: 12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFFEEE6FA),
              width: 1,
            ),
            boxShadow: const [
              BoxShadow(
                color: Color(0x0A3947E5),
                offset: Offset(0, 4),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: _buildChart(),
        ),
      ],
    );
  }

  // Obx? _buildChartOld() {
  // return Obx(() {
  //   return DataChartWidget(
  //       startTime: logic.glucoseStartTime.value,
  //       endTime: logic.glucoseEndTime.value,
  //       xAxisInterval: logic.glucoseXAxisInterval.value,
  //       pointTarget: logic.pointTarget,
  //       showGrid: true,
  //       xAxisDateFormat: "M/d",
  //       lines: [
  //         if (logic.glucoseData.isNotEmpty)
  //           DataChartLine(
  //             logic.glucoseData
  //                 .map((element) => DataChartValue(
  //                       dateTime: element.time,
  //                       value: DataGlucose.displayValue(
  //                         element.mmolValue,
  //                         UserConfigService.to.glucoseUnit.value,
  //                       ),
  //                       timePoint: element.timePoint,
  //                       tag: element,
  //                     ))
  //                 .toList(),
  //             dotColor: Colors.red.withOpacity(0.4),
  //             dotType: DataChartDotType.icon,
  //             dotColorCallback: (
  //               int pointTime,
  //               int lineIndex,
  //               int dataIndex,
  //               dynamic tag,
  //             ) {
  //               if (tag != null) {
  //                 DataGlucose glucose = tag as DataGlucose;
  //                 DataValueStatus status = glucose.getValueStatus(
  //                     GlucoseService.to.currentDataTarget.value);

  //                 return status.color;
  //               }
  //               return Colors.blue.withOpacity(0.6);
  //             },
  //           ),
  //       ],
  //       yAxisMin: logic.glucoseYAxisMin.value,
  //       yAxisMax: logic.glucoseYAxisMax.value,
  //       yAxisInterval: logic.glucoseYAxisInterval.value,
  //       lineTipMessage: (
  //         int lineIndex,
  //         DateTime dateTime,
  //         DataChartValue data,
  //       ) {
  //         if (lineIndex == 0) {
  //           if (data.tag != null) {
  //             DataGlucose glucose = data.tag as DataGlucose;

  //             String value =
  //                 glucose.formatValue(UserConfigService.to.glucoseUnit.value);
  //             String unit = UserConfigService.to.glucoseUnit.value.name;

  //             String dateFormatStr = "HH:mm";

  //             String dateStr = glucose.time.formatDate(dateFormatStr);

  //             return "$value $unit\n$dateStr\n${glucose.timePoint.name}";
  //           }
  //         }
  //         return "";
  //       });
  // });
  // }

  Widget _buildChart() {
    return Obx(() {
      double yAxisInterval = logic.glucoseYAxisInterval.value;
      double yAxisMax = logic.glucoseYAxisMax.value;
      List<LineChartBarData> lineBarsData =
          getLineData([], logic.glucoseYAxisMin.value);

      double xAxisInterval = 24 * 60 * 60 * 1000;

      return LineChart(LineChartData(
        minX: logic.glucoseStartTime.value.millisecondsSinceEpoch.toDouble(),
        maxX: logic.glucoseEndTime.value.millisecondsSinceEpoch.toDouble(),
        minY: logic.glucoseYAxisMin.value,
        maxY: yAxisMax,
        baselineX:
            logic.glucoseStartTime.value.millisecondsSinceEpoch.toDouble(),
        baselineY: logic.glucoseYAxisMin.value,
        lineBarsData: lineBarsData,
        titlesData: buildCommonTitlesData(
          xAxisInterval: xAxisInterval,
          yAxisInterval: yAxisInterval,
          xAxisMax: logic.glucoseEndTime.value,
          xAxisDateFormat: "M/d",
        ),
        gridData: buildCommonGridData(yAxisInterval),
        extraLinesData:
            buildExtraLinesData(yAxisMax: yAxisMax, yAxisMinDashLine: false),
        borderData: buildCommonBorderData(),
        rangeAnnotations: RangeAnnotations(
          horizontalRangeAnnotations: [
            HorizontalRangeAnnotation(
              y1: logic.pointTarget.mini,
              y2: logic.pointTarget.max,
              // color: const Color(0xFFE5E5E5),
              gradient: const LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  Color(0x1A35A1DB),
                  Color(0x1A36BF6F),
                ],
              ),
            ),
          ],
        ),
      ));
    });
  }
}
