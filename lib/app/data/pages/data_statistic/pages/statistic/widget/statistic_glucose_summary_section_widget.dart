/// FileName: statistic_glucose_summary_section_widget
///
/// @Author: ygc
/// @Date: 2024/8/13 19:12
/// @Description:
import 'dart:math';

import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_title_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:dnurse/app/user/service/user_config_service.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../resource/dnu_assets.dart';
import '../../../../../../../ui/style/color.dart';
import '../glucose_statistic_logic.dart';

class StatisticGlucoseSummarySectionWidget extends StatelessWidget {
  const StatisticGlucoseSummarySectionWidget({
    Key? key,
    required this.title,
    required this.iconPath,
  }) : super(key: key);

  final String title;
  final String iconPath;

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<GlucoseStatisticLogic>();
    return Column(
      children: [
        StatisticSectionTitleWidget(
            title: title,
            margin: EdgeInsets.only(top: 24.w),
            icon: StatisticTitleIconWidget(
              path: iconPath,
            ),
            tailWidget: _buildTailWidget(0.2)),
        Padding(
          padding: EdgeInsets.only(top: 2.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildSummaryItem(
                title: "达标率",
                value: "11",
              ),
              _buildSummaryItem(
                title: "模拟糖化",
                value: "11",
              ),
              _buildSummaryItem(
                title: "平均血糖",
                value: "11",
                showPercent: false,
                unit: UserConfigService.to.glucoseUnit.value.name,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTailWidget(double compareValue) {
    return Row(
      children: [
        compareValue > 0
            ? ShaderMask(
                shaderCallback: (Rect bounds) {
                  return AppColor.gradientGreenBlue.createShader(bounds);
                },
                child: const Icon(
                  DNUIconFont.xunsuxiajiang,
                  color: Colors.white,
                ),
              )
            : ShaderMask(
                shaderCallback: (Rect bounds) {
                  return AppColor.gradientRedOrange.createShader(bounds);
                },
                child: const Icon(
                  DNUIconFont.svg_fenhuaban_217,
                  color: Colors.white,
                ),
              ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: Text(
            '比上周${compareValue < 0 ? '退步' : '改善'}',
            style: TextStyle(fontSize: 12.sp, color: Color(0x99000000)),
          ),
        ),
        Text(
          '${compareValue.abs().toStringAsFixed(1)}%',
          style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: compareValue < 0
                  ? AppColor.auxiliaryRed1
                  : AppColor.funcGlucoseNormal),
        ),
      ],
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required String value,
    bool showPercent = true,
    String? unit,
  }) {
    return Container(
      width: 110.w,
      height: 120.w,
      padding: EdgeInsets.only(
        top: 20.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColor.primaryOpacity6,
            AppColor.primaryOpacity2,
          ],
          transform: GradientRotation(132.49 * pi / 180), // 将角度转换为弧度
        ),
        border: Border.all(
          color: const Color(0xFFEEE6FA).withOpacity(0.33),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF383838),
              fontSize: 16.sp,
              height: 23 / 16,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(
            height: 15.w,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                value,
                style: TextStyle(
                  color: AppColor.primary,
                  fontSize: 33.sp,
                  fontWeight: FontWeight.w700,
                  height: 0.9,
                ),
              ),
              if (showPercent)
                Text(
                  "%",
                  style: TextStyle(
                    color: const Color(0xFF808080),
                    fontSize: 24.sp,
                    height: 0.9,
                    fontWeight: FontWeight.w400,
                  ),
                ),
            ],
          ),
          unit != null
              ? Text(
                  unit,
                  style: TextStyle(
                    color: const Color(0xFF808080),
                    fontSize: 12.sp,
                    height: 0.9,
                    fontWeight: FontWeight.w400,
                  ),
                )
              : const SizedBox(),
        ],
      ),
    );
  }

  Widget _buildEmpty() {
    return Container(
      padding: EdgeInsets.only(
        top: 25.w,
        bottom: 20.w,
        left: 12.w,
        right: 12.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColor.primaryOpacity6,
            AppColor.primaryOpacity2,
          ],
          transform: GradientRotation(132.49 * pi / 180), // 将角度转换为弧度
        ),
        border: Border.all(
          color: const Color(0xFFEEE6FA).withOpacity(0.33),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Image.asset(
            DNUAssets.to.images.data.cgm,
            width: 122.w,
          ),
          Expanded(
            child: Text(
              "您还没有使用动态血糖仪",
              style: TextStyle(
                color: AppColor.textDesc,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
