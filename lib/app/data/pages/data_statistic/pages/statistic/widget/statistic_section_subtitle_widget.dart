import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatisticSectionSubTitleWidget extends StatelessWidget {
  const StatisticSectionSubTitleWidget({
    super.key,
    required this.title,
    this.icon,
    this.tailWidget,
    this.topMargin,
    this.margin,
    this.subtitle, // 新增副标题参数
    this.subtitleColor, // 新增副标题颜色参数
  });

  final String title;
  final Widget? icon;
  final Widget? tailWidget;
  final double? topMargin;
  final EdgeInsetsGeometry? margin;
  final String? subtitle; // 定义副标题
  final Color? subtitleColor; // 定义副标题颜色

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      margin: margin ??
          EdgeInsets.only(
            top: 24.w,
          ),
      child: Row(
        children: [
          if (icon != null)
            Padding(
              padding: EdgeInsets.only(right: 10.w),
              child: icon,
            ),
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: Text(
              subtitle!, // 新增副标题的 Text 组件
              style: TextStyle(
                fontSize: 16.sp,
                color: subtitleColor ?? const Color(0xFF808080), // 应用副标题颜色
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          const Spacer(),
          if (tailWidget != null)
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: tailWidget,
            ),
        ],
      ),
    );
  }
}
