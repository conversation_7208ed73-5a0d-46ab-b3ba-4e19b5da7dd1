/// FileName: statistic_section_title_widget
///
/// @Author: ygc
/// @Date: 2024/8/13 18:08
/// @Description:
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatisticSectionTitleWidget extends StatelessWidget {
  const StatisticSectionTitleWidget({
    super.key,
    required this.title,
    this.explain,
    this.icon,
    this.tailWidget,
    this.topMargin,
    this.margin,
  });

  final String title;
  final String? explain;
  final Widget? icon;
  final Widget? tailWidget;
  final double? topMargin;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      margin: margin ??
          EdgeInsets.only(
            top: 24.w,
          ),
      child: Row(
        children: [
          if (icon != null)
            Padding(
              padding: EdgeInsets.only(right: 10.w),
              child: icon,
            ),
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (explain != null)
            Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: Text(
                explain!,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xE6383838),
                ),
              ),
            ),
          const Spacer(),
          if (tailWidget != null)
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: tailWidget,
            ),
        ],
      ),
    );
  }
}
