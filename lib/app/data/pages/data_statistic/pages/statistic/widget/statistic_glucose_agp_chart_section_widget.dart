import 'dart:math';

import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/utils/chart_config_utils.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_subtitle_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:dnurse/app/data/widget/chart/dashed_divider.dart';
import 'package:dnurse/app/data/widget/chart/triangle_widget.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../../../resource/dnu_assets.dart';
import '../../../../../../../ui/style/color.dart';
import '../../../../../common/time_point.dart';
import '../../../../../widget/chart/data_chart2_widget.dart';
import '../glucose_statistic_logic.dart';

class StatisticGlucoseAGPChartSectionWidget extends StatelessWidget {
  StatisticGlucoseAGPChartSectionWidget({
    Key? key,
  }) : super(key: key);

  GlucoseStatisticLogic get logic => Get.find<GlucoseStatisticLogic>();

  final Rx<List<LineBarSpot>?> touchedSpot = null.obs;
  final Rx<Offset?> touchPosition = null.obs;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StatisticSectionSubTitleWidget(
            title: "日趋势图",
            subtitle: '(AGP)',
            subtitleColor: const Color(0xCC383838),
            margin: EdgeInsets.only(top: 24.w),
            icon: StatisticTitleIconWidget(
              path: DNUAssets.to.images.data.dataRiqushi,
            ),
            tailWidget: Image.asset(DNUAssets.to.images.data.dataArrowright)),
        Container(
            // height: 313.w,
            width: 343.w,
            padding: EdgeInsets.only(top: 10.w, bottom: 12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFEEE6FA),
                width: 1.w,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x0A3947E5),
                  offset: Offset(0, 4),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                // _builderAGPChart(context),
                _buildChart(),
                SizedBox(height: 8.w),
                Container(
                  margin: EdgeInsets.only(
                    left: 12.w,
                    right: 12,
                  ),
                  padding: EdgeInsets.only(
                    left: 7.w,
                  ),
                  // width: 319.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(139),
                    gradient: const LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: [
                        Color(0x0ff07a2b),
                        Color(0x0ffd5f7e),
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      _builderRedLight(context),
                      SizedBox(width: 13.w),
                      _builderTitle(context, '高血糖易发时段', '15:30', '16:20'),
                      SizedBox(width: 30.w),
                      _builderTitle(context, '低血糖易发时段', '10:45', '11:56',
                          isLowBloodSugar: true)
                    ],
                  ),
                )
              ],
            )),
      ],
    );
  }

  Widget _headerItem(String title, String value, Color valueColor) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: '$title: ',
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF383838),
              fontFamily: AppFont.rany,
            ),
          ),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle, // 垂直居中
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: valueColor,
                  fontWeight: FontWeight.w500,
                  fontFamily: AppFont.rany,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _builderRedLight(BuildContext context) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.w),
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  const Color(0xffff07a2b).withOpacity(0.2),
                  const Color(0xfffed5f7e).withOpacity(0.2),
                ],
              ),
            ),
          ),
          Container(
            width: 28.w,
            height: 28.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.w),
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  Color(0xFFFF07A2B).withOpacity(0.3),
                  Color(0xFFFED5F7E).withOpacity(0.3),
                ],
              ),
            ),
          ),
          Image.asset(
            DNUAssets.to.images.data.dataRedlight,
            height: 24.w,
            fit: BoxFit.cover,
          )
        ],
      ),
    );
  }

  Widget _builderTitle(
      BuildContext context, String text, String startTime, String endTime,
      {bool isLowBloodSugar = false}) {
    return Container(
      height: 60.w,
      padding: EdgeInsets.only(top: 10.w),
      child: Column(
        children: [
          Text(
            text,
            style: TextStyle(fontSize: 12.sp, color: const Color(0xFF808080)),
          ),
          Text(
            '$startTime~$endTime',
            style: TextStyle(
                fontSize: 18.sp,
                color: isLowBloodSugar
                    ? AppColor.funcGlucoseLow1
                    : AppColor.funcGlucoseHigh1,
                fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _builderAGPChart(BuildContext context) {
    DateTime now = DateTime.now();
    // 定义分段时间点
    final List<String> timeStrings = [
      "00:00",
      "04:00",
      "08:00",
      "12:00",
      "16:00",
      "20:00",
      "23:59"
    ];

    List<int> timePoints = timeStrings.map((t) {
      final parts = t.split(":");
      return DateTime(now.year, now.month, now.day, int.parse(parts[0]),
              int.parse(parts[1]))
          .millisecondsSinceEpoch;
    }).toList();

    return Obx(() {
      return Container(
        padding: EdgeInsets.only(left: 10.w, right: 18.w, top: 10.w),
        child: DataChart2Widget(
          height: 160.w,

          pointTarget: null,
          // 根据需要设置
          lines: [],
          startTime: logic.agpglucoseStartTime.value,
          endTime: logic.agpglucoseEndTime.value,
          xAxisInterval: 4 * 60 * 60 * 1000,
          yAxisMin: 0,
          yAxisMax: 16,
          yAxisInterval: 4,
          lineTipMessage: (lineIndex, dateTime, data) {
            return "${dateTime.formatDate("M/d HH:mm")} - ${data.value}";
          },
          xAxisDateFormat: "HH:mm",
          showGrid: true,
          timePoints: timePoints,
          showCustomTooltip: true,
          tooltipCard: _tooltipCard,
        ),
      );
    });
  }

  // 带拖拽线的折线图
  Widget _buildChart() {
    const double yAxisInterval = 4;
    const double yAxisMin = 0;
    const double yAxisMax = 16;

    List<LineChartBarData> lineBarsData = getLineData([], yAxisMin);

    // 定义分段时间点
    final List<String> timeStrings = [
      "00:00",
      "04:00",
      "08:00",
      "12:00",
      "16:00",
      "20:00",
      "23:59"
    ];

    DateTime now = DateTime.now();

    List<int> timePoints = timeStrings.map((t) {
      final parts = t.split(":");
      return DateTime(now.year, now.month, now.day, int.parse(parts[0]),
              int.parse(parts[1]))
          .millisecondsSinceEpoch;
    }).toList();

    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(left: 10.w, right: 18.w, top: 73.w),
          height: 150.w,
          child: LineChart(
            LineChartData(
              minX: logic.agpglucoseStartTime.value.millisecondsSinceEpoch
                  .toDouble(),
              maxX: logic.agpglucoseEndTime.value.millisecondsSinceEpoch
                  .toDouble(),
              maxY: yAxisMax,
              minY: yAxisMin,
              lineBarsData: lineBarsData,
              titlesData: buildCommonTitlesData(
                xAxisInterval: 4 * 60 * 60 * 1000,
                yAxisInterval: yAxisInterval,
                xAxisMax: logic.agpglucoseEndTime.value,
                timePoints: timePoints,
              ),
              betweenBarsData: [],
              gridData: buildCommonGridData(yAxisInterval),
              extraLinesData: buildExtraLinesData(
                  yAxisMax: yAxisMax, yAxisMinDashLine: false),
              borderData: buildCommonBorderData(),
              lineTouchData: customLineTouchData(
                touchCallback: (event, response) {
                  if (response?.lineBarSpots != null &&
                      response!.lineBarSpots!.isNotEmpty) {
                    touchedSpot.value = response.lineBarSpots;
                    touchPosition.value = event.localPosition;
                  }
                },
              ),
            ),
          ),
        ),
        _tooltipCard(touchedSpot.value, touchPosition.value),
        Positioned(
          top: 48.w,
          bottom: 20.w,
          left: touchPosition.value == null
              ? 22.w
              : touchPosition.value!.dx + 20.w,
          child: Column(
            children: [
              TriangleWidget(
                width: 16.w,
                height: 8.w,
                color: AppColor.primaryBackgroundLight6,
              ),
              DashedVerticalLine(
                color: AppColor.primaryLight,
                height: 144.w,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _tooltipCard(List<LineBarSpot>? touchedSpot, Offset? touchPosition) {
    return Container(
      height: 48.w,
      margin: EdgeInsets.only(left: 10.w, right: 10.w),
      padding: EdgeInsets.symmetric(horizontal: 18.w),
      decoration: BoxDecoration(
        color: AppColor.primaryBackgroundLight6,
        borderRadius: BorderRadius.circular(12.w),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // _headerItem(
          //     'x=',
          //     touchedSpot != null && touchedSpot!.isNotEmpty
          //         ? touchedSpot!.first.x.toString()
          //         : '--',
          //     AppColor.primary),
          // _headerItem(
          //     'y=',
          //     touchedSpot != null && touchedSpot!.isNotEmpty
          //         ? touchedSpot!.first.y.toString()
          //         : '--',
          //     AppColor.primary),
          // _headerItem('offset.x', '${touchPosition?.dx.toInt()}',
          //     AppColor.funcGlucoseLow1),

          // touchedSpot拿到数据
          _headerItem('时间', '20:00', AppColor.primary),
          _headerItem('平均', '4.7', AppColor.funcGlucoseLow1),
          _headerItem('最高', '6.8', AppColor.funcGlucoseNormal),
          _headerItem('最低', '3.7', AppColor.funcGlucoseLow1),
        ],
      ),
    );
  }
}
