import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_title_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../../../resource/dnu_assets.dart';
import '../../../../../../../ui/style/color.dart';
import '../../../../../widget/chart/data_chart2_widget.dart';
import '../glucose_statistic_logic.dart';

class FoodAndSportChartWidget extends StatelessWidget {
  const FoodAndSportChartWidget({
    Key? key,
  }) : super(key: key);

  GlucoseStatisticLogic get logic => Get.find<GlucoseStatisticLogic>();

  @override
  Widget build(BuildContext context) {
    int selectedPeriod = 0;
    return Column(
      children: [
        StatisticSectionTitleWidget(
            title: "饮食摄入与运动消耗",
            margin: EdgeInsets.only(top: 24.w),
            icon: StatisticTitleIconWidget(
              path: DNUAssets.to.images.data.dataGaizi,
            ),
            tailWidget: Image.asset(DNUAssets.to.images.data.dataZishiying)),
        Container(
            width: 343.w,
            padding: EdgeInsets.only(top: 16.w, bottom: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFEEE6FA),
                width: 1.w,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x0A3947E5),
                  offset: Offset(0, 4),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                _buildTab(context),
                _buildTimer(context),
                _buildChart(context),
                _buildLineColor(context)
              ],
            )),
      ],
    );
  }

  Widget _buildTab(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Container(
        height: 40.w,
        width: 311.w,
        decoration: BoxDecoration(
          color: AppColor.background1,
          borderRadius: BorderRadius.circular(12.w),
        ),
        padding: EdgeInsets.all(4.w),
        child: TabBar(
          controller: logic.tabController,
          tabs: logic.tabs,
          indicator: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(9.w),
            border: Border.all(color: const Color(0xFFEEE6FA), width: 1.w),
            boxShadow: [
              BoxShadow(
                  color: AppColor.primary.withOpacity(0.04),
                  blurRadius: 10.w,
                  offset: Offset(0, 4.w))
            ],
          ),
          labelStyle: TextStyle(fontSize: 16.sp),
          labelColor: const Color(0xFF383838),
          unselectedLabelColor: const Color(0xFF808080),
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent, // This removes the divider line
        ),
      ),
    );
  }

  Widget _buildTimer(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 13.w, left: 22.w),
      child: Obx(() {
        return Text(
          '${logic.calorStartTimeFormatted}~${logic.calorEndTimeFormatted}',
          style: TextStyle(
            fontFamily: AppFont.rany,
            fontSize: 14.sp,
            height: 16 / 14,
            color: const Color(0xFF383838),
          ),
        );
      }),
    );
  }

  Widget _buildChart(BuildContext context) {
    return Container(
      width: 309.w,
      height: 170.w,
      margin: EdgeInsets.only(top: 22.w),
      child: Obx(() {
        return DataChart2Widget(
          pointTarget: null,
          // 根据需要设置
          lines: [],
          startTime: logic.calorStartTime.value,
          endTime: logic.calorEndTime.value,
          xAxisInterval: logic.calorXAxisInterval.value,
          yAxisMin: 0.0,
          yAxisMax: 800.0,
          yAxisInterval: 200.0,
          lineTipMessage: (lineIndex, dateTime, data) {
            return "${dateTime.formatDate("M/d HH:mm")} - ${data.value}";
          },
          xAxisDateFormat: "M/d",
          showGrid: true,
          betweenColor: Colors.transparent,
          hasBottomBorder: false,
          yAxisMinDashLine: true,
        );
      }),
    );
  }

  Widget _buildLineColor(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, top: 16.w),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: const BoxDecoration(
              color: AppColor.auxiliaryOrange1,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 2.w),
          Text(
            '饮食摄入',
            style: TextStyle(color: const Color(0xFF383838), fontSize: 12.sp),
          ),
          SizedBox(width: 12.w),
          Container(
            width: 8.w,
            height: 8.w,
            decoration: const BoxDecoration(
              color: AppColor.auxiliaryBlue1,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 2.w),
          Text(
            '运动消耗',
            style: TextStyle(color: const Color(0xFF383838), fontSize: 12.sp),
          )
        ],
      ),
    );
  }
}
