import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../../ui/style/color.dart';
import '../../../../../../../ui/style/font.dart';
import '../glucose_statistic_logic.dart';

void main() {
  runApp(
    MaterialApp(
      home: Scaffold(
        body: Center(
          child: LineChartSample77(),
        ),
      ),
    ),
  );
}

class LineChartSample77 extends StatelessWidget {
  LineChartSample77({
    super.key,
    Color? line1Color,
    Color? line2Color,
    Color? betweenColor,
  })  : line1Color = line1Color ?? Colors.green,
        line2Color = line2Color ?? Colors.red,
        betweenColor = betweenColor ?? const Color(0xFFEEE6FA);

  final Color line1Color;
  final Color line2Color;
  final Color betweenColor;

  GlucoseStatisticLogic get logic => Get.find<GlucoseStatisticLogic>();

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(fontSize: 10);
    // 定义每隔4小时的时间标签
    final timeLabels = [
      '00:00',
      '04:00',
      '08:00',
      '12:00',
      '16:00',
      '20:00',
      '23:59'
    ];
    String text = timeLabels[value.toInt()];
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(text, style: style),
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(fontSize: 10);

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        ' ${value.toInt()}',
        style: style,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 2,

      child: LineChart(
        LineChartData(
          minY: 0,
          maxY: 16,
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              fitInsideHorizontally: true,
              fitInsideVertically: false,
              // tooltipBgColor: AppColor.primaryLightOpacity6,
              getTooltipColor: (LineBarSpot spot) =>
                  AppColor.primaryLightOpacity6,
              tooltipRoundedRadius: 12,
              tooltipMargin: 120.w,
              tooltipPadding: const EdgeInsets.only(
                  left: 150, top: 8, bottom: 8, right: 160),
              getTooltipItems: (spots) {
                return spots.map((spot) {
                  int index = spot.barIndex;
                  FlSpot flSpot = spot;
                  return LineTooltipItem(
                    "时间：", // 格式化显示的数据
                    const TextStyle(
                        color: Colors.black, fontWeight: FontWeight.bold),
                  );
                }).toList();
              },
            ),
            getTouchedSpotIndicator: (barData, spotIndexes) {
              return spotIndexes.map((index) {
                return TouchedSpotIndicatorData(
                  FlLine(
                    strokeWidth: 2.w,
                    color: Colors.deepOrangeAccent.withOpacity(0.8),
                    dashArray: [4, 2],
                  ),
                  FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: 4,
                        color: Colors.white,
                        strokeWidth: 1.5.w,
                        strokeColor: Colors.deepOrangeAccent.withOpacity(0.8),
                      );
                    },
                  ),
                );
              }).toList();
            },
          ),
          lineBarsData: [
            LineChartBarData(
              spots: const [
                FlSpot(0, 1),
                FlSpot(1, 3.5),
                FlSpot(2, 4.5),
                FlSpot(3, 1),
                FlSpot(4, 4),
                FlSpot(5, 6),
                FlSpot(6, 6.5),
                // FlSpot(7, 6),
                // FlSpot(8, 4),
                // FlSpot(9, 6),
                // FlSpot(10, 6),
                // FlSpot(11, 7),
              ],
              isCurved: true,
              barWidth: 2.w,
              color: line1Color,
              dotData: FlDotData(
                show: false,
              ),
            ),
            LineChartBarData(
              spots: const [
                FlSpot(0, 7),
                FlSpot(1, 3),
                FlSpot(2, 4),
                FlSpot(3, 2),
                FlSpot(4, 3),
                FlSpot(5, 4),
                FlSpot(6, 5),
                // FlSpot(7, 3),
                // FlSpot(8, 1),
                // FlSpot(9, 8),
                // FlSpot(10, 1),
                // FlSpot(11, 3),
              ],
              isCurved: false,
              barWidth: 2,
              color: line2Color,
              dotData: FlDotData(
                show: false,
              ),
            ),
          ],
          betweenBarsData: [
            BetweenBarsData(
              fromIndex: 0,
              toIndex: 1,
              color: betweenColor,
            )
          ],
          borderData: FlBorderData(
            show: true, // 显示边框
            border: Border(
              left: BorderSide.none,
              bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1.w),
              right: BorderSide.none, // 右边框不显示
              top: BorderSide.none, // 上边框不显示
            ),
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 1,
                getTitlesWidget: bottomTitleWidgets,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: leftTitleWidgets,
                interval: 4,
                reservedSize: 32,
              ),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          gridData: FlGridData(
            show: true,
            drawHorizontalLine: true,
            //水平网格线的间隔可以是固定的数值，也可以根据图表的数据动态计算得出。
            horizontalInterval: 2,
            drawVerticalLine: false,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                  color: const Color(0xFFE5E5E5),
                  strokeWidth: 1,
                  dashArray: [4, 2]);
            },
          ),
        ),
      ),
      // ),
    );
  }
}
