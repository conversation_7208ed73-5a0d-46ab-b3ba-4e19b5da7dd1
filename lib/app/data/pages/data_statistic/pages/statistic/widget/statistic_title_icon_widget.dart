/// FileName: statistic_title_icon_widget
///
/// @Author: ygc
/// @Date: 2024/8/13 18:11
/// @Description:
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatisticTitleIconWidget extends StatelessWidget {
  final String path;
  final double? width;

  const StatisticTitleIconWidget({
    Key? key,
    required this.path,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      path,
      width: width ?? 24.w,
      fit: BoxFit.cover,
    );
  }
}
