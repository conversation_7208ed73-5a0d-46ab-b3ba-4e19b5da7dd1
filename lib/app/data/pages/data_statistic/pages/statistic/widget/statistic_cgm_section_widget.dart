/// FileName: statistic_cgm_section_widget
///
/// @Author: ygc
/// @Date: 2024/8/13 18:13
/// @Description:
import 'dart:math';

import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_section_title_widget.dart';
import 'package:dnurse/app/data/pages/data_statistic/pages/statistic/widget/statistic_title_icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../resource/dnu_assets.dart';
import '../../../../../../../ui/style/color.dart';
import '../glucose_statistic_logic.dart';

class StatisticCgmSectionWidget extends StatelessWidget {
  const StatisticCgmSectionWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<GlucoseStatisticLogic>();
    return Column(
      children: [
        StatisticSectionTitleWidget(
          title: "当前动态血糖仪信息",
          margin: EdgeInsets.only(top: 17.w),
          icon: StatisticTitleIconWidget(
            path: DNUAssets.to.images.main.scanDevice,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 4.w, left: 8.w),
          child: Obx(() {
            if (logic.cgmInfo.value == null) {
              return _buildEmpty();
            }

            return Row(
              children: [
                Image.asset(
                  DNUAssets.to.images.data.cgm,
                  width: 122.w,
                ),
                SizedBox(
                  width: 18.w,
                ),
                Expanded(
                  child: Column(
                    children: [
                      _buildSummaryItem(
                          title: "型号",
                          value: logic.cgmInfo.value!.model,
                          margin: EdgeInsets.zero),
                      _buildSummaryItem(
                        title: "激活时间",
                        value: logic.cgmInfo.value!.activateDate,
                      ),
                      _buildSummaryItem(
                        title: "激活天数",
                        value: logic.cgmInfo.value!.activateDays,
                      ),
                      _buildSummaryItem(
                        title: "总测量次数",
                        value: "${logic.cgmInfo.value!.dataCount}",
                      ),
                    ],
                  ),
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required String value,
    EdgeInsetsGeometry? margin,
  }) {
    final bool isBold = title != '型号';

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 8.w,
      ),
      margin: margin ??
          EdgeInsets.only(
            top: 8.w,
          ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColor.primaryOpacity6,
            AppColor.primaryOpacity2,
          ],
          transform: GradientRotation(132.49 * pi / 180), // 将角度转换为弧度
        ),
        border: Border.all(
          color: const Color(0xFFEEE6FA).withOpacity(0.33),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF383838),
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: AppColor.primary,
                fontSize: 16.sp,
                fontWeight: isBold ? FontWeight.w600 : FontWeight.w400,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty() {
    return Container(
      padding: EdgeInsets.only(
        top: 25.w,
        bottom: 20.w,
        left: 12.w,
        right: 12.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColor.primaryOpacity6,
            AppColor.primaryOpacity2,
          ],
          transform: GradientRotation(132.49 * pi / 180), // 将角度转换为弧度
        ),
        border: Border.all(
          color: const Color(0xFFEEE6FA).withOpacity(0.33),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Image.asset(
            DNUAssets.to.images.data.cgm,
            width: 122.w,
          ),
          Expanded(
            child: Text(
              "您还没有使用动态血糖仪",
              style: TextStyle(
                color: AppColor.textDesc,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
