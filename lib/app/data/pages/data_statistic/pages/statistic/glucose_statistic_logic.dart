/// FileName: glucose_statistic_logic
///
/// @Author: ygc
/// @Date: 2024/8/13 17:08
/// @Description:
import 'dart:async';

import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../../../framework/utils/log.dart';
import '../../../../../../resource/dnu_assets.dart';
import '../../../../../user/entity/unit/glucose_unit.dart';
import '../../../../../user/service/user_config_service.dart';
import '../../../../common/data_target.dart';

import '../../../../service/glucose_service.dart';
import '../data_statistic_base_page.dart';

const String dataStatisticGlucose = 'dataStatisticGlucose';

//模拟卡路里
enum TimePoint2 { breakfast, lunch, dinner, snack, exercise }

class CalorieData {
  String id;
  String serialNumber;
  double calorieValue; // 卡路里值
  TimePoint2 timePoint; // 饮食摄入或运动的时间点
  DateTime dateTime; // 具体时间

  CalorieData({
    required this.id,
    required this.serialNumber,
    required this.calorieValue,
    required this.timePoint,
    required this.dateTime,
  });
}

class GcmDeviceInfo {
  String model;
  DateTime activateTime;
  int dataCount;
  String iconPath;

  GcmDeviceInfo({
    required this.model,
    required this.activateTime,
    required this.iconPath,
    this.dataCount = 0,
  });

  String get activateDate {
    return activateTime.formatDate("MM-dd HH:mm");
  }

  String get activateDays {
    int differenceInDays = DateTime.now().difference(activateTime).inDays;
    return "$differenceInDays天";
  }
}

class GlucoseStatisticLogic extends DataStatisticBasePageLogic
    with GetTickerProviderStateMixin {
  final cgmInfo = Rx<GcmDeviceInfo?>(null);

  final glucoseStartTime = DateTime.now().obs;
  final glucoseEndTime = DateTime.now().obs;
  final glucoseXAxisInterval = 0.0.obs;
  final glucoseYAxisMin = 0.0.obs;
  final glucoseYAxisMax = 0.0.obs;
  final glucoseYAxisInterval = 0.0.obs;
  final glucoseData = <DataGlucose>[].obs;
  late StreamSubscription<GlucoseUnit> glucoseUnitStream;

  //日趋势
  final agpglucoseEndTime = DateTime.now().obs;
  final agpglucoseStartTime = DateTime.now().obs;
  final agpglucoseXAxisInterval = 4 * 60 * 60 * 1000;

  //饮食摄入与运动消耗
  final calorStartTime = DateTime.now().obs;
  final calorEndTime = DateTime.now().obs;
  final calorXAxisInterval = 0.0.obs;
  final calorYAxisMin = 0.0.obs;
  final calorYAxisMax = 700.0.obs;
  final calorYAxisInterval = 100.0.obs;
  final calorIntakeData = <CalorieData>[].obs;
  final calorExpenditureData = <CalorieData>[].obs;

  String get calorStartTimeFormatted =>
      DateFormat('yyyy/MM/dd').format(calorStartTime.value);

  String get calorEndTimeFormatted =>
      DateFormat('yyyy/MM/dd').format(calorEndTime.value);

  late TabController tabController;
  late List<Tab> tabs;

  @override
  void onInit() {
    super.onInit();

    glucoseUnitStream = UserConfigService.to.glucoseUnit.listen((data) {
      _calculateMiniMax();
    });

    cgmInfo.value = GcmDeviceInfo(
      model: "DN-CGM-01",
      activateTime: DateTime.now().subtract(const Duration(days: 10)),
      iconPath: DNUAssets.to.images.data.cgm,
      dataCount: 100,
    );

    _loadData();
    // _setChartTimeRange();
    //饮食摄入与运动消耗
    tabs = const [
      Tab(text: "近1周"),
      Tab(text: "近1月"),
      Tab(text: "近3月"),
    ];
    tabController = TabController(length: tabs.length, vsync: this);
    tabController.addListener(() {
      calorLoadData(tabController.index);
    });
    calorLoadData(tabController.index);
    _agpLoadData();
  }

  @override
  void onClose() {
    glucoseUnitStream.cancel();

    super.onClose();
  }

  void _loadData() {
    glucoseEndTime.value = DateTime.now().startOfDay().addDay(1);
    glucoseStartTime.value = DateTime.now().startOfDay().addDay(-7);
    glucoseXAxisInterval.value = Duration.millisecondsPerDay * 1;

    _calculateMiniMax();
  }

  PointTarget get pointTarget {
    PointTarget old = GlucoseService.to.currentDataTarget.value.getAllRange();

    return PointTarget(
      DataGlucose.displayValue(
        old.mini,
        UserConfigService.to.glucoseUnit.value,
      ),
      DataGlucose.displayValue(old.max, UserConfigService.to.glucoseUnit.value),
    );
  }

  void _calculateMiniMax() {
    PointTarget pointTargetInner =
        GlucoseService.to.currentDataTarget.value.getAllRange();

    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;

    double miniY = pointTargetInner.mini;
    double maxY = pointTargetInner.max;

    if (glucoseData.isEmpty) {
      double distance = (maxY - miniY).ceilToDouble();
      if (distance < 2) {
        distance = 3;
      }

      double rangeLow = (miniY - distance / 2).floorToDouble();
      if (rangeLow < 0) {
        rangeLow = 0;
      }
      double rangeHigh = (maxY + distance / 2).ceilToDouble();
      if (rangeHigh > DataGlucose.maxValue) {
        rangeHigh = DataGlucose.maxValue;
      }

      miniY = rangeLow;
      maxY = rangeHigh;
    } else {
      for (var item in glucoseData) {
        if (item.mmolValue < miniY) {
          miniY = item.mmolValue;
        }
        if (item.mmolValue > maxY) {
          maxY = item.mmolValue;
        }
      }

      miniY = miniY.floorToDouble() - 2;
      if (miniY < 0) {
        miniY = 0;
      }

      maxY = maxY.ceilToDouble() + 2;
      if (maxY > DataGlucose.maxValue) {
        maxY = DataGlucose.maxValue;
      }
    }
    if (glucoseUnit == GlucoseUnit.mg) {
      miniY = DataGlucose.displayValue(miniY, glucoseUnit);
      miniY = miniY.floorToDouble();

      maxY = DataGlucose.displayValue(maxY, glucoseUnit);
      maxY = maxY.ceilToDouble();

      glucoseYAxisMin.value = miniY;
      glucoseYAxisMax.value = maxY;

      double interval = ((((maxY - miniY) / 5) * 10) / 10).ceilToDouble();
      if (interval < 1) {
        interval = 1;
      }

      glucoseYAxisInterval.value = interval;
    } else {
      glucoseYAxisMin.value = miniY;
      glucoseYAxisMax.value = maxY;

      double interval = ((maxY - miniY) / 5).floorToDouble();
      if (interval < 1) {
        interval = 1;
      }

      glucoseYAxisInterval.value = interval;
    }

    Log.i("glucoseYAxisInterval: ${glucoseYAxisInterval.value}");
  }

  void _agpLoadData() {
    DateTime now = DateTime.now();
    agpglucoseEndTime.value = DateTime(now.year, now.month, now.day, 23, 59);
    agpglucoseStartTime.value = DateTime(now.year, now.month, now.day, 0, 0);
  }

// 饮食摄入与运动消耗
  void _loadCalorieData() {}

  void calorLoadData(int index) async {
    DateTime now = DateTime.now();
    DateTime calorStartTime;
    DateTime calorEndTime;

    if (index == 0) {
      // 近一周7天的时间范围
      calorStartTime = now.subtract(Duration(days: 6));
      calorEndTime = now;
      calorXAxisInterval.value = Duration.millisecondsPerDay * 1; // X轴间隔为一天
    } else if (index == 1) {
      // 近一个月的时间范围
      calorStartTime = now.subtract(Duration(days: 30));
      calorEndTime = now;
      calorXAxisInterval.value = Duration.millisecondsPerDay * 7; // X轴间隔为一周
    } else if (index == 2) {
      // 近3个月的时间范围
      calorStartTime = now.subtract(Duration(days: 90));
      calorEndTime = now;
      calorXAxisInterval.value = Duration.millisecondsPerDay * 30; // X轴间隔为1个月
    } else {
      // 默认值
      calorStartTime = now.subtract(Duration(days: 7));
      calorEndTime = now;
      calorXAxisInterval.value = Duration.millisecondsPerDay * 1;
    }

    // 更新时间范围和X轴间隔
    this.calorStartTime.value = calorStartTime;
    this.calorEndTime.value = calorEndTime;
    Log.i("calorStartTime:${this.calorStartTime.value}");
    // _calorcalorcalculateMiniMax();
  }

  void _calorcalorcalculateMiniMax() {
    //计算饮食摄入与运动最小值最大值
  }
}
