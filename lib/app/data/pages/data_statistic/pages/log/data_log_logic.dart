/// FileName: data_log_logic
///
/// @Author: ygc
/// @Date: 2024/8/13 17:39
/// @Description:
import 'dart:async';
import 'dart:ui';

import 'package:dnurse/app/data/entity/Data.dart';
import 'package:dnurse/app/data/entity/data_log.dart';
import 'package:dnurse/app/data/service/data_log_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../bootstrap/app.dart';
import '../../../../../../framework/service/event_service.dart';
import '../../../../../../framework/utils/log.dart';
import '../../../../../../ui/widgets/page/loading_page.dart';
import '../../../../../user/event/login_changed.dart';
import '../../../../convertor/glucose/data_convertor.dart';
import '../../../../db/model/glucose/glucose_ua_value_model.dart';
import '../../../../event/data_changed.dart';
import '../../../../service/data_service.dart';
import '../data_statistic_base_page.dart';

const String dataStatisticLog = 'dataStatisticLog';

// 实现数据项展开收起功能的视图模型
class DataLogViewModel {
  final DateTime dateTime;
  final List<Data> data;
  bool isExpanded;

  DataLogViewModel({
    required this.dateTime,
    required this.data,
    this.isExpanded = true,
  });

  factory DataLogViewModel.fromDataLog(DataLog log) {
    return DataLogViewModel(
      dateTime: log.dateTime,
      data: log.data,
      isExpanded: true,
    );
  }

  String get day {
    return dateTime.formatDate(DateFormat.ABBR_MONTH_DAY);
  }
}

class DataLogLogic extends DataStatisticBasePageLogic {
  final items = <DataLogViewModel>[].obs;
  bool showLatestData = true;
  late RefreshController refreshController;
  late StreamSubscription dataChangedSubscription;
  late StreamSubscription loginChangedSubscription;

  final pageState = LoadingPageState.loading.obs;
  final rangeStart = Rx<DateTime?>(null);
  final rangeEnd = Rx<DateTime?>(null);
  final endTime = DateTime.now().startOfDay().addDay(1).obs;
  final startTime =
      DateTime.now().subtract(const Duration(days: 30)).startOfDay().obs;
  final focusDay = Rx<DateTime>(DateTime.now());
  final lastDay = Rx<DateTime>(DateTime.now());
  final Map<DateTime, int> dateCount = {};
  final showAll = true.obs;

  bool get hasSelectedDate =>
      rangeStart.value != null && rangeEnd.value != null;

  String get rangeButtonTitle {
    if (startTime.value == null || rangeEnd.value == null) {
      return AppContext.to.getLocale().all;
    }

    String start = startTime.value.formatDate("M.dd");
    String end = rangeEnd.value!.formatDate("M.dd");
    return "$start-$end";
  }

  @override
  void onInit() {
    super.onInit();

    refreshController = RefreshController(initialRefresh: false);
    _loadData(refresh: true);

    dataChangedSubscription =
        EventService.to.bus.on<DataChanged>().listen(_handleDataChanged);
    loginChangedSubscription =
        EventService.to.bus.on<LoginChanged>().listen(_handleLoginChanged);
  }

  @override
  void dispose() {
    dataChangedSubscription.cancel();
    loginChangedSubscription.cancel();
    refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadData({bool refresh = false}) async {
    // List<GlucoseUAValueModel> logData = await GlucoseProvider.instance.queryUserDataList(AppContext.to.userSn);
    DateTime end = endTime.value;
    DateTime start = startTime.value;
    List<DataLogViewModel> loadData = [];

    // List<GlucoseUAValueModel> logData =
    //     await DataService.to.loadDataList(AppContext.to.userSn, start, end);
    List<Data> logData = await DataLogService.to.queryDataLog(AppContext.to.userSn, start, end);

    DateTime? day;
    List<Data> dayData = [];

    for (final item in logData) {
      // Data d = GlucoseDataConvertor.toEntity(item);
      day ??= item.time.startOfDay();

      if (day.millisecondsSinceEpoch ==
          item.time.startOfDay().millisecondsSinceEpoch) {
        dayData.add(item);
      } else {
        loadData.add(DataLogViewModel(
          dateTime: day,
          data: dayData,
          isExpanded: true,
        ));

        day = item.time.startOfDay();
        dayData = [item];
      }
    }

    if (dayData.isNotEmpty) {
      loadData.add(DataLogViewModel(
        dateTime: day!,
        data: dayData,
        isExpanded: true,
      ));
    }

    if (logData.isEmpty) {
      Log.d('日志没加载到数据');
      if (refresh) {
        pageState.value = LoadingPageState.empty;
      }
      return;
    } else {
      if (refresh) {
        items.clear();
      }
      items.addAll(loadData);
    }

    pageState.value = LoadingPageState.success;
  }

  void onRefresh() async {
    endTime.value = DateTime.now();
    startTime.value =
        endTime.value.subtract(const Duration(days: 30)).startOfDay();
    showLatestData = true;
    await _loadData(refresh: true);
    refreshController.refreshCompleted();
  }

  void onLoading() async {
    endTime.value = startTime.value;
    startTime.value = endTime.value.subtract(const Duration(days: 30));
    showLatestData = false;

    await _loadData();

    refreshController.loadComplete();
  }

  void _handleDataChanged(DataChanged dataChanged) async {
    Log.d('日志页面监听数据变化');

    if (items.isEmpty) {
      _loadData(refresh: true);
    }

    if (dataChanged.operation == DataOperation.update ||
        dataChanged.operation == DataOperation.deleted) {
      int i = 0;
      for (DataLogViewModel dayLog in items) {
        int j = 0;
        for (Data d in dayLog.data) {
          if (dataChanged.data.did == d.did) {
            List<Data> dataList = dayLog.data;
            if (dataChanged.operation == DataOperation.update) {
              dataList[j] = dataChanged.data;
            } else {
              dataList.removeAt(j);
            }
            if (dataList.isEmpty) {
              items.removeAt(i);
            } else {
              DataLogViewModel newLog =
                  DataLogViewModel(dateTime: dayLog.dateTime, data: dataList);
              List<DataLogViewModel> list = List.of(items, growable: true);
              list[i] = newLog;
              items.value = list;
            }
            return;
          }
          j++;
        }

        i++;
      }
    } else {
      int i = 0;
      DateTime dataDateTime = dataChanged.data.time.startOfDay();
      bool inserted = false;
      for (DataLogViewModel dayLog in items) {
        int j = 0;
        if (dataDateTime.compareTo(dayLog.dateTime) == 0) {
          List<Data> dataList = dayLog.data;
          for (Data d in dayLog.data) {
            if (dataChanged.data.time.compareTo(d.time) == 1) {
              dataList.insert(j, dataChanged.data);
              inserted = true;
              break;
            }
            j++;
          }
          if (!inserted) {
            inserted = true;
            dataList.add(dataChanged.data);
          }
          DataLogViewModel newLog =
              DataLogViewModel(dateTime: dayLog.dateTime, data: dataList);
          List<DataLogViewModel> list = List.of(items, growable: true);
          list[i] = newLog;
          items.value = list;
        } else if (dataDateTime.compareTo(dayLog.dateTime) == 1) {
          List<Data> dataList = [dataChanged.data];
          DataLogViewModel newLog =
              DataLogViewModel(dateTime: dataDateTime, data: dataList);
          inserted = true;
          List<DataLogViewModel> list = List.of(items, growable: true);
          list.insert(i, newLog);
          items.value = list;
        }

        if (inserted) {
          break;
        }

        i++;
      }
      if (!inserted) {
        if (dataDateTime.compareTo(startTime.value) == 1) {
          List<Data> dataList = [dataChanged.data];
          DataLogViewModel newLog =
              DataLogViewModel(dateTime: dataDateTime, data: dataList);
          inserted = true;
          items.add(newLog);
        }
      }

      if (items.isNotEmpty && pageState.value == LoadingPageState.empty) {
        pageState.value = LoadingPageState.success;
      }
    }
  }

  void _handleLoginChanged(LoginChanged event) async {
    onRefresh();
  }

  void removeData(int groupIndex, int itemIndex) {
    Data item = items[groupIndex].data[itemIndex];
    DataService.to.deleteData(item, AppContext.to.userSn);

    List<DataLogViewModel> list = List.of(items, growable: true);
    list[groupIndex].data.removeAt(itemIndex);
    if (list[groupIndex].data.isEmpty) {
      list.removeAt(groupIndex);
    }

    items.value = list;

    if (items.isEmpty) {
      pageState.value = LoadingPageState.empty;
    }
  }

  // void switchRangeData() {
  //   // startTime.value = rangeStart.value;
  //   // endTime.value = rangeEnd.value;
  //   showAll.value = false;
  //   _loadData(refresh: true);
  // }
  //
  // void switchToAllData() {
  //   // startTime.value = null;
  //   // endTime.value = null;
  //   showAll.value = true;
  //   _loadData(refresh: true);
  // }

  Future loadDayDataCount() async {
    dateCount.clear();

    DateTime monthStart = focusDay.value;
    monthStart = monthStart.startOfMonth();

    DateTime startTime = monthStart.startOfMonth();
    DateTime endTime = startTime.addMonth(1);

    // await checkTask(startTime, endTime);

    Map<DateTime, int> result = await DataService.to.getDataCountByDay(
        AppContext.to.userId,
        startTime.millisecondsSinceEpoch,
        endTime.millisecondsSinceEpoch);
    if (result.isNotEmpty) {
      dateCount.addAll(result);
    }

    lastDay.value = DateTime.now();
  }

  @override
  Color getBackgroundColor() {
    return Colors.white;
  }

  @override
  bool getShowBackgroundGradient() {
    return true;
  }
}
