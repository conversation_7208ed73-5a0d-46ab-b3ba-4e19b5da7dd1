/// FileName: data_log_page
///
/// @Author: ygc
/// @Date: 2024/8/13 17:39
/// @Description:
import 'package:dnurse/app/data/service/data_log_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/theme.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../bootstrap/app.dart';
import '../../../../../../resource/generated/l10n.dart';
import '../../../../../../ui/style/color.dart';
import '../../../../../../ui/style/font.dart';
import '../../../../../../ui/widgets/button/dnu_outline_button.dart';
import '../../../../../../ui/widgets/button/dnu_primary_button.dart';
import '../../../../../../ui/widgets/calender_view.dart';
import '../../../../../../ui/widgets/page/loading_page.dart';
import '../../../../../../ui/widgets/sheet_view/sheet_view.dart';
import '../../../../../../ui/widgets/slide_view.dart';
import '../../../../entity/Data.dart';
import 'data_log_logic.dart';

class DataLogPage extends StatelessWidget {
  const DataLogPage({
    super.key,
    this.tag,
    this.safeTop = false,
  });

  final String? tag;
  final bool safeTop;

  DataLogLogic get logic => GetInstance().find<DataLogLogic>(tag: tag);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return LoadingPage(
        pageState: logic.pageState.value,
        emptyTitle: "无数据",
        emptyContent: "",
        child: _buildLogContent(context),
      );
    });
  }

  Widget _buildLogContent(BuildContext context) {
    return Column(children: [
      Expanded(
        child: _buildList(context),
      ),
      _buildButton(context),
    ]);
  }

  Widget _buildList(BuildContext context) {
    return SmartRefresher(
      enablePullDown: true,
      enablePullUp: true,
      header: const WaterDropHeader(),
      footer: const ClassicFooter(
        loadStyle: LoadStyle.ShowWhenLoading,
      ),
      controller: logic.refreshController,
      onRefresh: () => logic.onRefresh(),
      onLoading: () => logic.onLoading(),
      child: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: EdgeInsets.only(
              top: 42.w + (!safeTop ? 0 : MediaQuery.of(context).padding.top),
              left: 20.w,
              right: 20.w,
              bottom:
                  10.w + (!safeTop ? 0 : MediaQuery.of(context).padding.bottom),
            ),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (content, index) {
                  return _buildDayItem(index);
                },
                childCount: logic.items.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayItem(int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.w),
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.w),
        border: Border.all(
          color: const Color(0xFFEEE6FA),
          width: 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  logic.items[index].day,
                  style: TextStyle(
                    fontFamily: AppFont.rany,
                    color: AppColor.textSecondary,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    height: 24 / 18,
                  ),
                ),
              ),
              Text(
                '共${logic.items[index].data.length}条数据',
                style: TextStyle(
                  color: AppColor.textDesc,
                  fontFamily: AppFont.rany,
                  fontSize: 14.sp,
                  height: 20 / 12,
                ),
              ),
              SizedBox(width: 8.w),
              InkWell(
                onTap: () {
                  logic.items[index].isExpanded =
                      !logic.items[index].isExpanded;
                  logic.items.refresh(); // 触发 Obx 更新
                },
                child: AnimatedRotation(
                  turns: logic.items[index].isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    DNUIconFont.sanjiao01,
                    color: AppColor.textDesc,
                    size: 14.w,
                  ),
                ),
              ),
            ],
          ),
          if (logic.items[index].isExpanded) _buildGroupItems(index)
        ],
      ),
    );
  }

  Widget _buildGroupItems(int index) {
    List<Widget> children = [];
    int cur = 0;
    for (Data data in logic.items[index].data) {
      children.add(
        _buildGroupSlidableItem(index, cur, data),
      );

      cur++;
    }

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 12.w),
          child: Divider(
            color: const Color(0xFFF9F9FE),
            height: 1.w,
          ),
        ),
        ...children
      ],
    );
  }

  Widget _buildGroupSlidableItem(int groupIndex, int itemIndex, Data dataLog) {
    String key = "${groupIndex}_$itemIndex";

    return Container(
      height: 60.w,
      decoration: BoxDecoration(
          border: Border(
        bottom: BorderSide(
          color: const Color(0xFFF9F9FE),
          width: 1.w,
        ),
      )),
      // margin: EdgeInsets.symmetric(vertical: 8.w),
      child: Ink(
        child: InkWell(
          splashColor: AppColor.splashColor,
          borderRadius: BorderRadius.circular(20.w),
          onTap: () {
            // controller.editWeight(index);
            // weightAlert();
            logic.items[groupIndex].data[itemIndex].gotoEdit();
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.w),
            child: SlideView.withDelete(
              key: ValueKey(key),
              onDeletePressed: () {
                _showDeleteDialog(groupIndex, itemIndex);
              },
              child: Container(
                  constraints: BoxConstraints(minHeight: 80.w),
                  child: _buildGroupSlidableItemLayout(dataLog)),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGroupSlidableItemLayout(Data data) {
    return Row(
      children: [
        Image.asset(data.icon(), width: 40.w, height: 40.w),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                data.title(),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.textPrimary,
                  height: 24 / 16,
                ),
              ),
              Text(data.message(),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColor.textDesc,
                    height: 20 / 14,
                    fontFamily: AppFont.rany,
                  )),
            ],
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              data.stringValue(),
              style: TextStyle(
                fontSize: 24.sp,
                color: AppColor.textPrimary,
                fontWeight: FontWeight.w700,
                fontFamily: AppFont.rany,
                height: 28 / 24,
              ),
            ),
            Text(
              data.unit(),
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.textDesc,
                fontFamily: AppFont.rany,
                height: 16 / 14,
              ),
            ),
          ],
        ),
      ],
    );
  }

  _showDeleteDialog(int groupIndex, int itemIndex) {
    DNUDialog.showConfirm(
      title: AppContext.to.getLocale().warning,
      content: AppContext.to.getLocale().dataDelete,
      onConfirm: () {
        logic.removeData(groupIndex, itemIndex);
      },
    );
  }

  Widget _buildButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.w),
          topRight: Radius.circular(20.w),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x143947E5),
            offset: Offset(0, 4.w),
            blurRadius: 10.w,
          ),
        ],
      ),
      padding:
          EdgeInsets.only(left: 30.w, right: 30.w, top: 16.w, bottom: 34.w),
      child: Row(
        children: [
          Expanded(
            child: DNUPrimaryButton(
              title: S.of(context).dataExport,
              disable: false,
              iconLeft: Icons.construction,
              // iconIsRight: true,
              onPressed: () {
                // AppRouter.push(Routes.dataExport);
                DateTime endTime = DateTime.now().startOfDay().addDay(1);
                DateTime startTime = endTime.addDay(-7);

                DataLogService.to.queryDataLog(AppContext.to.userSn,startTime, endTime);
              },
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 15.w)),
          // Expanded(
          //   child: Obx(() {
          //     return DNUPrimaryButton(
          //       title: logic.rangeButtonTitle,
          //       disable: false,
          //       iconLeft: Icons.construction,
          //       onPressed: () {
          //         // AppRouter.push(Routes.dataTest);
          //         // changeTime(context);
          //       },
          //     );
          //   }),
          // )
        ],
      ),
    );
  }

  Widget _buildCalenderView(BuildContext context) {
    return Expanded(
      child: Obx(() {
        return CalenderView.defaultCalender(
          shouldFillViewport: true,
          onPageChanged: (DateTime focusedDay) {
            logic.focusDay.value = focusedDay;
            logic.loadDayDataCount();
          },
          lastDay: logic.lastDay.value,
          rangeStartDay: logic.rangeStart.value,
          rangeEndDay: logic.rangeEnd.value,
          onRangeSelected:
              (DateTime? start, DateTime? end, DateTime focusedDay) {
            logic.rangeStart.value = start;
            logic.rangeEnd.value = end;
          },
          enabledDayPredicate: (date) {
            return logic.dateCount.containsKey(date);
          },
          focusedDay: logic.focusDay.value,
        );
      }),
    );
  }

  void changeTime(BuildContext context) {
    logic.loadDayDataCount();

    SheetView.showBottomSheet(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCalenderView(context),
          Padding(padding: EdgeInsets.only(top: 10.w)),
          Obx(() {
            return SheetView.button(
              onOk: logic.hasSelectedDate
                  ? () {
                      // logic.switchRangeData();
                      Get.back();
                    }
                  : null,
              onCancel: () {
                Get.back();
              },
            );
          }),
          Padding(padding: EdgeInsets.only(top: 10.w)),
          TextButton(
            onPressed: () {
              // logic.switchToAllData();
              Get.back();
            },
            child: Text(
              AppContext.to.getLocale().dataShowAll,
              style: TextStyle(
                color: AppColor.primary,
                fontSize: 17.sp,
                height: 1.4,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Padding(padding: EdgeInsets.only(top: 10.w)),
        ],
      ),
      height: 550.w,
      isScrollControlled: true,
    );
  }
}
