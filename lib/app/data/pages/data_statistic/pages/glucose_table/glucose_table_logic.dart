/// FileName: glucose_table_logic
///
/// @Author: ygc
/// @Date: 2024/8/13 17:42
/// @Description:
import 'dart:async';

import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../bootstrap/app.dart';
import '../../../../../../framework/service/event_service.dart';
import '../../../../../../framework/utils/log.dart';
import '../../../../../user/event/login_changed.dart';
import '../../../../common/time_point.dart';
import '../../../../convertor/glucose/data_convertor.dart';
import '../../../../db/model/glucose/glucose_ua_provider.dart';
import '../../../../db/model/glucose/glucose_ua_value_model.dart';
import '../../../../entity/Data.dart';
import '../../../../entity/data_type.dart';
import '../../../../event/data_changed.dart';
import '../../../../widget/table/data_table_row_widget.dart';
import '../data_statistic_base_page.dart';

const String dataStatisticGlucoseTable = 'dataStatisticGlucoseTable';

class GlucoseTableLogic extends DataStatisticBasePageLogic {
  late RefreshController refreshController;
  final int _page = 1;
  final int _pageSize = 20;
  final items = <DataTableRowData>[].obs;
  late StreamSubscription dataChangedSubscription;
  late StreamSubscription loginChangedSubscription;

  @override
  void onInit() {
    super.onInit();
    _initItems();
    refreshController = RefreshController(initialRefresh: false);

    dataChangedSubscription = EventService.to.bus.on<DataChanged>().listen(_handleDataChanged);
    loginChangedSubscription = EventService.to.bus.on<LoginChanged>().listen(_handleLoginChanged);

  }

  @override
  void dispose() {
    loginChangedSubscription.cancel();

    dataChangedSubscription.cancel();
    refreshController.dispose();
    super.dispose();
  }

  void _handleLoginChanged(LoginChanged event) async {
    onRefresh();
  }

  Future<void> _initItems() async {
    DateTime now = DateTime.now();
    List<DataTableRowData> dataList = await _loadTableData(AppContext.to.userSn, now.addMonth(-1).startOfDay(), now);
    items.clear();
    items.addAll(dataList);
  }

  void onRefresh() async {
    // await Future.delayed(const Duration(seconds: 1));
    await _initItems();
    refreshController.refreshCompleted();
  }

  void onLoading() async {
    // await _loadData();

    // await Future.delayed(const Duration(milliseconds: 200));

    DataTableRowData lastItem = items.last;
    DateTime end = lastItem.dateTime;
    List<DataTableRowData> dataList = await _loadTableData(AppContext.to.userSn, end.addMonth(-1).startOfDay(), end);
    items.addAll(dataList);

    refreshController.loadComplete();
  }

  Future<List<DataTableRowData>> _loadTableData(String sn, DateTime start, DateTime end) async {

    if (start.compareTo(end) != -1) {   //起始时间不能大于结束时间
      return [];
    }

    List<DataTableRowData> tableData = [];

    List<GlucoseUAValueModel> dataList = await GlucoseProvider.instance.queryUserDataList(AppContext.to.userSn, start: start, end: end, dataType: DataType.glucose);
    DateTime day = end.startOfDay();
    int index = 0;
    Map<TimePoint, Data> dayData = {};
    while(day.compareTo(start.startOfDay()) != 0) {
      for (; index < dataList.length; index ++) {
        GlucoseUAValueModel item = dataList[index];
        Data d = GlucoseDataConvertor.toEntity(item);
        if (day.compareTo(d.time.startOfDay()) == 0) {
          if (dayData[d.getTimePoint()] == null) {  //数据倒序排列，前面是最新数据，所以某个时间段有值之后不再重新赋值
            dayData[d.getTimePoint()] = d;
          }
        } else {
          break;
        }
      }

      tableData.add(DataTableRowData(dateTime: day, values: dayData));
      dayData = {};

      day = day.addDay(-1);
    }

    // tableData.add(DataTableRowData(dateTime: day, values: dayData));

    return tableData;
  }

  void _handleDataChanged(DataChanged dataChanged) async {
    Log.d('血糖表格页面监听数据变化');
    if (dataChanged.operation == DataOperation.update || dataChanged.operation == DataOperation.deleted) {
      int i = 0;
      for (DataTableRowData rowData in items) {
        int j = 0;
        if (dataChanged.data.time.startOfDay().compareTo(rowData.dateTime) == 0) {
          for (TimePoint tp in rowData.values.keys) {
            if (dataChanged.data.getTimePoint().id == tp.id) {
              Map<TimePoint, Data> values = rowData.values;
              values[tp] = dataChanged.data;
              DataTableRowData newRowData = DataTableRowData(dateTime: dataChanged.data.time.startOfDay(), values: values);
              List<DataTableRowData> list = List.of(items, growable: true);
              list[i] = newRowData;
              items.value = list;
              return;
            }
            j ++;
          }
        }

        i ++;
      }

    }
    // else {
    //   int i = 0;
    //   DateTime dataDateTime = dataChanged.data.time.startOfDay();
    //   bool inserted = false;
    //   for (DataLog dayLog in items) {
    //     int j = 0;
    //     if (dataDateTime.compareTo(dayLog.dateTime) == 0) {
    //       List<Data> dataList = dayLog.data;
    //       for (Data d in dayLog.data) {
    //         if (dataChanged.data.time.compareTo(d.time) == 1) {
    //           dataList.insert(j, dataChanged.data);
    //           inserted = true;
    //           break;
    //         }
    //         j ++;
    //       }
    //       if (!inserted) {
    //         inserted = true;
    //         dataList.add(dataChanged.data);
    //       }
    //       DataLog newLog = DataLog(dateTime: dayLog.dateTime, data: dataList);
    //       List<DataLog> list = List.of(items, growable: true);
    //       list[i] = newLog;
    //       items.value = list;
    //
    //     } else if (dataDateTime.compareTo(dayLog.dateTime) == 1) {
    //       List<Data> dataList = [dataChanged.data];
    //       DataLog newLog = DataLog(dateTime: dataDateTime, data: dataList);
    //       inserted = true;
    //       List<DataLog> list = List.of(items, growable: true);
    //       list.insert(i, newLog);
    //       items.value = list;
    //     }
    //
    //     if (inserted) {
    //       break;
    //     }
    //
    //     i ++;
    //   }
    //   if (!inserted) {
    //     if (dataDateTime.compareTo(startTime.value) == 1) {
    //       List<Data> dataList = [dataChanged.data];
    //       DataLog newLog = DataLog(dateTime: dataDateTime, data: dataList);
    //       inserted = true;
    //       items.add(newLog);
    //     }
    //   }
    // }
  }
}
