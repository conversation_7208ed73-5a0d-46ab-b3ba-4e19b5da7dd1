import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../framework/utils/log.dart';
import '../../../../common/time_point.dart';
import '../../../../widget/table/data_table_head_widget.dart';
import '../../../../widget/table/data_table_row_widget.dart';
import 'glucose_table_logic.dart';

class GlucoseTablePage extends StatelessWidget {
  const GlucoseTablePage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<GlucoseTableLogic>();
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          // SizedBox(height: 20.w),
          DataTableHeadWidget(topPadding: 20.w,),
          Expanded(
            child: Obx(() {
              Log.i("message");
              return SmartRefresher(
                enablePullDown: true,
                enablePullUp: true,
                controller: logic.refreshController,
                onRefresh: () {
                  logic.onRefresh();
                },
                onLoading: () {
                  logic.onLoading();
                },
                child: CustomScrollView(
                  slivers: [
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                            (c, i) {
                          return _buildItem(c, i);
                        },
                        childCount: logic.items.length,
                      ),
                    )
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(BuildContext context, int index) {
    final logic = Get.find<GlucoseTableLogic>();

    return Obx(() {
      DataTableRowData glucoseDataItem = logic.items[index];
      DataTableItem item = DataTableItem.fromRowData(glucoseDataItem);
      DateTime nextDay = item.dateTime.addDay(-1);
      return DataTableRowWidget(
        separate: nextDay.month != item.dateTime.month,
        value: item,
        highlight: index %2 == 1,
        onTap: (DateTime dateTime, TimePoint timePoint, dynamic tag) {
          Log.d("DataTableRowWidget: $dateTime, $timePoint, $tag");
          if (tag != null && tag.runtimeType == DataGlucose) {
            (tag as DataGlucose).gotoEdit();
          }
        },
      );
    });
  }
}
