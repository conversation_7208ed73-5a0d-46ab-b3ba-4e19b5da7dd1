import 'package:bruno/bruno.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:dnurse/ui/widgets/page/custom_head_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../ui/style/color.dart';
import 'logic.dart';

class DataStatisticPage extends StatelessWidget {
  const DataStatisticPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<DataStatisticLogic>();

    return Obx(() {
      return CustomHeadScaffold(
        backgroundColor: logic.backgroundColor.value,
        showBackgroundGradient: logic.showBackgroundGradient.value,
        appBar: getAppBar(
          context: context,
          backgroundColor: Colors.white,
          title: "数据详情",
          elevation: 0,
        ),
        body: Container(
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 38.w),
                child: Column(
                  children: [
                    // SizedBox(height: 38.w),
                    Expanded(
                      child: PageView(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: logic.pageController,
                        children: logic.pages.map((e) => e.page()).toList(),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTab(context),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTab(BuildContext context) {
    final logic = Get.find<DataStatisticLogic>();
    return Container(
      width: double.infinity,
      height: 58.w,
      padding: const EdgeInsets.only(
        top: 4,
        bottom: 10,
        left: 20,
        right: 20,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20.w),
          bottomRight: Radius.circular(20.w),
        ),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0xF3947E5),
            offset: Offset(0, 4.w),
            blurRadius: 10.w,
          ),
        ],
      ),
      child: BrnTabBar(
        indicatorColor: AppColor.primary,
        indicatorWeight: 4.w,
        indicatorWidth: 20,
        labelColor: AppColor.textPrimary,
        labelStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelColor: AppColor.textPrimary,
        unselectedLabelStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w400,
        ),
        labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
        controller: logic.tabController,
        tabs: logic.tabs,
        backgroundcolor: Colors.transparent,
        mode: BrnTabBarBadgeMode.origin,
        onTap: (state, index) {
          logic.onTabChanged(context, index);
        },
      ),
    );
  }
}
