/// FileName: drug_amount_select_sheet
///
/// @Author: ygc
/// @Date: 2025/7/9 18:34
/// @Description:
import 'package:dnurse/app/user/entity/drug_type.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:wheel_slider/wheel_slider.dart';

class DrugAmountSelectSheet extends StatefulWidget {
  const DrugAmountSelectSheet({
    super.key,
    this.drugName,
    this.drugType = DrugType.hypoglycemic,
    this.amount = 0,
    this.onConfirm,
  });

  final String? drugName;
  final DrugType drugType;
  final num amount;
  final ValueChanged<num>? onConfirm;

  static void show(BuildContext context,
      {String? drugName,
      DrugType drugType = DrugType.hypoglycemic,
      num amount = 0,
      ValueChanged<num>? onConfirm}) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return DrugAmountSelectSheet(
          drugName: drugName,
          drugType: drugType,
          amount: amount,
          onConfirm: onConfirm,
        );
      },
    );
  }

  @override
  State<DrugAmountSelectSheet> createState() => _DrugAmountSelectSheetState();
}

class _DrugAmountSelectSheetState extends State<DrugAmountSelectSheet> {
  int _currentIndex = 0;
  final List<num> options = [];

  @override
  void initState() {
    super.initState();
    // _amount = widget.dataDrug.amount;
    if (widget.drugType == DrugType.insulin) {
      for (int i = 0; i < 32; i++) {
        options.add(i + 1);
      }
    } else if (widget.drugType == DrugType.glp_1) {
      for (num i = 0; i < 99; i += 0.5) {
        options.add(i + 0.5);
      }
    } else {
      for (num i = 0; i < 33; i += 0.5) {
        options.add(i + 0.5);
      }
    }

    if (widget.amount == 0) {
      if (widget.drugType != DrugType.insulin) {
        _currentIndex = 1;
      }
    } else {
      for (int i = 0; i < options.length; i++) {
        if (options[i] == widget.amount) {
          _currentIndex = i;
          break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        left: 20.w,
        right: 20.w,
        top: 12.w,
        bottom: 12.w + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.w),
          topRight: Radius.circular(20.w),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 20.w, top: 10.w),
            child: Text(
              widget.drugName ?? 'Drug',
              style: TextStyle(
                fontSize: 16.w,
                color: AppColor.textPrimary,
              ),
            ),
          ),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          SizedBox(
              height: 48.w,
              child: Center(
                child: Text(
                  '${options[_currentIndex].toStringAsFixed(1)} ${widget.drugType.unit}',
                  style:
                      TextStyle(fontSize: 16.sp, color: AppColor.textSecondary),
                ),
              )),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          _buildUnitSlider(context),
          Divider(
            height: 1.w,
            color: AppColor.lineColor,
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.w, bottom: 8.w),
            child: Text(
              widget.drugType.unit,
              style: TextStyle(fontSize: 16.w, color: AppColor.textSecondary),
            ),
          ),
          SizedBox(height: 10.w),
          DNUPrimaryButton(
            width: 335.w,
            height: 48.w,
            gradient: AppColor.primaryGradientHorizontal1,
            child: Center(
              child: Text(
                '选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
              widget.onConfirm?.call(options[_currentIndex]);
            },
          )
        ],
      ),
    );
  }

  Widget _buildItemCard({
    Widget? child,
    double? marginTop,
  }) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        top: marginTop ?? 0,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 10.w,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8FE),
        borderRadius: BorderRadius.circular(20.w),
        border: Border.all(
          color: AppColor.tableBorder,
          width: 1.w,
        ),
      ),
      child: child,
    );
  }

  Widget _buildUnitSlider(BuildContext context) {
    TextStyle itemTextStyle = TextStyle(
      fontSize: 16.sp,
      color: AppColor.textSecondary,
    );
    TextStyle selectedItemTextStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColor.primary,
      fontWeight: FontWeight.w500,
    );

    return Container(
      constraints: BoxConstraints(minHeight: 58.w),
      child: WheelSlider.customWidget(
        totalCount: options.length,
        initValue: _currentIndex,
        isInfinite: false,
        scrollPhysics: const BouncingScrollPhysics(),
        itemSize: 60.w,
        enableAnimation: false,
        pointerColor: Colors.transparent,
        onValueChanged: (value) {
          _currentIndex = value;
          setState(() {});
        },
        children: List.generate(
          options.length,
          (index) => Center(
            child: Center(
              child: Text(
                options[index].toStringAsFixed(1),
                style: _currentIndex == index
                    ? selectedItemTextStyle
                    : itemTextStyle,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
