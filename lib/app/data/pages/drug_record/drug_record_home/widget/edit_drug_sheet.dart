/// FileName: edit_drug_sheet
///
/// @Author: ygc
/// @Date: 2025/7/9 17:32
/// @Description:
import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/entity/data_drug.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import 'drug_amount_select_sheet.dart';

class EditDrugSheet extends StatefulWidget {
  const EditDrugSheet({
    super.key,
    required this.dataDrug,
    required this.onConfirm,
  });

  final DataDrug dataDrug;
  final ValueChanged<DataDrug>? onConfirm;

  static void show(BuildContext context, DataDrug dataDrug,
      ValueChanged<DataDrug>? onConfirm) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return EditDrugSheet(
          dataDrug: dataDrug,
          onConfirm: onConfirm,
        );
      },
    );
  }

  @override
  State<EditDrugSheet> createState() => _EditDrugSheetState();
}

class _EditDrugSheetState extends State<EditDrugSheet> {
  late DateTime _dateTime;
  late num _amount;

  @override
  void initState() {
    super.initState();
    _dateTime = widget.dataDrug.time;
    _amount = widget.dataDrug.amount;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 20.w,
        right: 20.w,
        top: 20.w,
        bottom: 20.w + MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Text(
          '修改用药记录',
          style: TextStyle(
            fontSize: 20.sp,
            color: AppColor.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        _buildItemCard(
          marginTop: 10.w,
          child: Text(
            widget.dataDrug.name,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColor.textPrimary,
            ),
          ),
        ),
        _buildItemCard(
          marginTop: 10.w,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  "用药时间",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textPrimary,
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  DateSheetView.show(
                    context: context,
                    title: '请选择用药时间',
                    initialDateTime: _dateTime,
                    pickerMode: BrnDateTimePickerMode.time,
                    dateFormat: "HH:mm",
                    // 设置时间格式
                    onConfirm: (dateTime, selectedIndex) {
                      DateTime newDateTime = DateTime(
                        _dateTime.year,
                        _dateTime.month,
                        _dateTime.day,
                        dateTime.hour,
                        dateTime.minute,
                      );

                      if (newDateTime.isAfter(DateTime.now())) {
                        DNUToast.show(
                          '不能选择未来的时间',
                          context,
                        );
                        return;
                      }

                      setState(() {
                        _dateTime = newDateTime;
                      });
                    },
                  );
                },
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  Text(
                    _dateTime.formatDate("yyyy-MM-dd HH:mm"),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.primary,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_right,
                    size: 20.w,
                    color: AppColor.primary,
                  ),
                ]),
              ),
            ],
          ),
        ),
        _buildItemCard(
          marginTop: 10.w,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  "本次药量",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textPrimary,
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  DrugAmountSelectSheet.show(context,
                      drugName: widget.dataDrug.name,
                      drugType: widget.dataDrug.drugType,
                      amount: _amount, onConfirm: (amount) {
                    if (_amount != amount) {
                      setState(() {
                        _amount = amount;
                      });
                    }
                  });
                },
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  Text(
                    "${_amount.toStringAsFixed(1)} ${widget.dataDrug.drugType.unit}",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.primary,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_right,
                    size: 20.w,
                    color: AppColor.primary,
                  ),
                ]),
              )
            ],
          ),
        ),
        SizedBox(
          height: 10.w,
        ),
        DNUPrimaryButton(
          title: "保存",
          onPressed: () {
            Navigator.of(context).pop();
            if (_dateTime != widget.dataDrug.time ||
                _amount != widget.dataDrug.amount) {
              DataDrug newData = widget.dataDrug.copyWith(
                time: _dateTime,
                amount: _amount,
              );
              widget.onConfirm?.call(newData);
            }
          },
        )
      ]),
    );
  }

  Widget _buildItemCard({
    Widget? child,
    double? marginTop,
  }) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        top: marginTop ?? 0,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 10.w,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8FE),
        borderRadius: BorderRadius.circular(20.w),
        border: Border.all(
          color: AppColor.tableBorder,
          width: 1.w,
        ),
      ),
      child: child,
    );
  }
}
