import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/drug_record/drug_record_home/widget/edit_drug_sheet.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/widgets/button/dnu_outline_button.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../../framework/utils/log.dart';
import '../../../../../router/router.dart';
import '../../../../../router/routes.dart';
import '../../../../../ui/style/color.dart';
import '../../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../../ui/widgets/dnu_toast.dart';
import '../../../../../ui/widgets/sheet_view/date_sheet_view.dart';
import '../../../../user/entity/drug_info.dart';
import '../../../../user/entity/drug_item.dart';
import '../../../../user/entity/drug_plan.dart';
import '../../../entity/data_drug.dart';
import '../../../entity/day_drug_info.dart';
import '../../food_record/food_record_first/widget/food_calendar_cell_widget.dart';
import 'logic.dart';

class DrugRecordHomePage extends StatelessWidget {
  const DrugRecordHomePage({super.key});

  DrugRecordHomeLogic get logic => Get.find<DrugRecordHomeLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
        context: context,
        title: '用药记录',
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Center(
            child: InkWell(
              onTap: () {
                AppRouter.push(Routes.drugPlan);
              },
              child: Text(
                '用药方案',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.textSecondary,
                ),
              ),
            ),
          )
        ],
        bottom: PreferredSize(
          preferredSize: Size(Get.width, 100.w),
          child: _getCalendar(),
        ),
      ),
      body: Column(
        children: [
          // _getCalendar(),
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Expanded(
      child: Obx(() {
        if (logic.state.drugPlanList.isEmpty && logic.state.drugList.isEmpty) {
          return _buildTodayPlan(context);
        }

        return SingleChildScrollView(
          controller: logic.scrollController,
          child: Column(
            children: [
              _buildTodayPlan(context),
              _buildUsedDrug(context),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildTodayPlan(BuildContext context) {
    return Obx(() {
      bool isEmpty =
          logic.state.drugPlanList.isEmpty && logic.state.drugList.isEmpty;
      if (!logic.state.isToday.value) {
        return Container();
      }

      Widget? child;
      if (isEmpty) {
        child = _buildTodayEmpty(context);
      } else if (logic.state.todayPlan.isEmpty) {
        child = _buildEmpty(
          context,
          hiddenSetPlan: true,
          message: "本日暂无用药方案",
        );
      } else {
        List<String> addedDrugNames =
            logic.state.drugList.map((e) => e.name).toSet().toList();

        List<Widget> children = <Widget>[];
        int index = 0;
        for (final drugPlan in logic.state.todayPlan) {
          if (drugPlan.drugItems == null || drugPlan.drugItems!.isEmpty) {
            continue;
          }

          for (var drugItem in drugPlan.drugItems!) {
            if (addedDrugNames.contains(drugItem.name)) {
              continue; // Skip if the drug has already been added
            }

            children.add(_buildPlanDrugItem(drugPlan, drugItem,
                marginTop: index == 0 ? 0 : 12.w));
            index++;
          }
        }

        if (children.isEmpty) {
          child = _buildEmpty(
            context,
            hiddenSetPlan: true,
            imagePath: DNUAssets.to.images.common.dataSuccess,
            message: "本日用药方案已全部执行",
          );
        } else {
          child = Column(
            children: children,
          );
        }
      }

      return _buildCard(
        context,
        title: '本日待用药',
        height: isEmpty ? double.infinity : null,
        child: child,
      );
    });
  }

  Widget _buildPlanDrugItem(DrugPlan drugPlan, DrugItem drugItem,
      {double? marginTop}) {
    return Container(
      margin: EdgeInsets.only(top: marginTop ?? 0.w),
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 16.w,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8FE),
        borderRadius: BorderRadius.circular(20.w),
        border: Border.all(
          color: AppColor.tableBorder,
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  drugItem.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColor.textPrimary,
                  ),
                ),
                SizedBox(
                  height: 4.w,
                ),
                Text(
                  "${drugPlan.getTime()} ${drugItem.getAmount()}",
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColor.textDesc,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 16.w,
          ),
          DNUPrimaryButton(
            width: 90.w,
            small: true,
            title: "已用药",
            onPressed: () {
              logic.addPlanDrug(drugPlan, drugItem);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUsedDrug(BuildContext context) {
    return Obx(() {
      bool isEmpty =
          logic.state.drugPlanList.isEmpty && logic.state.drugList.isEmpty;
      if (logic.state.isToday.value && isEmpty) {
        return Container();
      }

      return _buildCard(
        context,
        title: '本日已用药',
        child: logic.state.drugList.isEmpty
            ? _buildEmpty(
                context,
                hiddenSetPlan: true,
                message: "本日暂无用药记录",
              )
            : Column(
                children: [
                  ...List.generate(logic.state.drugList.length, (i) => i)
                      .map((index) => _buildUsedDrugItem(context, index)),
                  Container(
                    margin: EdgeInsets.only(top: 12.w),
                    padding: EdgeInsets.symmetric(horizontal: 60.w),
                    child: _buildAddButton(context),
                  ),
                ],
              ),
      );
    });
  }

  Widget _buildUsedDrugItem(BuildContext context, int index) {
    final DataDrug drug = logic.state.drugList[index];
    return Container(
      margin: EdgeInsets.only(top: index == 0 ? 0 : 20.w),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: [
            SlidableAction(
              onPressed: (v) {
                DNUDialog.showConfirm(
                  title: '是否删除',
                  onConfirm: () {
                    logic.deleteDrug(drug);
                  },
                );
              },
              spacing: 10,
              // padding: EdgeInsets.all(20.w),
              backgroundColor: const Color(0xFFFE4A49),
              foregroundColor: Colors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(10.w),
                bottomRight: Radius.circular(10.w),
              ),
              icon: Icons.delete,
              label: '删除',
            ),
          ],
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 20.w,
            vertical: 16.w,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8FE),
            borderRadius: BorderRadius.circular(20.w),
            border: Border.all(
              color: AppColor.tableBorder,
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      drug.name,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColor.textPrimary,
                      ),
                    ),
                    SizedBox(
                      height: 4.w,
                    ),
                    Text(
                      "${drug.getTimeString()} ${drug.getAmount()}",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColor.textDesc,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
              InkWell(
                onTap: () {
                  _showOptionMenu(context, index);
                },
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    gradient: AppColor.primaryGradientHorizontal1,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.more_horiz,
                    size: 20.w,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOptionMenu(BuildContext context, int index) {
    final DataDrug drug = logic.state.drugList[index];

    List<BrnCommonActionSheetItem> actions = [];
    actions.add(BrnCommonActionSheetItem(
      '编辑',
    ));
    actions.add(BrnCommonActionSheetItem(
      '删除',
    ));

// 展示actionSheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return BrnCommonActionSheet(
          title: "选择操作",
          actions: actions,
          clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
            Future.delayed(const Duration(milliseconds: 1), () {
              if (index == 1) {
                DNUDialog.showConfirm(
                  title: "警告",
                  content: "确定要删除 ${drug.name} 吗？",
                  onConfirm: () {
                    logic.deleteDrug(drug);
                  },
                );
              } else {
                EditDrugSheet.show(context, drug, (newData) {
                  logic.updateDrug(newData);
                });
              }
            });
          },
        );
      },
    );
  }

  Widget _buildEmpty(
    BuildContext context, {
    bool hiddenSetPlan = false,
    String? message,
    String? imagePath,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 69.w),
      child: Column(
        children: [
          Image.asset(
            imagePath ?? DNUAssets.to.images.common.dataEmpty,
            width: 156.w,
            height: 117.w,
            fit: BoxFit.fitWidth,
          ),
          Padding(
            padding: EdgeInsets.only(
              top: 10.w,
              bottom: 40.w,
            ),
            child: Text(
              message ?? "您还没有用药方案",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: AppColor.textDesc,
              ),
            ),
          ),
          if (!hiddenSetPlan)
            DNUPrimaryButton(
              title: "设定用药方案",
              onPressed: () {
                AppRouter.push(Routes.drugPlan);
              },
            ),
          if (!hiddenSetPlan)
            SizedBox(
              height: 16.w,
            ),
          _buildAddButton(context),
          SizedBox(
            height: 16.w,
          ),
        ],
      ),
    );
  }

  Widget _buildTodayEmpty(BuildContext context) {
    return SingleChildScrollView(
      child: _buildEmpty(context),
    );
  }

  Widget _buildAddButton(BuildContext context) {
    return DNUOutlineButton(
      title: "手动记录用药",
      onPressed: () {
        DateSheetView.show(
          context: context,
          title: '请选择用药时间',
          initialDateTime: DateTime.now(),
          pickerMode: BrnDateTimePickerMode.time,
          dateFormat: "HH:mm",
          // 设置时间格式
          onConfirm: (dateTime, selectedIndex) {
            Log.i("手动记录用药: $dateTime");
            DateTime now = logic.state.selectedDay.value.startOfDay();
            DateTime newDateTime = DateTime(
              now.year,
              now.month,
              now.day,
              dateTime.hour,
              dateTime.minute,
            );

            if (newDateTime.isAfter(DateTime.now())) {
              DNUToast.show(
                '不能选择未来的时间',
                context,
              );
              return;
            }

            Future.delayed(const Duration(milliseconds: 1), () {
              AppRouter.push(Routes.drug)?.then((result) {
                if (result != null && result.isNotEmpty) {
                  try {
                    List<DrugInfo> drugList = result as List<DrugInfo>;
                    logic.addDrugByManual(newDateTime, drugList);
                  } catch (e) {
                    print(e);
                    Log.i('add drug error', e);
                  }
                }
              });
            });
          },
        );
      },
    );
  }

  Widget _buildCard(
    BuildContext context, {
    required String title,
    required Widget child,
    double? height,
  }) {
    return Container(
      height: height,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
      margin: EdgeInsets.only(top: 12.w),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              color: AppColor.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
          child
        ],
      ),
    );
  }

  Widget _getCalendar() {
    return Obx(() {
      return Container(
        // color: Colors.white,
        padding: EdgeInsets.symmetric(
          vertical: 10.w,
        ),
        child: TableCalendar(
          focusedDay: logic.state.focusDay.value,
          firstDay: DateTime(2012, 1, 1),
          lastDay: DateTime.now(),
          startingDayOfWeek: StartingDayOfWeek.monday,
          availableCalendarFormats: const {CalendarFormat.week: 'Week'},
          headerVisible: false,
          daysOfWeekVisible: false,
          calendarFormat: CalendarFormat.week,
          rowHeight: 100.w,
          selectedDayPredicate: (day) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            day = day.subtract(timeZoneOffset).toLocal();
            return isSameDay(logic.state.selectedDay.value, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            selectedDay = selectedDay.subtract(timeZoneOffset).toLocal();
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();

            if (!isSameDay(logic.state.selectedDay.value, selectedDay)) {
              logic.state.selectedDay.value = selectedDay.startOfDay();
              logic.state.focusDay.value = focusedDay.startOfDay();
            }

            logic.onSwitchSelectedDate(selectedDay.startOfDay());
          },
          onPageChanged: (focusedDay) {
            var timeZoneOffset = DateTime.now().timeZoneOffset;
            focusedDay = focusedDay.subtract(timeZoneOffset).toLocal();
            logic.state.focusDay.value = focusedDay;
            logic.onPageChanged();
          },
          calendarBuilders:
              CalendarBuilders(selectedBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: true,
            );
          }, todayBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: true,
              selected: false,
            );
          }, disabledBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }, outsideBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }, defaultBuilder: (context, day, focusedDay) {
            return _buildCalendarCellWidget(
              day: day,
              focused: false,
              selected: false,
            );
          }),
        ),
      );
    });
  }

  Widget _buildCalendarCellWidget({
    required DateTime day,
    bool focused = false,
    bool selected = false,
    bool disabled = false,
  }) {
    return Obx(() {
      Color textColor = AppColor.textPrimary;
      if (disabled) {
        textColor = AppColor.textDisable;
      } else {
        if (selected) {
          textColor = AppColor.primary;
        } else {
          textColor = AppColor.textSecondary;
        }
      }

      var timeZoneOffset = DateTime.now().timeZoneOffset;
      DateTime dayLocal = day.subtract(timeZoneOffset).toLocal();

      DayDrugInfo? dayDrugInfo = logic.state.dayDrugInfo[dayLocal];
      Color? statusColor;
      if (dayDrugInfo != null) {
        if (dayDrugInfo.count > 0) {
          statusColor = const Color(0xFFA0D486);
        }
      }

      return FoodCalendarCellWidget(
        day: day,
        focused: focused,
        textColor: textColor,
        statusColor: statusColor,
      );
    });
  }
}
