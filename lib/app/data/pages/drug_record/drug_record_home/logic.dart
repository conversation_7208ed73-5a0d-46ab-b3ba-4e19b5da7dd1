import 'dart:async';

import 'package:dnurse/app/common/event/change_event.dart';
import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/app/data/entity/data_drug.dart';
import 'package:dnurse/app/data/service/drug_service.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../../bootstrap/app.dart';
import '../../../../../framework/service/event_service.dart';
import '../../../../../framework/utils/log.dart';
import '../../../../user/entity/drug_info.dart';
import '../../../../user/entity/drug_item.dart';
import '../../../../user/entity/drug_plan.dart';
import '../../../../user/service/drug_plan_service.dart';
import '../../../entity/day_drug_info.dart';
import 'state.dart';

class DrugRecordHomeLogic extends DNUBaseController {
  final DrugRecordHomeState state = DrugRecordHomeState();
  final ScrollController scrollController = ScrollController();
  late StreamSubscription drugPlanChangedSubscription;

  @override
  void onInit() {
    super.onInit();

    _initSubscription();

    _init();
  }

  @override
  void onClose() {
    _cancelSubscription();

    super.onClose();
  }

  void _initSubscription() {
    drugPlanChangedSubscription = EventService.to.bus
        .on<ChangeEvent<DrugPlan>>()
        .listen(_onDrugPlanChanged);
  }

  void _cancelSubscription() {
    drugPlanChangedSubscription.cancel();
  }

  void _init() async {
    await _loadDrugPlan();
    await _switchTodayPlan(state.selectedDay.value);
    await _loadDrugInfo(state.selectedDay.value);
    await _loadDrugData(state.selectedDay.value);
  }

  void _onDrugPlanChanged(ChangeEvent<DrugPlan> event) async {
    _init();
  }

  Future _loadDrugInfo(DateTime date) async {
    int weekDay = date.weekday;

    DateTime firstDayOfWeek =
        state.focusDay.value.subtract(Duration(days: weekDay - 1));
    firstDayOfWeek = firstDayOfWeek.startOfDay();
    DateTime lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 7));

    for (var i = 0; i < 7; i++) {
      DateTime day = firstDayOfWeek.add(Duration(days: i));
      state.dayDrugInfo[day] = null;
    }

    List<DayDrugInfo> dayDrugList = await DrugService.to.queryDrugCountByDay(
        AppContext.to.userSn, firstDayOfWeek, lastDayOfWeek);
    for (var dayDrugInfo in dayDrugList) {
      state.dayDrugInfo[dayDrugInfo.date] = dayDrugInfo;
      Log.i("======== dayDrugInfo ${dayDrugInfo.date}");
    }

    state.dayDrugInfo.refresh();

    Log.i("======== dayDrugInfo loaded");
  }

  Future<void> _loadDrugPlan() async {
    final uid = AppContext.to.userSn;
    final drugPlans = await DrugPlanService.to.getDrugPlanList(uid);
    state.drugPlanList.clear();
    if (drugPlans.isNotEmpty) {
      state.drugPlanList.addAll(drugPlans);
    }
  }

  Future<void> _switchTodayPlan(DateTime dateTime) async {
    DateTime nextDay = dateTime.add(const Duration(days: 1));

    List<DrugPlan> list = [];

    for (final drugPlan in state.drugPlanList) {
      if (!drugPlan.enable) {
        continue;
      }

      if (drugPlan.repeated != 0) {
        final mask = 1 << (dateTime.weekday - 1);
        if (drugPlan.repeated & mask != 0) {
          list.add(drugPlan);
        }
      } else {
        if (drugPlan.stamp != null &&
            drugPlan.stamp!.isAfter(dateTime) &&
            drugPlan.stamp!.isBefore(nextDay)) {
          list.add(drugPlan);
        }
      }
    }

    state.todayPlan.clear();
    state.todayPlan.addAll(list);
  }

  void onSwitchSelectedDate(DateTime dateTime) async {
    state.isToday.value = isSameDay(dateTime, DateTime.now().startOfDay());

    _switchTodayPlan(dateTime);
    // scrollController.jumpTo(0);
    scrollController.animateTo(0,
        duration: const Duration(milliseconds: 300), curve: Easing.standard);
    await _loadDrugData(state.selectedDay.value);
  }

  Future<void> _loadDrugData(DateTime dateTime) async {
    DateTime nextDay = dateTime.add(const Duration(days: 1));

    List<DataDrug> list = await DrugService.to.getDrugDataByTime(
      AppContext.to.userSn,
      dateTime,
      nextDay,
    );

    state.drugList.clear();
    state.drugList.addAll(list);
  }

  Future<void> addPlanDrug(DrugPlan drugPlan, DrugItem drugItem) async {
    DateTime drugTime = DateTime.now().startOfDay().add(
          Duration(
            hours: drugPlan.hour,
            minutes: drugPlan.minute,
          ),
        );

    DataDrug drug = DataDrug(
      did: DataUtils.generateDidWithPrefix("DR"),
      sn: AppContext.to.userSn,
      time: drugTime,
      name: drugItem.name,
      amount: drugItem.count,
      drugType: drugItem.type,
      isPlan: true,
      fromUser: false,
    );

    await DrugService.to.saveDrug(AppContext.to.userSn, drug);

    await _refreshDrugData();
  }

  Future<void> _refreshDrugData() async {
    await _loadDrugInfo(state.selectedDay.value);

    await _loadDrugData(state.selectedDay.value);

    await _switchTodayPlan(state.selectedDay.value);
  }

  void deleteDrug(DataDrug dataDrug) async {
    await DrugService.to.deleteDrug(AppContext.to.userSn, dataDrug);

    await _refreshDrugData();
  }

  void updateDrug(DataDrug newData) async {
    await DrugService.to.saveDrug(AppContext.to.userSn, newData);

    await _refreshDrugData();
  }

  void addDrugByManual(DateTime dateTime, List<DrugInfo> drugList) async {
    for (final drug in drugList) {
      if (drug.drugCount == null) {
        continue;
      }

      DataDrug drugData = DataDrug(
        did: DataUtils.generateDidWithPrefix("DR"),
        sn: AppContext.to.userSn,
        time: dateTime,
        name: drug.name,
        amount: drug.drugCount!,
        drugType: drug.type,
        isPlan: false,
        fromUser: false,
      );

      await DrugService.to.saveDrug(AppContext.to.userSn, drugData);
    }

    await _refreshDrugData();
  }

  void onPageChanged() {
    _loadDrugInfo(state.focusDay.value);
  }
}
