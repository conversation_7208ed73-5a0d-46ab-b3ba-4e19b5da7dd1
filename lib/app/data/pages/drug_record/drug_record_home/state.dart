import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../../user/entity/drug_plan.dart';
import '../../../entity/data_drug.dart';
import '../../../entity/day_drug_info.dart';

class DrugRecordHomeState {
  DrugRecordHomeState() {
    ///Initialize variables
  }

  final selectedDay = DateTime.now().startOfDay().obs;
  final focusDay = DateTime.now().obs;
  final drugPlanList = <DrugPlan>[].obs;
  final drugList = <DataDrug>[].obs;
  final todayPlan = <DrugPlan>[].obs;
  final isToday = true.obs;
  final dayDrugInfo = RxMap<DateTime, DayDrugInfo?>({});
}
