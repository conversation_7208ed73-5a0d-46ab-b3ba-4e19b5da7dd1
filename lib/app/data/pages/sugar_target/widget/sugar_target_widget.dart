/// FileName: SugarTargetWidget
///
/// @Author: ygc
/// @Date: 2024/7/29 15:47
/// @Description:
import 'package:dnurse/app/common/link/deep_link.dart';
import 'package:dnurse/app/data/pages/sugar_target/widget/sugar_target_logic.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/theme.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../ui/style/font.dart';
import '../../../../../ui/widgets/bottom_operation_container_widget.dart';

class SugarTargetWidget extends StatelessWidget {
  const SugarTargetWidget({
    super.key,
    this.padding,
  });

  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SugarTargetWidgetLogic>();
    logic.context = context;

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Padding(
                padding: padding ?? EdgeInsets.symmetric(horizontal: 19.w),
                child: Column(
                  children: [
                    Obx(() {
                      if (logic.showHint) {
                        return _buildHintMessage(context);
                      }
                      return Container();
                    }),
                    _buildTable(context),
                    _buildTip(context),
                    SizedBox(
                      height: AppTheme.listSectionGap,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        BottomOperationContainerWidget(
          child: DNUPrimaryButton(
            title: '保存修改',
            onPressed: () {
              FocusScope.of(context).unfocus();
              logic.onSave();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTableCell(
    BuildContext context, {
    Widget? child,
    Color? backgroundColor,
    double? height,
  }) {
    return Container(
      height: height ?? 48.w,
      color: backgroundColor,
      child: Center(child: child ?? Container()),
    );
  }

  Widget _buildTableTextCell(
    BuildContext context, {
    String? title = '',
    Color? backgroundColor,
    double? height,
    bool bold = false,
  }) {
    return _buildTableCell(
      context,
      backgroundColor: backgroundColor,
      height: height,
      child: Text(
        title ?? '',
        style: TextStyle(
          color: AppColor.textPrimary,
          fontSize: 16.sp,
          fontWeight: bold ? FontWeight.bold : FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildCardWidget({Widget? child, EdgeInsetsGeometry? margin}) {
    return Container(
      margin: margin,
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 16.w,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.w),
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(255, 57, 71, 255).withOpacity(0.08),
            blurRadius: 10.w,
            spreadRadius: 0.w,
            offset: Offset(0, 4.w),
          )
        ],
      ),
      child: child,
    );
  }

  Widget _buildTable(BuildContext context) {
    return _buildCardWidget(
      // margin: EdgeInsets.only(top: AppTheme.listSectionGap),
      child: Obx(() {
        final logic = Get.find<SugarTargetWidgetLogic>();
        return Table(
          // border: TableBorder.all(color: AppColor.textDisable),
          // border: const TableBorder(
          //     horizontalInside: BorderSide(color: AppColor.textDisable)),
          columnWidths: {
            0: FixedColumnWidth(60.w),
          },
          children: [
            TableRow(
              children: [
                _buildTableTextCell(
                  context,
                  title: '时间段',
                  bold: true,
                ),
                _buildTableTextCell(
                  context,
                  title: '推荐值',
                  bold: true,
                ),
                _buildTableTextCell(
                  context,
                  title: '目标值',
                  bold: true,
                ),
              ],
            ),
            TableRow(
              children: [
                _buildTableTextCell(context, title: '糖化'),
                _buildTableTextCell(
                  context,
                  title: '< ${logic.diastaticRecommend} %',
                  // backgroundColor: AppColor.background2,
                ),
                _buildDiastaticCell(context),
              ],
            ),
            _buildTargetRow(
              context,
              title: '空腹',
              recommend: logic.emptyRecommend,
              lowController: logic.lowEmptyController,
              highController: logic.highEmptyController,
              lowFocusNode: logic.lowEmptyFocusNode,
              highFocusNode: logic.highEmptyFocusNode,
              onLowChanged: (value) {
                logic.lowEmptyValue.value = value;
              },
              onHighChanged: (value) {
                logic.highEmptyValue.value = value;
              },
            ),
            _buildTargetRow(
              context,
              title: '餐前',
              recommend: logic.beforeMealRecommend,
              lowController: logic.lowBeforeMealController,
              highController: logic.highBeforeMealController,
              lowFocusNode: logic.lowBeforeMealFocusNode,
              highFocusNode: logic.highBeforeMealFocusNode,
              onLowChanged: (value) {
                logic.lowBeforeMealValue.value = value;
              },
              onHighChanged: (value) {
                logic.highBeforeMealValue.value = value;
              },
            ),
            _buildTargetRow(
              context,
              title: '餐后',
              recommend: logic.afterMealRecommend,
              lowController: logic.lowAfterMealController,
              highController: logic.highAfterMealController,
              lowFocusNode: logic.lowAfterMealFocusNode,
              highFocusNode: logic.highAfterMealFocusNode,
              onLowChanged: (value) {
                logic.lowAfterMealValue.value = value;
              },
              onHighChanged: (value) {
                logic.highAfterMealValue.value = value;
              },
            ),
            _buildTargetRow(
              context,
              title: '睡前',
              recommend: logic.sleepRecommend,
              lowController: logic.lowSleepController,
              highController: logic.highSleepController,
              lowFocusNode: logic.lowSleepFocusNode,
              highFocusNode: logic.highSleepFocusNode,
              onLowChanged: (value) {
                logic.lowSleepValue.value = value;
              },
              onHighChanged: (value) {
                logic.highSleepValue.value = value;
              },
            ),
            _buildTargetRow(
              context,
              title: '凌晨',
              recommend: logic.dawnRecommend,
              lowController: logic.lowDawnController,
              highController: logic.highDawnController,
              lowFocusNode: logic.lowDawnFocusNode,
              highFocusNode: logic.highDawnFocusNode,
              onLowChanged: (value) {
                logic.lowDawnValue.value = value;
              },
              onHighChanged: (value) {
                logic.highDawnValue.value = value;
              },
            ),
          ],
        );
      }),
    );
  }

  TableRow _buildTargetRow(
    BuildContext context, {
    required String title,
    required String recommend,
    TextEditingController? lowController,
    TextEditingController? highController,
    ValueChanged<String>? onLowChanged,
    ValueChanged<String>? onHighChanged,
    FocusNode? lowFocusNode,
    FocusNode? highFocusNode,
  }) {
    return TableRow(
      children: [
        _buildTableTextCell(context, title: title),
        _buildTableTextCell(
          context,
          title: recommend,
          // backgroundColor: AppColor.background2,
        ),
        _buildSugarCell(
          context,
          lowController: lowController,
          highController: highController,
          onLowChanged: onLowChanged,
          onHighChanged: onHighChanged,
          lowFocusNode: lowFocusNode,
          highFocusNode: highFocusNode,
        ),
      ],
    );
  }

  Widget _buildDiastaticCell(BuildContext context) {
    final logic = Get.find<SugarTargetWidgetLogic>();

    return _buildTableCell(
      context,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '<',
            style: TextStyle(
              color: AppColor.primaryLight,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          buildEditField(
            controller: logic.diastaticController,
            focusNode: logic.diastaticFocusNode,
            onChanged: (value) {
              logic.diastaticValue.value = value;
            },
          ),
          SizedBox(width: 10.w),
          Text(
            '%',
            style: TextStyle(
              color: AppColor.primaryLight,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildEditField({
    TextEditingController? controller,
    ValueChanged<String>? onChanged,
    int? maxLength,
    FocusNode? focusNode,
    double? width,
    List<TextInputFormatter>? inputFormatters,
    TextInputType? keyboardType,
    ValueChanged<String>? onSubmitted,
  }) {
    return SizedBox(
      width: width ?? 40.w,
      child: TextField(
        decoration: InputDecoration(
          counterText: "",
          prefixIcon: null,
          iconColor: AppColor.textDesc,
          border: InputBorder.none,
          hintStyle: TextStyle(
            color: AppColor.textDisable,
            fontSize: AppFont.mainTextSize,
            fontWeight: FontWeight.w500,
            height: 1.1,
          ),
        ),
        inputFormatters: inputFormatters ??
            <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp(r'^\d\.?\d?')),
            ],
        // autofocus: focusNode,
        focusNode: focusNode,
        style: TextStyle(
          fontSize: AppFont.mainTextSize,
          color: AppColor.primaryLight,
          fontWeight: FontWeight.bold,
        ),
        controller: controller,
        textAlign: TextAlign.center,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        maxLength: maxLength ?? 4,
        keyboardType: keyboardType ??
            const TextInputType.numberWithOptions(decimal: true),
      ),
    );
  }

  Widget _buildSugarCell(
    BuildContext context, {
    TextEditingController? lowController,
    TextEditingController? highController,
    ValueChanged<String>? onLowChanged,
    ValueChanged<String>? onHighChanged,
    FocusNode? lowFocusNode,
    FocusNode? highFocusNode,
  }) {
    final logic = Get.find<SugarTargetWidgetLogic>();

    return _buildTableCell(
      context,
      child: Obx(() {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            buildEditField(
                controller: lowController,
                focusNode: lowFocusNode,
                inputFormatters: <TextInputFormatter>[
                  logic.onlyInputNumber
                      ? FilteringTextInputFormatter.allow(RegExp(r'^\d*'))
                      : FilteringTextInputFormatter.allow(RegExp(r'^\d\.?\d?')),
                ],
                keyboardType: TextInputType.numberWithOptions(
                  decimal: !logic.onlyInputNumber,
                ),
                onChanged: onLowChanged,
                onSubmitted: (value) {
                  Log.i("low: $value");
                }),
            Text(
              ' - ',
              style: TextStyle(
                color: AppColor.primaryLight,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            buildEditField(
                controller: highController,
                focusNode: highFocusNode,
                inputFormatters: <TextInputFormatter>[
                  logic.onlyInputNumber
                      ? FilteringTextInputFormatter.allow(RegExp(r'^\d*'))
                      : FilteringTextInputFormatter.allow(RegExp(r'^\d\.?\d?')),
                ],
                keyboardType: TextInputType.numberWithOptions(
                  decimal: !logic.onlyInputNumber,
                ),
                onChanged: onHighChanged,
                onSubmitted: (value) {
                  Log.i("high: $value");
                }),
          ],
        );
      }),
    );
  }

  Widget _buildTip(BuildContext context) {
    return _buildCardWidget(
      margin: EdgeInsets.only(
        top: AppTheme.listSectionGap,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "温馨提示",
            style: TextStyle(
              color: AppColor.textPrimary,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              // left: 10.w,
              // right: 10.w,
              top: 12.w,
              bottom: 10.w,
            ),
            child: Text.rich(
              TextSpan(
                children: [
                  const TextSpan(
                      text: "以下内容为糖护士IDSS™智能决策系统\n\n为您专属定制，仅供参考，请遵医嘱\n\n"
                          "1.推荐值是根据年龄、糖尿病类型、并发症等情况而智能分析出的最适合您的个性化控糖目标，"
                          "您可以"),
                  TextSpan(
                    text: "完善个人资料",
                    style: const TextStyle(
                      color: AppColor.primaryLight,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        DeepLink.handle(
                            "dnurseapp://com.dnurse/openwith?act=USER_HEALTHG_INFO");
                      },
                  ),
                  const TextSpan(text: "，以获得准确的推荐值。您也可以修改为医生建议您的控糖目标。\n\n"),
                  const TextSpan(
                      text:
                          "2.血糖控制目标应该个体化，要兼顾控糖的最大获益和存在的低血糖等风险问题。如出现频繁低血糖或无症状低血糖时，应放宽血糖的控制目标。"),
                  const TextSpan(text: "\n\n3.推荐阅读："),
                  TextSpan(
                    text: "血糖目标到底应该控制到多少?",
                    style: const TextStyle(
                      color: AppColor.primaryLight,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        DeepLink.handle(
                            "dnurseapp://com.dnurse/openwith?act=KL:19125");
                      },
                  ),
                ],
              ),
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColor.textPrimary,
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              bottom: 10.w,
            ),
            child: Center(
              child: Text(
                "以上数据内容仅供参考，不能作为诊断或治疗依据",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColor.textDesc,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildHintMessage(BuildContext context) {
    final logic = Get.find<SugarTargetWidgetLogic>();
    return Container(
      margin: EdgeInsets.only(
        top: 10.w,
      ),
      child: Ink(
        height: 50.w,
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: InkWell(
          splashColor: AppColor.splashColor,
          onTap: () {
            logic.onHintClicked();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 10.w,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Obx(() {
                    final logic = Get.find<SugarTargetWidgetLogic>();
                    return Text(
                      logic.hintMessage,
                      style: TextStyle(
                        color: AppColor.textPrimary,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  }),
                ),
                Icon(
                  DNUIconFont.qianwang,
                  size: 30.w,
                  color: AppColor.primaryLight,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
