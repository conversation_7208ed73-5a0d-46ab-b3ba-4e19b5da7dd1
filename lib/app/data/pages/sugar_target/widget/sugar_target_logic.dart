/// FileName: sugar_target_logic
///
/// @Author: ygc
/// @Date: 2024/7/29 15:48
/// @Description:
import 'dart:async';

import 'package:dnurse/app/data/common/data_target.dart';
import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/app/user/entity/unit/glucose_unit.dart';
import 'package:dnurse/app/user/service/user_config_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/exception/dnu_error.dart';
import 'package:dnurse/router/router.dart';
import 'package:dnurse/router/routes.dart';
import 'package:dnurse/ui/widgets/dialog/dnu_dialog.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../user/entity/recommend_target_values.dart';
import '../../../../user/service/user_info_service.dart';
import '../../../common/data_utils.dart';
import '../../../service/glucose_service.dart';

class SugarTargetWidgetLogic extends GetxController {
  // final SugarTargetState state = SugarTargetState();

  BuildContext? context;
  late StreamSubscription<DataTarget> _targetStream;
  DataTarget oldTarget = DataTarget.defaultTarget();
  final target = DataTarget.defaultTarget().obs;

  TextEditingController diastaticController = TextEditingController();
  FocusNode diastaticFocusNode = FocusNode();
  final diastaticValue = "".obs;

  TextEditingController lowEmptyController = TextEditingController();
  FocusNode lowEmptyFocusNode = FocusNode();
  final lowEmptyValue = "".obs;

  TextEditingController highEmptyController = TextEditingController();
  FocusNode highEmptyFocusNode = FocusNode();
  final highEmptyValue = "".obs;

  TextEditingController lowBeforeMealController = TextEditingController();
  FocusNode lowBeforeMealFocusNode = FocusNode();
  final lowBeforeMealValue = "".obs;

  TextEditingController highBeforeMealController = TextEditingController();
  FocusNode highBeforeMealFocusNode = FocusNode();
  final highBeforeMealValue = "".obs;

  TextEditingController lowAfterMealController = TextEditingController();
  FocusNode lowAfterMealFocusNode = FocusNode();
  final lowAfterMealValue = "".obs;

  TextEditingController highAfterMealController = TextEditingController();
  FocusNode highAfterMealFocusNode = FocusNode();
  final highAfterMealValue = "".obs;

  TextEditingController lowSleepController = TextEditingController();
  FocusNode lowSleepFocusNode = FocusNode();
  final lowSleepValue = "".obs;

  TextEditingController highSleepController = TextEditingController();
  FocusNode highSleepFocusNode = FocusNode();
  final highSleepValue = "".obs;

  TextEditingController lowDawnController = TextEditingController();
  FocusNode lowDawnFocusNode = FocusNode();
  final lowDawnValue = "".obs;

  TextEditingController highDawnController = TextEditingController();
  FocusNode highDawnFocusNode = FocusNode();
  final highDawnValue = "".obs;

  // final recommendTargetValues = RecommendTargetValues.defaultGroup.obs;

  String get diastaticRecommend {
    return "${recommendTargetValues.analogSugar}";
  }

  RecommendTargetValues get recommendTargetValues {
    return RecommendTargetValues.getByGroup(
        UserInfoService.to.currentUserInfo.value.userGroup);
  }

  String get emptyRecommend {
    return _formatRecommend(recommendTargetValues.emptyStomachLow,
        recommendTargetValues.emptyStomachHigh);
  }

  String get beforeMealRecommend {
    return _formatRecommend(recommendTargetValues.beforeMealLow,
        recommendTargetValues.beforeMealHigh);
  }

  String get afterMealRecommend {
    return _formatRecommend(recommendTargetValues.afterMealLow,
        recommendTargetValues.afterMealHigh);
  }

  String get dawnRecommend {
    return _formatRecommend(
        recommendTargetValues.dawnLow, recommendTargetValues.dawnHigh);
  }

  String get sleepRecommend {
    return _formatRecommend(
        recommendTargetValues.sleepLow, recommendTargetValues.sleepHigh);
  }

  String _formatRecommend(double low, double high) {
    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;
    return "${DataGlucose.formatMole(
      low,
      glucoseUnit,
    )} - ${DataGlucose.formatMole(high, glucoseUnit)}";
  }

  @override
  void onInit() {
    super.onInit();

    target.value = GlucoseService.to.currentDataTarget.value.copyWith();
    _initTarget();

    _targetStream = GlucoseService.to.currentDataTarget.listen((p0) {
      _initTarget();
    });

    _initListener();
  }

  void _initTarget() {
    oldTarget = target.value.copyWith();

    diastaticController.text = target.value.diastatic.toStringAsPrecision(2);

    lowEmptyController.text = lowEmpty;
    highEmptyController.text = highEmpty;

    lowBeforeMealController.text = lowBeforeMeal;
    highBeforeMealController.text = highBeforeMeal;

    lowAfterMealController.text = lowAfterMeal;
    highAfterMealController.text = highAfterMeal;

    lowSleepController.text = lowSleep;
    highSleepController.text = highSleep;

    lowDawnController.text = lowDawn;
    highDawnController.text = highDawn;
  }

  @override
  void onClose() {
    _targetStream.cancel();

    super.onClose();
  }

  void _initListener() {
    Duration debounceDuration = const Duration(seconds: 3);

    debounce(
      diastaticValue,
      (value) {
        double doubleValue = double.tryParse(value) ?? 0.0;
        if (doubleValue == 0) {
          target.value.diastatic = oldTarget.diastatic;

          diastaticController.text = oldTarget.diastatic.toStringAsPrecision(2);
          diastaticController.selection = TextSelection.fromPosition(
            TextPosition(
              offset: diastaticController.text.length,
            ),
          );
        } else {
          target.value.diastatic = doubleValue;
        }
      },
      time: debounceDuration,
    );

    debounce(
      lowEmptyValue,
      (value) {
        double? dataValue = checkGlucoseValue(
          value: value,
          controller: lowEmptyController,
          oldValue: formatValue(oldTarget.emptyLow),
        );
        if (dataValue != null) {
          target.value.emptyLow = dataValue;
        } else {
          target.value.emptyLow = oldTarget.emptyLow;
        }
      },
      time: debounceDuration,
    );

    debounce(
      highEmptyValue,
      (value) {
        double? dataValue = checkGlucoseValue(
          value: value,
          controller: highEmptyController,
          oldValue: formatValue(oldTarget.emptyHigh),
        );
        if (dataValue != null) {
          target.value.emptyHigh = dataValue;
        } else {
          target.value.emptyHigh = oldTarget.emptyHigh;
        }
      },
      time: debounceDuration,
    );

    debounce(lowBeforeMealValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: lowBeforeMealController,
        oldValue: formatValue(oldTarget.mealBeforeLow),
      );
      if (dataValue != null) {
        target.value.mealBeforeLow = dataValue;
      } else {
        target.value.mealBeforeLow = oldTarget.mealBeforeLow;
      }
    });

    debounce(highBeforeMealValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: highBeforeMealController,
        oldValue: formatValue(oldTarget.mealBeforeHigh),
      );
      if (dataValue != null) {
        target.value.mealBeforeHigh = dataValue;
      } else {
        target.value.mealBeforeHigh = oldTarget.mealBeforeHigh;
      }
    });

    debounce(lowAfterMealValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: lowAfterMealController,
        oldValue: formatValue(oldTarget.mealAfterLow),
      );
      if (dataValue != null) {
        target.value.mealAfterLow = dataValue;
      } else {
        target.value.mealAfterLow = oldTarget.mealAfterLow;
      }
    });

    debounce(highAfterMealValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: highAfterMealController,
        oldValue: formatValue(oldTarget.mealAfterHigh),
      );
      if (dataValue != null) {
        target.value.mealAfterHigh = dataValue;
      } else {
        target.value.mealAfterHigh = oldTarget.mealAfterHigh;
      }
    });

    debounce(lowSleepValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: lowSleepController,
        oldValue: formatValue(oldTarget.sleepLow),
      );
      if (dataValue != null) {
        target.value.sleepLow = dataValue;
      } else {
        target.value.sleepLow = oldTarget.sleepLow;
      }
    });

    debounce(highSleepValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: highSleepController,
        oldValue: formatValue(oldTarget.sleepHigh),
      );
      if (dataValue != null) {
        target.value.sleepHigh = dataValue;
      } else {
        target.value.sleepHigh = oldTarget.sleepHigh;
      }
    });

    debounce(lowDawnValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: lowDawnController,
        oldValue: formatValue(oldTarget.dawnLow),
      );
      if (dataValue != null) {
        target.value.dawnLow = dataValue;
      } else {
        target.value.dawnLow = oldTarget.dawnLow;
      }
    });

    debounce(highDawnValue, (value) {
      double? dataValue = checkGlucoseValue(
        value: value,
        controller: highDawnController,
        oldValue: formatValue(oldTarget.dawnHigh),
      );
      if (dataValue != null) {
        target.value.dawnHigh = dataValue;
      } else {
        target.value.dawnHigh = oldTarget.dawnHigh;
      }
    });
  }

  String get lowEmpty {
    return formatValue(target.value.emptyLow);
  }

  String get highEmpty {
    return formatValue(target.value.emptyHigh);
  }

  String get lowBeforeMeal {
    return formatValue(target.value.mealBeforeLow);
  }

  String get highBeforeMeal {
    return formatValue(target.value.mealBeforeHigh);
  }

  String get lowAfterMeal {
    return formatValue(target.value.mealAfterLow);
  }

  String get highAfterMeal {
    return formatValue(target.value.mealAfterHigh);
  }

  String get lowSleep {
    return formatValue(target.value.sleepLow);
  }

  String get highSleep {
    return formatValue(target.value.sleepHigh);
  }

  String get lowDawn {
    return formatValue(target.value.dawnLow);
  }

  String get highDawn {
    return formatValue(target.value.dawnHigh);
  }

  bool get onlyInputNumber {
    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;
    return glucoseUnit == GlucoseUnit.mg;
  }

  String formatValue(double value) {
    return DataUtils.formatMole(value);
  }

  bool get showHint {
    if (AppContext.to.isTemp) {
      return true;
    }

    return !UserInfoService.to.currentUserInfo.value.isEnoughInfo;
  }

  String get hintMessage {
    if (AppContext.to.isTemp) {
      return "无法获得推荐值，请先登录。";
    }
    return "无法获得推荐值，去设置个人资料。";
  }

  void onHintClicked() {
    if (AppContext.to.isTemp) {
      AppRouter.push(Routes.login, parameters: {"from": "target"});
    } else {
      AppRouter.push(Routes.userProfile, parameters: {"from": "target"});
    }
  }

  double? checkGlucoseValue({
    required String value,
    required TextEditingController controller,
    required String oldValue,
  }) {
    bool changed = false;
    double? resultValue;

    double doubleValue = double.tryParse(value) ?? 0;
    GlucoseUnit glucoseUnit = UserConfigService.to.glucoseUnit.value;
    if (glucoseUnit == GlucoseUnit.mg) {
      double maxValue = DataGlucose.maxValue * DataGlucose.moleToMgRatio;
      double miniValue = DataGlucose.minValue * DataGlucose.moleToMgRatio;
      if (doubleValue < miniValue) {
        controller.text = oldValue;

        changed = true;
      } else if (doubleValue > maxValue) {
        controller.text = oldValue;
        changed = true;
      } else {
        doubleValue = doubleValue / DataGlucose.moleToMgRatio;
        resultValue = doubleValue;
      }
    } else {
      double maxValue = DataGlucose.maxValue;
      double miniValue = DataGlucose.minValue;
      if (doubleValue < miniValue) {
        controller.text = oldValue;
        changed = true;
      } else if (doubleValue > maxValue) {
        controller.text = oldValue;
        changed = true;
      } else {
        resultValue = doubleValue;
      }
    }

    if (changed) {
      controller.selection = TextSelection.fromPosition(
        TextPosition(
          offset: controller.text.length,
        ),
      );
    }

    return resultValue;
  }

  bool checkChanged() {
    return target.value != oldTarget;
  }

  bool _checkPoint(
    double lowValue,
    double highValue,
    FocusNode lowFocusNode,
    FocusNode highFocusNode,
  ) {
    if (!DataUtils.validateGlucoseValueMole(lowValue)) {
      if (context != null && context!.mounted) {
        DNUToast.show(
          DataUtils.glucoseValueRangeError(),
          context!,
          gravity: DNUToastGravity.top,
        );
      }

      lowFocusNode.requestFocus();
      return false;
    }

    if (!DataUtils.validateGlucoseValueMole(highValue)) {
      if (context != null && context!.mounted) {
        DNUToast.show(
          DataUtils.glucoseValueRangeError(),
          context!,
          gravity: DNUToastGravity.top,
        );
      }
      highFocusNode.requestFocus();
      return false;
    }

    if (lowValue >= highValue) {
      if (context != null && context!.mounted) {
        DNUToast.show(
          "目标的最大值应不低于最小值",
          context!,
          gravity: DNUToastGravity.top,
        );
      }

      lowFocusNode.requestFocus();
      return false;
    }

    return true;
  }

  bool checkValue() {
    if (target.value.diastatic <= 0) {
      if (context != null && context!.mounted) {
        DNUToast.show(
          "请输入糖化值",
          context!,
          gravity: DNUToastGravity.top,
        );
      }

      return false;
    }

    if (!_checkPoint(
      target.value.emptyLow,
      target.value.emptyHigh,
      lowEmptyFocusNode,
      highEmptyFocusNode,
    )) {
      return false;
    }

    if (!_checkPoint(
      target.value.mealBeforeLow,
      target.value.mealBeforeHigh,
      lowBeforeMealFocusNode,
      highBeforeMealFocusNode,
    )) {
      return false;
    }

    if (!_checkPoint(
      target.value.mealAfterLow,
      target.value.mealAfterHigh,
      lowAfterMealFocusNode,
      highAfterMealFocusNode,
    )) {
      return false;
    }

    if (!_checkPoint(
      target.value.sleepLow,
      target.value.sleepHigh,
      lowSleepFocusNode,
      highSleepFocusNode,
    )) {
      return false;
    }

    if (!_checkPoint(
      target.value.dawnLow,
      target.value.dawnHigh,
      lowDawnFocusNode,
      highDawnFocusNode,
    )) {
      return false;
    }
    return true;
  }

  Future<bool> onSave({
    bool showMessage = true,
  }) async {
    if (!checkChanged()) {
      if (showMessage && context != null && context!.mounted) {
        DNUToast.show("目标值无修改", context!);
      }
      return true;
    }
    if (!checkValue()) {
      return false;
    }

    DataTarget value = target.value.copyWith();
    value.userModified = true;
    if (showMessage && context != null && context!.mounted) {
      DNULoading.show(context!, content: "保存中");
    }
    try {
      await GlucoseService.to.syncDataTarget(value);
      if (showMessage && context != null && context!.mounted) {
        DNULoading.dismiss(context!);
      }
      oldTarget = target.value.copyWith();
      return true;
    } on DNUError catch (e) {
      if (showMessage && context != null && context!.mounted) {
        DNULoading.dismiss(context!);
        DNUToast.show(e.message, context!);
      } else {
        rethrow;
      }
    }

    return false;
  }

  void showDialog(BuildContext context) {
    DNUDialog.showConfirm(
      title: "提示",
      content: "修改了控糖目标,\n是否保存?",
      onConfirm: () {
        onSave();
      },
      onCancel: () {
        AppRouter.back();
      },
    );
  }
}
