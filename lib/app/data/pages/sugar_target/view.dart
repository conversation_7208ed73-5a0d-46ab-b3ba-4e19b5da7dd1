import 'package:dnurse/app/data/pages/sugar_target/widget/sugar_target_logic.dart';
import 'package:dnurse/app/data/pages/sugar_target/widget/sugar_target_widget.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/theme.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import 'logic.dart';

class SugarTargetPage extends StatelessWidget {
  const SugarTargetPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<SugarTargetLogic>();
    final sugarTargetWidgetLogic = Get.find<SugarTargetWidgetLogic>();
    return Scaffold(
      backgroundColor: AppColor.background1,
      appBar: getAppBar(
          context: context,
          title: '控糖目标',
          backgroundColor: Colors.white,
          actions: [
            getAppBarAction(
              onPressed: () {
                logic.onSave(context);
              },
              iconData: DNUIconFont.baocun,
            ),
          ]),
      body: WillPopScope(
        onWillPop: () async {
          if (sugarTargetWidgetLogic.checkChanged()) {
            sugarTargetWidgetLogic.showDialog(context);
            return false;
          }
          return true;
        },
        child: SugarTargetWidget(
          padding: EdgeInsets.only(
            left: 12.w,
            right: 12.w,
            top: AppTheme.listSectionGap,
            bottom: AppTheme.listSectionGap,
          ),
        ),
      ),
    );
  }
}
