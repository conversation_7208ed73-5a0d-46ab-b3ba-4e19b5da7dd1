import 'package:dnurse/app/data/pages/sugar_target/widget/sugar_target_logic.dart';
import 'package:dnurse/app/data/service/glucose_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import 'state.dart';

class SugarTargetLogic extends GetxController {
  final SugarTargetState state = SugarTargetState();

  @override
  void onInit() {
    super.onInit();

    // Get.lazyPut<SugarTargetWidgetLogic>(() => SugarTargetWidgetLogic());
    //
    // final sugarTargetWidgetLogic = Get.find<SugarTargetWidgetLogic>();
    //
    // sugarTargetWidgetLogic.target.value =
    //     GlucoseService.to.currentDataTarget.value.copyWith();

    GlucoseService.to.downloadDataTarget();
  }

  void onSave(BuildContext context) async{
    final sugarTargetWidgetLogic = Get.find<SugarTargetWidgetLogic>();
    await sugarTargetWidgetLogic.onSave();

  }
}
