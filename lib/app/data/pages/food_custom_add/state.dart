import 'package:dnurse/ui/widgets/page/loading_page.dart';
import 'package:get/get.dart';

final foodSpeedMap = {
  '0': '快',
  '1': '慢',
  '2': '一般',
};

class FoodCustomAddState {
  FoodCustomAddState() {
    ///Initialize variables
  }

  final pageState = LoadingPageState.loading.obs;

  final foodName = ''.obs;
  final foodSpeed = foodSpeedMap['0'].obs;
  final foodWeight = 0.obs;
  final foodCalorie = 0.obs;
  final foodImage = ''.obs;

  final foodDesc = ''.obs;

  final imgPath = ''.obs;
  // bool get isEmpty => selectFoodList.isEmpty;
}
