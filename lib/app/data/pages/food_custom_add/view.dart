import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/pages/food_custom_add/brnRow_delegate.dart';
import 'package:dnurse/app/data/pages/food_custom_add/logic.dart';
import 'package:dnurse/app/data/pages/food_custom_add/state.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/style/theme.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:dnurse/ui/widgets/button/dnu_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

class FoodCustomAddPage extends StatelessWidget {
  FoodCustomAddPage({super.key});

  final controller = Get.find<FoodCustomAddLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.background3,
      appBar: getAppBar(
          context: context,
          backgroundColor: Colors.white,
          title: '添加饮食',
          elevation: 0,
          actions: [
            getAppBarAction(
              iconData: DNUIconFont.zhengchanggou1,
              onPressed: () {
                controller.saveCustomFood();
              },
            )
          ]),
      body: _buildBody(context),
      // 底部按钮
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  _buildBody(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: AppTheme.listSectionGap),
        _buildFormGroup(context),
      ],
    );
  }

  // form group
  _buildFormGroup(BuildContext context) {
    final controller = Get.find<FoodCustomAddLogic>();
    return Obx(() {
      return Column(
        children: [
          _buildFormItem(context, _buildUploadImage(context)),
          _buildFormItem(
            context,
            BrnTextInputFormItem(
              isRequire: true,
              title: "食物名称",
              hint: "请输入名称",
              onChanged: (newValue) {
                controller.state.foodName.value = newValue;
              },
            ),
          ),
          _buildFormItem(
            context,
            BrnTextSelectFormItem(
              title: "升糖快慢",
              hint: "快、慢、一般",
              value: controller.state.foodSpeed.value,
              onTap: () {
                BrnMultiDataPicker(
                  context: context,
                  title: '升糖快慢',
                  delegate: BrnRowDelegate(firstSelectedIndex: 1),
                  confirmClick: (list) {
                    controller.state.foodSpeed.value =
                        foodSpeedMap[list[0].toString()];
                  },
                ).show();
              },
            ),
          ),
          _buildFormItem(
            context,
            BrnTextInputFormItem(
              title: "食物重置",
              hint: "0",
              unit: "克",
              controller: TextEditingController(
                text: controller.state.foodWeight.value.toString(),
              ),
              onChanged: (newValue) {
                if (newValue.isNotEmpty) {
                  controller.state.foodWeight.value = int.parse(newValue);
                } else {
                  controller.state.foodWeight.value = 0;
                }
              },
            ),
          ),
          _buildFormItem(
            context,
            BrnTextInputFormItem(
              title: "热量",
              hint: "0",
              unit: "千卡",
              onChanged: (newValue) {
                controller.state.foodCalorie.value = int.parse(newValue);
              },
            ),
          ),

          // 文本域
          _buildFormItem(
              context,
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: BrnInputText(
                  hint: "备注信息:",
                  onTextChange: (newValue) {
                    controller.state.foodDesc.value = newValue;
                  },
                ),
              )),
        ],
      );
    });
  }

  _buildBottomNavigationBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      color: AppColor.backgroundWhite,
      child: DNUPrimaryButton(
        title: '继续添加饮食',
        fontSize: 16.sp,
        onPressed: () {
          controller.saveCustomFood(isContinue: true);
        },
      ),
    );
  }

  Widget _buildFormItem(BuildContext context, Widget child,
      {bool isLast = false}) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: isLast
            ? null
            : const Border(
                bottom: BorderSide(color: AppColor.tableBorder),
              ),
      ),
      child: child,
    );
  }

// 上传照片
  _buildUploadImage(BuildContext context) {
    final controller = Get.find<FoodCustomAddLogic>();
    return Obx(() {
      return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('上传照片', style: TextStyle(fontSize: 16.sp)),
              InkWell(
                onTap: () {
                  Get.find<FoodCustomAddLogic>().showImageSelectMenu(context);
                },
                child: controller.state.imgPath.value.isEmpty
                    ? Icon(Icons.camera_alt_outlined,
                        size: 24.sp, color: AppColor.textDesc)
                    : Image.network(controller.state.imgPath.value,
                        width: 50.w, height: 50.h),
              )
            ],
          ));
    });
  }
}
