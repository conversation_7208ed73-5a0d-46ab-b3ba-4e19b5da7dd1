import 'dart:convert'; // Added for jsonEncode

import 'package:bruno/bruno.dart';
import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/app/data/pages/food_custom_add/state.dart';
import 'package:dnurse/app/data/service/extra_data_service.dart';
import 'package:dnurse/app/user/service/case_manager_service.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:dnurse/ui/widgets/page/loading_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../bootstrap/app.dart';

class FoodCustomAddLogic extends DNUBaseController {
  final FoodCustomAddState state = FoodCustomAddState();

  void showImageSelectMenu(BuildContext context) {
    List<BrnCommonActionSheetItem> actions = [];
    actions.add(BrnCommonActionSheetItem(
      '从相册中选择',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ));
    actions.add(BrnCommonActionSheetItem(
      '相机',
      actionStyle: BrnCommonActionSheetItemStyle.link,
    ));

    // 展示actionSheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return BrnCommonActionSheet(
          actions: actions,
          clickCallBack: (
            int index,
            BrnCommonActionSheetItem actionEle,
          ) {
            _selectImages(index);
          },
        );
      },
    );
  }

  void _selectImages(int index) async {
    final ImagePicker picker = ImagePicker();
    XFile? image;
    if (index == 0) {
      image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );
    } else {
      image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );
    }

    Log.i("_selectImages: ${image?.path}");
    if (image == null || image.path.isEmpty) {
      return;
    }
    state.imgPath.value = image.path;
    state.pageState.value = LoadingPageState.loading;
    try {
      final DateTime now = DateTime.now();
      final int timestamp = now.secondsSinceEpoch;
      Map<String, Object> param = {
        "sn": AppContext.to.userSn,
        "did": DataUtils.generateDidWithPrefix('R'),
        "time": timestamp.toString(),
      };
      final response =
          await CaseManagerService.to.uploadReport(param, image.path);
      if (response.successful) {
        DNUToast.show("上传成功", Get.context!);
        Log.i("FoodCustomAddLogic imgPath: -------->>>>>>>>> ${response.data}");
        state.imgPath.value = response.data;
      } else {
        DNUToast.show("上传失败", Get.context!);
      }

      state.pageState.value = LoadingPageState.success;
    } catch (e) {
      Log.e("UserInfoService.to.changeAvatar error:", e);
    }
  }

// 校验是否为空
  Future<bool> checkFoodName() async {
    if (state.foodName.value.isEmpty) {
      DNUToast.show("请输入名称", Get.context!);
      return false;
    }
    return true;
  }

  // 检查食物是否存在
  Future<bool> checkFoodExist() async {
    final food =
        await ExtraDataService.to.getCustomFoodByName(state.foodName.value);
    return food != null;
  }

  // 清空数据
  void clearData() {
    state.foodName.value = '';
    state.foodSpeed.value = foodSpeedMap['0'];
    state.foodWeight.value = 0;
    state.foodCalorie.value = 0;
    state.imgPath.value = '';
    state.foodDesc.value = '';
  }

  Future<void> saveCustomFood({bool isContinue = false}) async {
    try {
      final isExist = await checkFoodExist();
      if (isExist) {
        DNUToast.show("食物已存在", Get.context!);
        return;
      }

      final isNameEmpty = await checkFoodName();
      if (!isNameEmpty) {
        return;
      }

      // 将unit对象数组转换为JSON字符串
      final unitJson = jsonEncode([
        {
          "u": "1",
          "r": "1",
          "s": {"min": "10", "max": "500", "x": "5"}
        }
      ]);

      await ExtraDataService.to.saveCustomFood({
        'name': state.foodName.value,
        'type': 1,
        'class': '自定义食物',
        'generalname': state.foodName.value,
        'unit': unitJson, // 使用转换后的JSON字符串
        'calories': state.foodCalorie.value,
        'fromuser': 1,
        'imageurl': state.imgPath.value,
        'starnum': 0,
        'abstract': state.foodDesc.value,
        'glycemic_Index': state.foodSpeed.value,
      });

      DNUToast.show("保存成功", Get.context!);
      clearData();
      if (!isContinue) {
        Get.back();
      }
    } catch (e) {
      Log.e("FoodCustomAddLogic.saveCustomFood error:", e);
      DNUToast.show("保存失败", Get.context!);
    }
  }
}
