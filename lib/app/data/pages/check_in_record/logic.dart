import 'dart:math';

import 'package:dnurse/app/common/widget/share_action_sheet.dart';
import 'package:dnurse/app/common/widget/widgets_to_share.dart';
import 'package:dnurse/app/data/pages/check_in_record/widget/pie_progress.dart';
import 'package:dnurse/app/data/pages/check_in_record/widget/share_img.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/ui/style/font.dart';
import 'package:dnurse/ui/widgets/dnu_loading.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../framework/utils/log.dart';
import '../../../../ui/style/color.dart';
import 'calenderWidget.dart';
import 'state.dart';

class Event {
  final bool isQualified;

  Event(this.isQualified);
}

class CheckInRecordLogic extends DNUBaseController {
  final CheckinRecordState state = CheckinRecordState();

  final accumulatedDays = 0.obs;
  final continueDays = 0.obs;
  final passRate = 0.obs;
  final rank = 0.obs;
  final focusDay = Rx<DateTime>(DateTime.now());
  final selectedDay = Rx<DateTime?>(null);
  final rangeStart = Rx<DateTime?>(null);
  final rangeEnd = Rx<DateTime?>(null);
  final Map<DateTime, List<Event>> events = {
    DateTime(2024, 8, 20): [Event(false)],
    DateTime(2024, 8, 13): [Event(true)],
    DateTime(2024, 9, 4): [Event(false)],
    DateTime(2024, 8, 31): [
      Event(true),
    ],
  };

  final size = 30.obs;

  // 存储每个任务项的展开状态，key为任务标题
  final expandedItems = <String, bool>{}.obs;

  // 切换任务项的展开状态
  void toggleItemExpansion(String title) {
    if (expandedItems.containsKey(title)) {
      expandedItems[title] = !expandedItems[title]!;
    } else {
      expandedItems[title] = true;
    }
  }

  // 检查任务项是否展开
  bool isItemExpanded(String title) {
    return expandedItems[title] ?? false;
  }

  List<Event> getEventsForDay(DateTime day) {
    DateTime dateWithoutTime = DateTime(day.year, day.month, day.day);
    List<Event> eventsForDay = events[dateWithoutTime] ?? [];
    Log.i(
        "Events for $day: ${eventsForDay.map((e) => e.isQualified).toList()}");
    return eventsForDay;
  }

  String get glucoseAccumulatedDays {
    return accumulatedDays.value == 0
        ? "- -"
        : accumulatedDays.value.toString();
  }

  String get glucoseContinueDays {
    return continueDays.value == 0 ? "- -" : continueDays.value.toString();
  }

  String get glucoseRank {
    return rank.value > 0 ? "${rank.value}" : "- -";
  }

  String get glucosePassRate {
    return passRate.value > 0 ? "${passRate.value}%" : "- -";
  }

  CalendarBuilders getCalendarBuilders() {
    return CalendarBuilders(
      // 默认日期构建器 - 用于常规日期
      defaultBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay);
      },
      // 今天的日期构建器
      todayBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isToday: true);
      },
      // 选中日期的构建器
      selectedBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isSelected: true);
      },

      // 禁用日期的构建器
      disabledBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isDisabled: true);
      },
      // 日期范围起始日构建器
      rangeStartBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isRangeStart: true);
      },
      // 日期范围结束日构建器
      rangeEndBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isRangeEnd: true);
      },
      // 日期范围内的日期构建器
      withinRangeBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isWithinRange: true);
      },
      // 范围外日期构建器（不在当前月份的日期）
      outsideBuilder: (context, day, focusedDay) {
        return _buildDayCell(day, focusedDay, isOutside: true);
      },
      // 标记构建器
      markerBuilder: (BuildContext context, date, events) {
        bool isQualified = events.any((event) => event.isQualified);
        // 根据是否有合格的事件来设置颜色
        BoxDecoration decoration = isQualified
            ? const BoxDecoration(
                gradient: AppColor.funcGlucoseNormalGradient,
                shape: BoxShape.circle,
              )
            : const BoxDecoration(
                color: AppColor.funcGlucoseLow1,
                shape: BoxShape.circle,
              );
        if (events.isEmpty) return const SizedBox();
        return ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: events.length,
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.only(top: 40.w),
                padding: EdgeInsets.all(1.w),
                child: Container(
                    width: 12.w, // for horizontal axis
                    decoration: decoration),
              );
            });
      },
    );
  }

  // 构建日期单元格的方法
  Widget _buildDayCell(
    DateTime day,
    DateTime focusedDay, {
    bool isToday = false,
    bool isSelected = false,
    bool isDisabled = false,
    bool isRangeStart = false,
    bool isRangeEnd = false,
    bool isWithinRange = false,
    bool isOutside = false,
  }) {
    // 判断是否是当前月的日期
    final bool isCurrentMonth = day.month == focusedDay.month;
    if (!isCurrentMonth && !isOutside) {
      return Container(); // 如果不是当前月份的日期且不是特别指定的范围外日期，返回空容器
    }

    // 根据不同状态设置不同的颜色
    Color backgroundColor = AppColor.backgroundWhite; // 默认浅蓝色背景
    Color textColor = Colors.black;

    if (isToday) {
      // backgroundColor = AppColor.primaryBackgroundLight6;
      // textColor = AppColor.primary;
    }

    if (isSelected || isRangeStart || isRangeEnd) {
      backgroundColor = AppColor.primaryBackgroundDark6;
      textColor = AppColor.primary;
    }

    if (isWithinRange) {
      backgroundColor = AppColor.primaryBackgroundLight6;
    }

    if (isDisabled) {
      // backgroundColor = Colors.grey.withOpacity(0.3);
      textColor = AppColor.textPrimary;
    }

    if (isOutside) {
      // 范围外日期的样式
      backgroundColor = Colors.transparent;
      textColor = AppColor.textDisable.withOpacity(0.5);

      // 对于范围外的日期使用简单的文本显示
      return Container(
        width: size.value.w,
        height: size.value.w,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            '${day.day}',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: textColor,
              fontFamily: AppFont.rany,
            ),
          ),
        ),
      );
    }

    return Container(
      width: size.value.w,
      height: size.value.w,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
      ),
      child: isDisabled
          // 禁用状态下只显示文本
          ? Center(
              child: Text(
                '${day.day == focusDay.value.day ? "今" : day.day}',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                  fontFamily: AppFont.rany,
                ),
              ),
            )
          // 非禁用状态下显示饼状进度条
          : PieProgressWidget(
              value: 0.75,
              // 这里可以根据实际数据动态设置进度值
              size: 80,
              backgroundColor: AppColor.background2,

              progressGradient: const LinearGradient(
                colors: [
                  Color(0xFFB6DAFF),
                  Color(0xFFD7E9FF),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                stops: [0.0, 1.0],
                transform: GradientRotation(270 * pi / 180), // 270度旋转
              ),
              child: Text(
                '${day.day == focusDay.value.day ? "今" : day.day}',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                  fontFamily: AppFont.rany,
                ),
              ),
            ),
    );
  }

  Widget _buildCalendarForShare(BuildContext context) {
    return Container(
      height: 320.h,
      width: 258.w,
      decoration: BoxDecoration(
        color: AppColor.backgroundWhite,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25.w), topRight: Radius.circular(25.w)),
      ),
      child: Obx(() {
        return RecordCalender.recordDefaultCalender(
          shouldFillViewport: true,
          focusedDay: focusDay.value,
          rangeStartDay: rangeStart.value,
          rangeEndDay: rangeEnd.value,
          eventLoader: getEventsForDay,
          onDaySelected: (selectedDay, focusedDay) {
            this.selectedDay.value = selectedDay;
            this.focusDay.value = focusedDay;
          },
          selectedDayPredicate: (day) {
            return isSameDay(selectedDay.value, day);
          },
          calendarBuilders: getCalendarBuilders(),
        );
      }),
    );
  }

  void showShareImg(BuildContext context) async {
    // size.value = 23;

    try {
      DNULoading.show(context);

      var tempDir = await getTemporaryDirectory();
      var filePath =
          '${tempDir.path}/temp/sign/image_${DateTime.now().millisecondsSinceEpoch}.png';
      if (!context.mounted) {
        return;
      }

      final path = await WidgetsToShare.captureAndSave(
        context: context,
        filePath: filePath,
        child: ShareImg(
          child: _buildCalendarForShare(context),
        ),
      );

      if (!context.mounted) {
        return;
      }

      ShareSheet.show(context,
          shareType: ShareType.image,
          shareChannel: ShareChannel.all,
          url: path, onSuccess: (int channel) {
        print('分享成功');
      }, onFail: (int channel) {}, onCancel: (int channel) {});
    } catch (e) {
      DNULoading.dismiss(context);
      print('生成分享图片失败: $e');
    }
  }
}
