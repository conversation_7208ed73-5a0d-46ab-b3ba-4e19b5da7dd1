import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../bootstrap/app.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';

class RecordCalender {
  // 静态方法，用于创建一个默认的日历视图
  static Widget recordDefaultCalender({
    required DateTime focusedDay, // 必需参数，当前聚焦的日期
    DateTime? rangeStartDay, // 可选参数，日期范围的开始日期
    DateTime? rangeEndDay, // 可选参数，日期范围的结束日期
    DateTime? firstDay, // 可选参数，日历可显示的最早日期
    DateTime? lastDay, // 可选参数，日历可显示的最晚日期
    bool shouldFillViewport = false, // 是否填充视口，默认为 false
    OnRangeSelected? onRangeSelected, // 可选参数，日期范围选择的回调函数
    OnDaySelected? onDaySelected, // 可选参数，单个日期选择的回调函数
    RangeSelectionMode? rangeSelectionMode, // 可选参数，日期范围选择模式
    void Function(DateTime focusedDay)? onPageChanged, // 可选参数，页面切换时的回调函数
    bool Function(DateTime day)? enabledDayPredicate, // 可选参数，判断日期是否可用的函数
    bool Function(DateTime day)? selectedDayPredicate, // 可选参数，判断日期是否被选中的函数
    // 可选参数，日历构建器，默认为一个常量对象
    // CalendarBuilders mCalendarBuilders =
    //     const CalendarBuilders(),
    List<dynamic> Function(DateTime day)? eventLoader,
    Decoration? customMarkerDecoration,
    CalendarBuilders? calendarBuilders,
  }) {
    return TableCalendar(
      calendarBuilders: calendarBuilders ?? const CalendarBuilders(),
      // 是否填充视口
      shouldFillViewport: shouldFillViewport,
      // 页面切换时的回调函数
      onPageChanged: onPageChanged,
      // 设置一周的开始日为周一
      startingDayOfWeek: StartingDayOfWeek.monday,
      // 设置一周的高度为 36.w（可能是根据某种单位定义的高度）
      daysOfWeekHeight: 20.w,
      // 设置日历头部样式
      headerStyle: HeaderStyle(
        // 格式化头部显示的日期文本，这里使用日期格式化函数将日期格式化为特定格式
        titleTextFormatter: (DateTime date, dynamic locale) {
          return DateFormat('yyyy-MM').format(date);
        },
        // 不显示格式按钮
        formatButtonVisible: false,
        // 标题居中显示
        titleCentered: true,
        // 设置标题文本样式
        titleTextStyle: TextStyle(
          color: AppColor.textPrimary,
          fontFamily: AppFont.ranyMedium,
          fontSize: 18.sp,
        ),
        leftChevronIcon: Container(
          width: 27.w,
          height: 27.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(13.w),
            color: AppColor.primaryBackgroundLight6,
          ),
          child: Icon(Icons.arrow_back_ios_new,
              color: AppColor.primary, size: 13.sp),
        ),
        rightChevronIcon: Container(
          width: 27.w,
          height: 27.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(13.w),
            color: AppColor.primaryBackgroundLight6,
          ),
          child: Icon(Icons.arrow_forward_ios,
              color: AppColor.primary, size: 13.sp),
        ),
      ),
      // 设置一周中每天的样式
      daysOfWeekStyle: DaysOfWeekStyle(
        // 格式化一周中每天的文本，根据当前日期的星期几从一个列表中获取对应文本
        dowTextFormatter: (DateTime date, dynamic locale) {
          List<String> weeks = [
            AppContext.to.getLocale().weekMondaySimple,
            AppContext.to.getLocale().weekTuesdaySimple,
            AppContext.to.getLocale().weekThursdaySimple,
            AppContext.to.getLocale().weekWednesdaySimple,
            AppContext.to.getLocale().weekFridaySimple,
            AppContext.to.getLocale().weekSaturdaySimple,
            AppContext.to.getLocale().weekSundaySimple,
          ];
          return weeks[date.weekday - 1];
        },
        // 设置一周中每天的装饰，包括背景颜色和圆角半径
        decoration: BoxDecoration(
            // color: const Color(0xFFF0F0F6),
            // color: Colors.white,
            borderRadius: BorderRadius.circular(4.sp)),
      ),

      // 设置日历可显示的最早日期，如果未传入参数，则默认为 2022 年 1 月 1 日
      firstDay: firstDay ?? DateTime(2022, 1, 1),
      // 设置日历可显示的最晚日期，如果未传入参数，则默认为当前日期
      lastDay: lastDay ?? DateTime.now(),
      // 设置日期范围的开始日期
      rangeStartDay: rangeStartDay,
      // 设置日期范围的结束日期
      rangeEndDay: rangeEndDay,
      // 设置日期范围选择模式，如果未传入参数，则默认为 toggledOn
      rangeSelectionMode: rangeSelectionMode ?? RangeSelectionMode.toggledOn,
      // 日期范围选择的回调函数
      onRangeSelected: onRangeSelected,
      // 单个日期选择的回调函数
      onDaySelected: onDaySelected,
      // 判断日期是否可用的函数
      enabledDayPredicate: enabledDayPredicate,
      // 判断日期是否被选中的函数
      selectedDayPredicate: selectedDayPredicate,
      // 设置日历的样式
      calendarStyle: _getCalendarStyle(customMarkerDecoration),
      // 设置日历构建器
      // calendarBuilders: mCalendarBuilders,
      // 设置聚焦的日期
      focusedDay: focusedDay,
      eventLoader: eventLoader,
    );
  }

  // 获取日历样式的静态方法
  static CalendarStyle _getCalendarStyle(Decoration? customMarkerDecoration) {
    TextStyle enableTextStyle = TextStyle(
      fontSize: 20.sp,
      fontFamily: AppFont.ranyBold,
      color: AppColor.textPrimary,
    );

    TextStyle disableTextStyle = TextStyle(
      fontSize: 20.sp,
      fontFamily: AppFont.ranyBold,
      color: AppColor.textDisable,
    );

    // 范围外日期的文本样式（不在当前月份的日期）
    TextStyle outsideTextStyle = TextStyle(
      fontSize: 16.sp,
      fontFamily: AppFont.ranyBold,
      color: AppColor.textDisable.withOpacity(0.5),
    );

    Color rangeColor = AppColor.auxiliaryGreen2;
    // Color rangeColor = Colors.yellow;
    BoxDecoration rangeDecoration = BoxDecoration(
      color: rangeColor,
      shape: BoxShape.circle,
    );

    return CalendarStyle(

        // 在日期范围内的文本样式
        withinRangeTextStyle: enableTextStyle,
        // 不在日期范围内的文本样式（不在当前月份的日期）
        outsideTextStyle: outsideTextStyle,
        // 默认文本样式
        defaultTextStyle: enableTextStyle,
        // 周末文本样式
        weekendTextStyle: enableTextStyle,
        // 日期范围开始的装饰
        rangeStartDecoration: rangeDecoration,
        // 日期范围结束的装饰
        rangeEndDecoration: rangeDecoration,
        // 日期范围高亮颜色
        rangeHighlightColor: rangeColor,
        // 日期范围开始的文本样式
        rangeStartTextStyle: enableTextStyle,
        // 日期范围结束的文本样式
        rangeEndTextStyle: enableTextStyle,
        // 不可用日期的文本样式
        disabledTextStyle: disableTextStyle,

        // 今天的文本样式
        todayTextStyle: TextStyle(
          fontSize: 20.sp,
          fontFamily: AppFont.ranyBold,
          color: AppColor.primary,
        ),
        // 今天的装饰
        todayDecoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
          // color: Color(0x333947e5),
        ),
        // 选中日期的装饰
        selectedDecoration: rangeDecoration,
        // 选中日期的文本样式
        selectedTextStyle: TextStyle(
          fontSize: 20.sp,
          fontFamily: AppFont.ranyBold,
          color: AppColor.primary,
        ),
        // 事件点装饰 如果有自定义装饰，则使用自定义装饰，否则使用默认装饰
        markerDecoration: customMarkerDecoration ??
            const BoxDecoration(
              // 设置事件点的颜色
              color: Colors.transparent,
              // gradient: AppColor.funcGlucoseNormalGradient,
              shape: BoxShape.circle, // 设置事件点的形状为圆形
            ),
        // 事件点最大数量
        markersMaxCount: 1,
        // 事件点溢出设置
        canMarkersOverflow: true,
        // 事件点自动对齐
        markersAutoAligned: true,
        // 事件点大小比例
        // markerSizeScale: 0.3,
        // 事件点间距
        // markerMargin: EdgeInsets.all(1.0),
        markerSize: 6.w);
  }

  // 获取头部图标按钮的静态方法
  static Widget _getHeadIconButton(IconData iconData) {
    return Container(
      width: 20.w,
      height: 20.w,
      decoration: const BoxDecoration(
        color: AppColor.textDisable,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Icon(
          iconData,
          color: AppColor.primary,
          size: 13.sp,
        ),
      ),
    );
  }
}
