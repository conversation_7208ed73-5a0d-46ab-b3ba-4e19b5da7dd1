import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../resource/dnu_assets.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import 'calenderWidget.dart';
import 'logic.dart';

class CheckInRecordPage extends StatelessWidget {
  const CheckInRecordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<CheckInRecordLogic>();
    return Scaffold(
        extendBodyBehindAppBar: true, // 让 body 延伸到 AppBar 后面
        appBar: getAppBar(
          context: context,
          title:
          '打卡记录',
          backgroundColor: Colors.transparent, // 设置 AppBar 背景透明
          elevation: 0, // 移除阴影

          actions: [
            getAppBarAction(
                title: '分享',
                onPressed: () {
                  logic.showShareImg(context);
                },
            ),
          ],
        ),
        body: Stack(
          children: [
            Positioned.directional(
                textDirection: TextDirection.ltr,
                child: Container(
                  width: double.infinity,
                  height: 172.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColor.primaryLight.withOpacity(0.2),
                        AppColor.primaryLight.withOpacity(0),
                      ],
                    ),
                  ),
                )),
            SafeArea(
              child: Scrollbar(
                thickness: 6,
                radius: const Radius.circular(10),
                thumbVisibility: true,
                child: SingleChildScrollView(
                  child: Column(
                    children: [_builderCalender(context), _buildContent()],
                  ),
                ),
              ),
            )
          ],
        ));
  }

  Widget _builderCalender(BuildContext context) {
    final logic = Get.put(CheckInRecordLogic());
    return Container(
      height: 320.w,
      width: 343.w,
      padding: EdgeInsets.only(bottom: 20.w),
      decoration: BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25.w), topRight: Radius.circular(25.w)),
      ),
      child: Obx(() {
        return RecordCalender.recordDefaultCalender(
          shouldFillViewport: true,
          focusedDay: logic.focusDay.value,
          rangeStartDay: logic.rangeStart.value,
          rangeEndDay: logic.rangeEnd.value,
          eventLoader: logic.getEventsForDay,
          onDaySelected: (selectedDay, focusedDay) {
            logic.selectedDay.value = selectedDay;
            logic.focusDay.value = focusedDay;
          },
          selectedDayPredicate: (day) {
            return isSameDay(logic.selectedDay.value, day);
          },
          calendarBuilders: logic.getCalendarBuilders(),
        );
      }),
    );
  }

  Widget _buildContent() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
      decoration: BoxDecoration(
        color: AppColor.backgroundWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.w),
          topRight: Radius.circular(25.w),
        ),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(57, 71, 229, 0.08),
            offset: Offset(0, -4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            text: const TextSpan(
              style: TextStyle(color: AppColor.textSecondary),
              children: [
                TextSpan(
                  text: "6",
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: AppColor.textPrimary),
                ),
                TextSpan(
                  text: "月",
                  style: TextStyle(
                    color: AppColor.textSecondary,
                    fontSize: 16,
                  ),
                ),
                TextSpan(
                  text: "9",
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: AppColor.textPrimary),
                ),
                TextSpan(
                  text: "日",
                  style: TextStyle(
                    color: AppColor.textSecondary,
                    fontSize: 16,
                  ),
                ),
                TextSpan(text: "  "),
                TextSpan(
                  text: "任务完成情况",
                  style: TextStyle(
                    color: AppColor.textDesc,
                    fontSize: 16,
                  ),
                )
              ],
            ),
          ),
          _taskItem(
            title: "血糖监测",
            img: DNUAssets.to.images.task.xue,
            progress: 1,
            segments: 8,
            textsLeft: [
              '空腹6.5mmol/L',
              '午餐前6.5mmol/L',
              '晚餐前6.5mmol/L',
            ],
            textsRight: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
            ],
          ),
          _taskItem(
            title: "膳食管理",
            img: DNUAssets.to.images.task.eat,
            progress: 1,
            segments: 8,
            textsLeft: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
            textsRight: [
              '早餐后6.5mmol/L',
            ],
          ),
          _taskItem(
            title: "每日运动",
            img: DNUAssets.to.images.task.run,
            progress: 1,
            segments: 8,
            textsLeft: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
            textsRight: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
          ),
          _taskItem(
            title: "每日一考",
            img: DNUAssets.to.images.task.notebook,
            progress: 1,
            segments: 8,
            textsLeft: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
            textsRight: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
          ),
          _taskItem(
            title: "每日一签",
            img: DNUAssets.to.images.task.date,
            progress: 1,
            segments: 8,
            textsLeft: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
            textsRight: [
              '早餐后6.5mmol/L',
              '午餐后6.5mmol/L',
              '晚餐后6.5mmol/L',
              '其他时间6.5mmol/L',
            ],
          ),
        ],
      ),
    );
  }

  Widget _taskItem({
    String title = "",
    String img = "",
    int progress = 1,
    int segments = 8,
    List<String> textsLeft = const [],
    List<String> textsRight = const [],
  }) {
    final logic = Get.find<CheckInRecordLogic>();

    return Obx(() {
      final isExpanded = logic.isItemExpanded(title);

      return GestureDetector(
        onTap: () => logic.toggleItemExpansion(title),
        child: Container(
          margin: EdgeInsets.only(top: 10.w),
          decoration: BoxDecoration(
            color: AppColor.background1,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.w),
              // 使用自适应高度，但在收起状态下限制内容
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image(
                              image: AssetImage(img),
                              width: 35.w,
                              height: 35.w),
                          const SizedBox(width: 6),
                          Text(title),
                        ],
                      ),
                      Row(
                        children: [
                          Text("$progress/$segments",
                              style: const TextStyle(color: AppColor.textDesc)),
                        ],
                      ),
                    ],
                  ),
                  if (isExpanded)
                    Padding(
                      padding: EdgeInsets.only(top: 10.w),
                      child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(8.w),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              // 顶部对齐
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    // 顶部对齐
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: _buildTextItems(textsLeft)),
                                Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: _buildTextItems(textsRight))
                              ])),
                    ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  // 辅助方法：根据文本列表生成带间距的Text组件列表
  List<Widget> _buildTextItems(List<String> texts) {
    List<Widget> result = [];
    for (int i = 0; i < texts.length; i++) {
      // 添加文本
      result.add(
        Text(
          texts[i],
          style: const TextStyle(color: AppColor.textDesc),
        ),
      );

      // 如果不是最后一个元素，添加间距
      if (i < texts.length - 1) {
        result.add(SizedBox(height: 10.w));
      }
    }
    return result;
  }
}
