import 'package:dnurse/app/user/api/dto/member/user_member_info_v2_dto.dart';
import 'package:dnurse/app/user/service/user_info_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/widgets/avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class Avatars extends StatelessWidget {
  const Avatars({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 100.w,
        height: 70.h,
        child: Stack(
          children: [
            Positioned(
              top: 20.h,
              left: 3.w,
              child: Avatar(
                size: 35.w,
                url: AppContext.to.isTemp
                    ? null
                    : AppContext.to.auth.value.headImgUrl,
              ),
            ),
            Positioned(
              left: 3.w,
              top: 0,
              child: _buildLevelIcon(context),
            ),
          ],
        ));
  }

  Widget _buildLevelIcon(BuildContext context) {
    return Obx(() {
      UserMemberInfoV2DTO userMemberInfoDTO =
          UserInfoService.to.userMemberInfoV2.value;

      if (userMemberInfoDTO.level_list == null) {
        return Container();
      }

      return Image.asset(
        "assets/images/user/user_level_icon_${userMemberInfoDTO.level_list?.level}.png",
        fit: BoxFit.fitWidth,
        width: 35.w,
      );
    });
  }
}
