import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'dart:ui';

import 'package:flutter_screenutil/flutter_screenutil.dart';

class BgT extends StatelessWidget {
  const BgT({super.key});

  double dp(double value, BuildContext context) {
    return value * MediaQuery.of(context).devicePixelRatio / 2;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // 第一个渐变圆（紫色）
          Positioned(
            left: dp(-54.w, context),
            top: dp(-36.w, context),
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Opacity(
                opacity: 0.17, // 增强透明度
                child: Container(
                  width: dp(238.w, context),
                  height: dp(228.w, context),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColor.auxiliaryPurple2,
                        AppColor.auxiliaryPurple2,

                        // AppColor.backgroundWhite
                      ],
                      stops: [0.2, 1.0], // 扩展渐变范围
                    ),
                  ),
                ),
              ),
            ),
          ),
          // 图片
          Positioned(
            left: dp(10.w, context),
            top: dp(10.w, context),
            child: Opacity(
              opacity: 0.25,
              child: Container(
                width: dp(113.w, context),
                height: dp(113.w, context),
                child: Image.asset(DNUAssets.to.images.task.safe),
              ),
            ),
          ),
          // 第二个渐变圆（红色）
          Positioned(
            left: dp(166.w, context),
            top: dp(-6.w, context),
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Opacity(
                opacity: 0.15, // 增强透明度
                child: Container(
                  width: dp(168.w, context),
                  height: dp(178.w, context),
                  // color: AppColor.auxiliaryRed2,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        // rgba(255, 163, 183, 1)
                        Color.fromRGBO(255, 163, 183, 1),
                        Color.fromRGBO(255, 163, 183, 1),
                      ],
                      stops: [0.3, 1.0], // 扩展渐变范围
                    ),
                  ),
                ),
              ),
            ),
          ),

          // // 模糊渐变条效果
          Positioned(
            left: dp(120.w, context),
            top: dp(70.w, context),
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(sigmaX: 160, sigmaY: 40),
              child: Container(
                width: dp(160.w, context),
                height: dp(120.w, context),
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color.fromRGBO(59, 255, 252, 0), // 透明青色起始
                      Color.fromRGBO(26, 255, 236, 0.2), // 青色结束
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
