import 'package:dnurse/app/data/pages/check_in_record/widget/bg.dart';
import 'package:dnurse/app/task/widgets/bg.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:dnurse/app/data/pages/check_in_record/widget/avatar.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ShareImg extends StatelessWidget {
  const ShareImg({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 510.h, // Use a fixed height instead of double.infinity
      // 溢出隐藏
      clipBehavior: Clip.hardEdge,
      // color: Colors.white,
      // 背景图片
      decoration: BoxDecoration(
        color: Colors.white,

        // 设置圆角
        borderRadius: BorderRadius.circular(15.w),
      ),
      child: Stack(
        children: [
          const BgT(),
          Column(
            children: [
              SizedBox(height: 40.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Row(
                  // 两端对齐
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  // padding

                  children: [
                    const Avatars(),
                    _dayText(),
                  ],
                ),
              ),
              SizedBox(height: 60.h),
              child,
            ],
          ),
        ],
      ),
    );
  }

  Widget _dayText() {
    return Container(
        child: Row(
      children: [
        Text("187",
            style: TextStyle(
                fontSize: 33.34.sp,
                fontWeight: FontWeight.w700,
                color: AppColor.primary,
                fontFamily: 'Rany')),
        SizedBox(width: 4.w),
        Text("天健康管理 \n连续不断电",
            style: TextStyle(
                fontSize: 11.03.sp,
                fontWeight: FontWeight.w400,
                color: AppColor.textSecondary,
                fontFamily: '思源黑体')),
      ],
    ));
  }
}
