import 'dart:math';

import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 饼状进度条组件
/// 用于显示日历中的进度
class PieProgressWidget extends StatelessWidget {
  const PieProgressWidget({
    Key? key,
    required this.value,
    this.size = 40,
    this.backgroundColor = const Color(0xFFE6E8FF),
    this.progressGradient,
    this.progressColor,
    this.child,
  }) : super(key: key);

  /// 当前进度，取值范围 [0.0-1.0]
  final double value;

  /// 组件大小
  final double size;

  /// 背景颜色
  final Color backgroundColor;

  /// 进度颜色，如果不指定则使用应用主色调
  final Color? progressColor;
  
  /// 进度条渐变色，优先级高于progressColor
  final Gradient? progressGradient;

  /// 可选的子组件，显示在进度条中央
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size.w,
      height: size.w,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 饼状进度条
          CustomPaint(
            size: Size(size.w, size.w),
            painter: _PieProgressPainter(
              value: value,
              backgroundColor: backgroundColor,
              progressColor: progressColor ?? AppColor.primary,
              progressGradient: progressGradient ?? AppColor.primaryGradientHorizontal1,
            ),
          ),
          // 可选的中心内容
          if (child != null) child!,
        ],
      ),
    );
  }
}

/// 饼状进度条画笔
class _PieProgressPainter extends CustomPainter {
  _PieProgressPainter({
    required this.value,
    required this.backgroundColor,
    required this.progressColor,
    required this.progressGradient,
  });

  /// 当前进度，取值范围 [0.0-1.0]
  final double value;

  /// 背景颜色
  final Color backgroundColor;

  /// 进度颜色
  final Color progressColor;
  
  /// 进度条渐变色
  final Gradient progressGradient;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // 绘制背景圆
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度扇形
    if (value > 0) {
      final progressPaint = Paint()
        ..style = PaintingStyle.fill;
      
      // 创建矩形区域用于渐变色
      final rect = Rect.fromCircle(center: center, radius: radius);
      
      // 使用渐变色
      progressPaint.shader = progressGradient.createShader(rect);
      
      // 计算扇形角度，从顶部开始顺时针
      final startAngle = -pi / 2;
      final sweepAngle = 2 * pi * value.clamp(0.0, 1.0);

      // 绘制扇形
      canvas.drawArc(
        rect,
        startAngle,
        sweepAngle,
        true,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is _PieProgressPainter) {
      return oldDelegate.value != value ||
          oldDelegate.backgroundColor != backgroundColor ||
          oldDelegate.progressColor != progressColor ||
          oldDelegate.progressGradient != progressGradient;
    }
    return true;
  }
}
