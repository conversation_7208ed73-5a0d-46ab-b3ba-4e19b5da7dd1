import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/ui/style/color.dart';
import 'package:dnurse/ui/widgets/app_bar/dnu_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';

import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../../ui/icons/icons.dart';
import '../data_statistic/pages/log/data_log_page.dart';
import 'logic.dart';

class DataHomePage extends StatelessWidget {
  const DataHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    final controller = Get.find<DataHomeLogic>();
    final double appBarHeight = AppBar().preferredSize.height;
    Log.i("appBarHeight: $appBarHeight");
    return Scaffold(
      backgroundColor: AppColor.background2,
      appBar: getAppBar(
          backgroundColor: Colors.white,
          context: context,
          title: "数据日志",
          actions: [
            InkWell(
              onTap: () {
                AppRouter.push(Routes.sportRecord);
              },
              child: SizedBox(
                width: 44.sp,
                height: 44.sp,
                child: const Icon(DNUIconFont.jiashujia),
              ),
            ),
            InkWell(
              onTap: () {
                AppRouter.push(Routes.dataStatistic);
              },
              child: SizedBox(
                width: 44.sp,
                height: 44.sp,
                child: const Icon(DNUIconFont.shuju),
              ),
            )
          ]),
      body: const DataLogPage(
        tag: 'data_home',
        safeTop: false,
      ),
    );
  }
}
