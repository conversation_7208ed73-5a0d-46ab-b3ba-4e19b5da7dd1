
import '../entity/Data.dart';

/// FileName: DataChanged
///
/// @Author: ygc
/// @Date: 2022/7/28 16:48
/// @Description:

enum DataOperation {
  add,
  update,
  deleted,
}

class DataChanged {
  final DataOperation operation;
  final Data data;
  final Data? oldData;

  DataChanged.add({required this.data})
      : operation = DataOperation.add,
        oldData = null;

  DataChanged.update({required this.data, required this.oldData})
      : operation = DataOperation.update;

  DataChanged.deleted({required this.data})
      : operation = DataOperation.deleted,
        oldData = null;

  @override
  String toString() {
    return 'DataChanged: {data: $data, operation: $operation}';
  }
}
