/// FileName: spug_state_changed
///
/// @Author: Wei
/// @Date: 2024/7/09 11:21
/// @Description:

class BSStateChanged {
  BSStateChanged(this.state, this.data);

  /*
   * typedef NS_ENUM(NSI<PERSON><PERSON>, DNUTestStatus) {
      TEST_STATUS_WAITING_DEVICE_PLUGIN,
      TEST_STATUS_WAKING_UP_DEVICE,
      TEST_STATUS_RECOGNIZE_DEVICE,
      TEST_STATUS_GET_DEVICE_INFO,
      TEST_STATUS_DEVICE_CHECKE_FINISH,
      TEST_STATUS_PAPER_USED,
      TEST_STATUS_PAPER_INSERTED,
      TEST_STATUS_PAPER_OUT,
      TEST_STATUS_START_TEST,
      TEST_STATUS_TEST_COMPLETE,
      TEST_STATUS_NEED_CALIBRATION,
      TEST_STATUS_LOW_POWER,
      TEST_STATUS_CHECK_ERROR,
      TEST_STATUS_VOLTAGE_INFO,
      TEST_STATUS_TEMPERATURE_INFO,
      TEST_STATUS_SN_INFO,
      TEST_STATUS_SWVER_INFO,
      TEST_STATUS_UNKNOW_CAUSE_ERROR,
      TEST_STATUS_TIME_OUT_DEVICE_SLEEP,
      TEST_STATUS_TEMPERATURE_LOW_ERROR,
      TEST_STATUS_TEMPERATURE_HIGH_ERROR,
      TEST_STATUS_TEST_TIMEOUT,
      TEST_STATUS_TIMEOUT_UPDATE,
      TEST_STATUS_REC_DENY,
      TEST_STATUS_REC_PENDING,
      };
   */

  final int state;  //0,设备未插入；1,插入设备；2,识别设备；3,获取设备信息；4,设备检测完成；5,试纸已使用；6,试纸插入；7,试纸拔出；8,开始测量；9,测量完成
  final dynamic data;
}
