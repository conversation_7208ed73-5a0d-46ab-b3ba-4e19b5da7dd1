class FoodRecordModel {
  final String tag;
  final String name;
  final String calories;
  final String image;
  final String abstract;
  final int starNum;
  final String gi;
  final String purine;
  final String carbohydrate;
  final int id;
  int count; // count属性现在是一个非final的成员变量

  // 构造函数现在包括count参数，并设置默认值为0
  FoodRecordModel(
    this.tag,
    this.name,
    this.calories,
    this.image,
    this.abstract,
    this.starNum,
    this.gi,
    this.purine,
    this.carbohydrate,
    this.id, {
    this.count = 0,
  });

  @override
  String toString() {
    return 'FoodRecordModel{classValue: $tag, name: $name, calories: $calories, imageurl: $image, abstract: $abstract, starnum: $starNum, gi: $gi, purine: $purine, carbohydrate: $carbohydrate, did: $id}';
  }

  // 方法来设置count的值
  void setCount(int newCount) {
    count = newCount;
  }

  // 饮食记录
  String getTabCategory() {
    if (tag == '谷薯类') return '常用主食';
    if (['蔬菜类', '水果类', '菌藻类'].contains(tag)) return '常用蔬果';
    if (['禽畜肉类', '蛋奶类', '豆类'].contains(tag)) return '常用肉蛋豆';
    if (tag == '海鲜类') return '常用海鲜';
    if (['坚果类', '小吃糕点', '糖果类'].contains(tag)) return '常用小吃';
    if (['油脂类', '调料类', '饮料酒类'].contains(tag)) return '酒品调料';
    return ''; // 如果不属于上述分类，返回空字符串，可根据实际情况调整处理方式
  }
}
