/// FileName: data_drug
///
/// @Author: ygc
/// @Date: 2025/7/7 15:33
/// @Description:
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:intl/intl.dart';

import '../../../resource/dnu_assets.dart';
import '../../user/entity/drug_type.dart';
import 'Data.dart';
part 'data_drug.g.dart';

@CopyWith()
class DataDrug extends Data {
  DataDrug({
    required super.did,
    required super.time,
    super.id,
    required super.sn,
    required this.name,
    required this.amount,
    required this.fromUser,
    this.uuid,
    this.isPlan = false,
    this.insulinkPenName,
    required this.drugType,
  });


  bool isPlan;
  bool fromUser;
  String name;
  num amount;
  DrugType drugType;
  String? uuid;
  String? insulinkPenName;

  String getTimeString() {
    return time.formatDate("HH:mm");
  }

  String getTimeFullString() {
    return time.formatDate("yyyy-MM-dd HH:mm");
  }

  String getAmount() {
    return "${amount.toStringAsFixed(1)} ${drugType.unit}";
  }

  @override
  String toString() {
    return "DataDrug { id:$id, did:$did, amount:$amount, time:$time, drugType:$drugType , isPlan:$isPlan, uuid:$uuid, insulinkPenName:$insulinkPenName}";
  }

  @override
  String stringValue() {
    return amount.toStringAsFixed(1);
  }

  @override
  String originalValue() {
    return amount.toStringAsFixed(1);
  }

  @override
  String unit() {
    return drugType.unit;
  }

  @override
  String title() {
    return name;
  }

  @override
  String message() {
    return DateFormat("HH:mm").format(time);
  }

  @override
  String icon() {
    return DNUAssets.to.images.data.food;
  }
}
