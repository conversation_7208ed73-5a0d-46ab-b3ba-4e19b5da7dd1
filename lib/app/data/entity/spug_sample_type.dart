
import 'package:dnurse/ui/icons/icons.dart';
import 'package:flutter/cupertino.dart';

@immutable
class SPUGSampleType {

  final int value;
  final String name;
  final IconData icon;

  const SPUGSampleType(this.value, this.name, this.icon);

  static SPUGSampleType BLOOD = const SPUGSampleType(0, '血液', DNUIconFont.shouzhixie,);
  static SPUGSampleType MOCK_BLOOD = const SPUGSampleType(1, '质控液', DNUIconFont.diguan1,);
  static SPUGSampleType STANDARD_PAPER = const SPUGSampleType(15, '电子试纸', DNUIconFont.shitiao1,);

  static SPUGSampleType getSampleTypeByValue(int v) {
    switch (v) {
      case 0:
        return BLOOD;
      case 1:
        return MOCK_BLOOD;
      case 15:
        return STANDARD_PAPER;
    }

    return BLOOD;
  }

}