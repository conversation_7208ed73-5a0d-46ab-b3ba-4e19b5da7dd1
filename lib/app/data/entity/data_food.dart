import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:dnurse/app/data/common/food_type.dart';
import 'package:dnurse/app/data/entity/data_food_item.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../../common/entity/unit.dart';
import 'Data.dart';

/// FileName: data_food
///
/// @Author: Wei
/// @Date: 2024/6/23 09:47

part 'data_food.g.dart';

@CopyWith()
class DataFood extends Data {
  DataFood({
    required super.did,
    required super.time,
    super.id,
    required super.sn,
    required this.calorie,
    required this.foodType,
    required this.items,
  });

  int calorie;
  FoodType foodType;
  List<DataFoodItem> items;

  bool get isEmpty {
    return items.isEmpty;
  }
  bool get isNotEmpty {
    return items.isNotEmpty;
  }

  @override
  String toString() {
    return "DataFood { id:$id, did:$did, time:$time, foodType:$foodType, items:$items }";
  }

  @override
  String stringValue() {
    return "+$calorie";
  }

  @override
  String unit() {
    return "Kcal";
  }

  @override
  String title() {
    return foodType.title;
  }

  @override
  String message() {
    return time.formatDate('HH:mm');
  }

  @override
  String icon() {
    return 'assets/images/data/food.png';
  }
}
