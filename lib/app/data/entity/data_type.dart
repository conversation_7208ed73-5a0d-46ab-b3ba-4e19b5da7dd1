
import 'package:flutter/cupertino.dart';

// 0、血糖； 1、尿酸
@immutable
class DataType {

  final int value;
  final String name;

  const DataType(this.value, this.name);

  static const DataType glucose = DataType(0, '血糖');
  static const DataType ua = DataType(1, '尿酸');
  static const DataType food = DataType(2, '饮食');
  static const DataType sport = DataType(3, '运动');
  static const DataType drug = DataType(4, '用药');
  static const DataType bloodPressure = DataType(5, '血压');

  static DataType getDataFromByValue(int v) {
    switch (v) {
      case 1:
        return ua;
      case 2:
        return food;
      case 3:
        return sport;
      case 4:
        return drug;
      case 5:
        return bloodPressure;
    }

    return glucose;
  }

}