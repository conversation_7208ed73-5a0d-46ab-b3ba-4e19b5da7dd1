/// FileName: device_sync_info
///
/// @Author: ygc
/// @Date: 2024/9/30 10:24
/// @Description:

class DeviceSyncDevice {
  final int id;

  const DeviceSyncDevice._(this.id);

  static DeviceSyncDevice insulink = const DeviceSyncDevice._(0);
  static DeviceSyncDevice contour = const DeviceSyncDevice._(1);

  static DeviceSyncDevice? getById(int id) {
    if(id == insulink.id) {
      return insulink;
    } else if(id == contour.id) {
      return contour;
    }

    return null;
  }
  @override
  String toString() {
    return "DeviceSyncDevice{id: $id}";
  }
}

class DeviceSyncInfo {
  final int? id;
  final String did;
  final String deviceSn;
  final DeviceSyncDevice device;

  DeviceSyncInfo({
    this.id,
    required this.did,
    required this.deviceSn,
    required this.device,
  });

  @override
  String toString() {
    return 'DeviceSyncInfo{id: $id, did: $did, deviceSn: $deviceSn, device: $device}';
  }
}
