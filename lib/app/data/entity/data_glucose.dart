import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../resource/dnu_assets.dart';
import '../../../router/router.dart';
import '../../../router/routes.dart';
import '../../user/entity/unit/glucose_unit.dart';
import '../../user/service/user_config_service.dart';
import '../common/data_status.dart';
import '../common/data_target.dart';
import '../common/data_utils.dart';
import '../common/time_point.dart';
import '../service/glucose_service.dart';
import 'Data.dart';
import 'data_extra.dart';
import 'data_from.dart';
import 'data_source.dart';

/// FileName: data_target
///
/// @Author: ygc
/// @Date: 2022/6/23 09:47
/// @Description: 控糖目标

/// 时间段目标值
part 'data_glucose.g.dart';

@CopyWith()
class DataGlucose extends Data {
  static const double moleToMgRatio = 18.0;
  static const double minValue = 1.1;
  static const double maxValue = 33.3;

  static NumberFormat moleFormat = NumberFormat("###0.0#", "en_US");
  static NumberFormat mgFormat = NumberFormat("###", "en_US");

  DataGlucose({
    required String did,
    required String sn,
    required this.mmolValue,
    required this.timePoint,
    required DateTime time,
    int id = 0,
    this.source = DataSource.Device_V1,
    this.dataFrom = DataFrom.DataFrom_Add,
    DataExtra? extra,
    this.remarkExtra = '',
  })  : extra = extra ?? DataExtra(),
        super(id: id, did: did, time: time, sn: sn);

  static DataGlucose getEmpty() {
    return DataGlucose(
        did: '',
        sn: AppContext.to.userSn,
        mmolValue: 0,
        timePoint: TimePoint.breakfastBefore,
        time: DateTime.now());
  }

  TimePoint timePoint;
  double mmolValue;
  DataSource source;
  DataFrom dataFrom;
  DataExtra extra;
  String remarkExtra;

  bool get isValidate {
    return mmolValue > 0;
  }

  String formatValue(GlucoseUnit unit) {
    return formatMole(mmolValue, unit);
  }

  static String formatMole(double value, GlucoseUnit unit,
      {bool withHighLow = true}) {
    if (value <= 0) {
      return '';
    }

    if (withHighLow && value < DataGlucose.minValue) {
      return "LOW";
    } else if (withHighLow && value > DataGlucose.maxValue) {
      return "HIGH";
    } else {
      if (value < minValue) {
        value = minValue;
      } else if (value > maxValue) {
        value = maxValue;
      }

      if (unit == GlucoseUnit.mole) {
        return moleFormat.format(value);
      } else {
        var temp = value * moleToMgRatio;
        return mgFormat.format(temp);
      }
    }
  }

  static double displayValue(double value, GlucoseUnit unit) {
    double v = value;
    if (value < DataGlucose.minValue) {
      v = DataGlucose.minValue;
    } else if (value > DataGlucose.maxValue) {
      v = DataGlucose.maxValue;
    }

    if (unit == GlucoseUnit.mole) {
      return v;
    } else {
      return v * moleToMgRatio;
    }
  }

  static String formatMg(double value, GlucoseUnit unit) {
    if (unit == GlucoseUnit.mole) {
      var temp = value / moleToMgRatio;
      return moleFormat.format(temp);
    } else {
      return moleFormat.format(value);
    }
  }

  void generateDid() {
    did = DataUtils.generateDidWithPrefix('BS');
  }

  // static DataValueStatus getStatus(double value, PointTarget pointTarget) {
  //   if (value < pointTarget.mini) {
  //     return DataValueStatus.low;
  //   } else if (value > pointTarget.max) {
  //     return DataValueStatus.high;
  //   }
  //   return DataValueStatus.normal;
  // }

  @override
  String toString() {
    return "GlucoseValue { id:$id, did:$did, value:$mmolValue, time:$time, timePoint:$timePoint , source:$source, dataFrom:$dataFrom, extra:$extra, remarkExtra:$remarkExtra}";
  }

  @override
  String stringValue() {
    return DataGlucose.formatMole(
        mmolValue, UserConfigService.to.glucoseUnit.value,
        withHighLow: true);
  }

  @override
  String originalValue() {
    return '$mmolValue';
  }

  @override
  String unit() {
    return UserConfigService.to.glucoseUnit.value.name;
  }

  @override
  String title() {
    return timePoint.name;
  }

  @override
  String message() {
    return DateFormat("HH:mm").format(time);
  }

  @override
  String icon() {
    return DNUAssets.to.images.data.test;
  }

  DataValueStatus getValueStatus(DataTarget dataTarget) {
    return DataUtils.getDataValueStatus(mmolValue, timePoint, dataTarget);
  }

  @override
  void gotoEdit() {
    AppRouter.push(Routes.dataAddBS, arguments: {'editData': this});
  }

  //获取血糖仪表盘进度
  @override
  double getProgressValue() {
    if (isValidate) {
      if (mmolValue < DataGlucose.minValue) {
        return 0;
      } else if (mmolValue > DataGlucose.maxValue) {
        return 1;
      } else {
        return (mmolValue - DataGlucose.minValue) /
            (DataGlucose.maxValue - DataGlucose.minValue);
      }
    }

    return 0;
  }

  @override
  Gradient getStatusGradientColor() {
    return getValueStatus(GlucoseService.to.currentDataTarget.value)
        .gradientColor;
  }

  @override
  Color getStatusColor() {
    return getValueStatus(GlucoseService.to.currentDataTarget.value).color;
  }

  @override
  DataSource getDataSource() {
    return source;
  }

  @override
  String sourceStr() {
    if (DataFrom.DataFrom_Add == dataFrom) {
      return '手动添加';
    }

    return source.name;
  }

  @override
  TimePoint getTimePoint() {
    return timePoint;
  }

  @override
  String getMaxValueStr() {
    return DataGlucose.formatMole(
        maxValue, UserConfigService.to.glucoseUnit.value,
        withHighLow: false);
  }

  @override
  String getMiniValueStr() {
    return DataGlucose.formatMole(
        minValue, UserConfigService.to.glucoseUnit.value,
        withHighLow: false);
  }
}
