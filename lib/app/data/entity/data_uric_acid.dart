/// FileName: data_uric_acid
///
/// @Author: ygc
/// @Date: 2024/8/13 13:57
/// @Description:
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:dnurse/app/data/common/uric_acid_target.dart';
import 'package:dnurse/app/user/entity/unit/uric_acid_unit.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../router/router.dart';
import '../../../router/routes.dart';
import '../../user/service/user_config_service.dart';
import '../common/data_utils.dart';
import '../common/time_point.dart';
import '../common/uric_acid_data_status.dart';
import '../service/uric_acid_service.dart';
import 'Data.dart';
import 'data_from.dart';
import 'data_source.dart';

part 'data_uric_acid.g.dart';

@CopyWith()
class DataUricAcid extends Data {
  static const double moleToMgRatio = 0.016806;
  static const int minValue = 2;
  static const int maxValue = 1188;

  static NumberFormat moleFormat = NumberFormat("####", "en_US");
  static NumberFormat mgFormat = NumberFormat("####0.0#", "en_US");

  DataUricAcid({
    required String did,
    required String sn,
    required this.mmolValue,
    required this.timePoint,
    required DateTime time,
    int id = 0,
    DataSource source = DataSource.Device_Spug,
    DataFrom dataFrom = DataFrom.DataFrom_Add,
  }) : super(id: id, did: did, time: time, sn: sn);

  static DataUricAcid getEmpty() {
    return DataUricAcid(
        did: '',
        sn: AppContext.to.userSn,
        mmolValue: 0,
        timePoint: TimePoint.breakfastBefore,
        time: DateTime.now());
  }

  TimePoint timePoint;
  int mmolValue;
  DataSource source = DataSource.Device_Spug;
  DataFrom dataFrom = DataFrom.DataFrom_Device;

  bool get isValidate {
    return mmolValue > 0;
  }

  String formatValue(UricAcidUnit unit) {
    return formatMole(mmolValue, unit);
  }

  static String formatMole(int value, UricAcidUnit unit,
      {bool withHighLow = true}) {

    if (value <= 0) {
      return "";
    }

    if (withHighLow && value < DataUricAcid.minValue) {
      return "LOW";
    } else if (withHighLow && value > DataUricAcid.maxValue) {
      return "HIGH";
    } else {
      if (value < minValue) {
        value = minValue;
      } else if (value > maxValue) {
        value = maxValue;
      }

      if (unit == UricAcidUnit.mole) {
        return moleFormat.format(value);
      } else {
        var temp = value * moleToMgRatio;
        return mgFormat.format(temp);
      }
    }
  }

  static String formatMg(double value, UricAcidUnit unit) {
    if (unit == UricAcidUnit.mole) {
      var temp = value / moleToMgRatio;
      return moleFormat.format(temp);
    } else {
      return moleFormat.format(value);
    }
  }

  static UricAcidDataValueStatus getStatus(
      int value, UricAcidTarget pointTarget) {
    if (value < pointTarget.low) {
      return UricAcidDataValueStatus.low;
    } else if (value > pointTarget.high) {
      return UricAcidDataValueStatus.high;
    }
    return UricAcidDataValueStatus.normal;
  }

  void generateDid() {
    did = DataUtils.generateDidWithPrefix('UA');
  }

  @override
  String toString() {
    return "DataUricAcid { id:$id, did:$did, value:$mmolValue, time:$time, timePoint:$timePoint }";
  }

  @override
  String stringValue() {
    return DataUricAcid.formatMole(
        mmolValue, UserConfigService.to.uricAcidUnit.value,
        withHighLow: true);
  }

  @override
  String originalValue() {
    return '$mmolValue';
  }

  @override
  String unit() {
    return UserConfigService.to.uricAcidUnit.value.name;
  }

  @override
  String title() {
    return timePoint.name;
  }

  @override
  String message() {
    return DateFormat("HH:mm").format(time);
  }

  @override
  String icon() {
    return DNUAssets.to.images.data.uricAcid;
  }

  @override
  void gotoEdit() {
    AppRouter.push(Routes.dataAddUa, arguments: {'editData': this});
  }

  UricAcidDataValueStatus getValueStatus(UricAcidTarget dataTarget) {
    return getStatus(mmolValue, dataTarget);
  }

  static double displayValue(int value, UricAcidUnit unit) {
    int v = value;
    if (v < minValue) {
      v = minValue;
    } else if (v > maxValue) {
      v = maxValue;
    }

    if (unit == UricAcidUnit.mole) {
      return v.toDouble();
    } else {
      return v * moleToMgRatio;
    }
  }

  static double displayChartValue(double value, UricAcidUnit unit) {
    double v = value;
    if (v < minValue) {
      v = minValue.toDouble();
    } else if (v > maxValue) {
      v = maxValue.toDouble();
    }

    if (unit == UricAcidUnit.mole) {
      return v.toDouble();
    } else {
      return v * moleToMgRatio;
    }
  }

  //获取血糖仪表盘进度
  @override
  double getProgressValue() {
    if (isValidate) {
      if (mmolValue < DataUricAcid.minValue) {
        return 0;
      } else if (mmolValue > DataUricAcid.maxValue) {
        return 1;
      } else {
        return (mmolValue - DataUricAcid.minValue) /
            (DataUricAcid.maxValue - DataUricAcid.minValue);
      }
    }

    return 0;
  }

  @override
  Gradient getStatusGradientColor() {
    return getValueStatus(UricAcidService.to.currentUricAcidDataTarget.value)
        .gradientColor;
  }

  @override
  Color getStatusColor() {
    return getValueStatus(UricAcidService.to.currentUricAcidDataTarget.value)
        .color;
  }

  @override
  DataSource getDataSource() {
    return source;
  }

  @override
  String sourceStr() {
    if (DataFrom.DataFrom_Add == dataFrom) {
      return '手动添加';
    }

    return source.name;
  }

  @override
  TimePoint getTimePoint() {
    return timePoint;
  }

  @override
  String getMaxValueStr() {
    return DataUricAcid.formatMole(maxValue, UserConfigService.to.uricAcidUnit.value, withHighLow: false);
  }

  @override
  String getMiniValueStr() {
    return DataUricAcid.formatMole(minValue, UserConfigService.to.uricAcidUnit.value, withHighLow: false);
  }

}
