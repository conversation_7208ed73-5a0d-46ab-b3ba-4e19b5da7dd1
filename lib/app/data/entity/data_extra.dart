/// FileName: data_extra
///
/// @Author: ygc
/// @Date: 2024/9/29 18:10
/// @Description:
///

import 'package:freezed_annotation/freezed_annotation.dart';

part 'data_extra.g.dart';

@JsonSerializable()
class DataExtra {
  int? appVersion;
  int? deviceVersion;
  int? deviceSwVersion;
  String? deviceSn;
  double? deviceVoltage;
  double? deviceTemp;
  String? imei;
  String? phone;

  DataExtra({
    this.appVersion,
    this.deviceVersion,
    this.deviceSwVersion,
    this.deviceSn,
    this.deviceVoltage,
    this.deviceTemp,
    this.imei,
    this.phone,
  });

  factory DataExtra.fromJson(Map<String, dynamic> json) =>
      _$DataExtraFromJson(json);

  Map<String, dynamic> toJson() => _$DataExtraToJson(this);

  @override
  String toString() {
    return "DataExtra{appVersion: $appVersion, deviceVersion: $deviceVersion, deviceSwVersion: $deviceSwVersion, deviceSn: $deviceSn, deviceVoltage: $deviceVoltage, deviceTemp: $deviceTemp, imei: $imei, phone: $phone}";
  }
}
