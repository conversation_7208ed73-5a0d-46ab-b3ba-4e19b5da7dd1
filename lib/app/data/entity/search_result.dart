class SearchResult {
  String category;
  int type;
  List<SearchResultItem> items;
  bool isExpanded;

  SearchResult(
      {required this.category,
      required this.type,
      required this.items,
      this.isExpanded = false});
}

class SearchResultItem {
  final String id;
  final int type;
  final String content;
  final String abstract;

  SearchResultItem({
    required this.id,
    required this.type,
    required this.content,
    this.abstract = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'content': content,
      'abstract': abstract,
    };
  }

  factory SearchResultItem.fromMap(Map<String, dynamic> map) {
    return SearchResultItem(
      id: map['id'],
      type: map['type'],
      content: map['content'],
      abstract: map['abstract'],
    );
  }

  @override
  String toString() {
    return 'SearchResultItem(id: $id, type: $type, content: $content, abstract: $abstract)';
  }
}
