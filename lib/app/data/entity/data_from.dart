
import 'package:flutter/cupertino.dart';

// 1、手输； 2、机测
@immutable
class DataFrom {

  final int value;
  final String name;

  const DataFrom(this.value, this.name);

  static const DataFrom DataFrom_None = DataFrom(0, '无');
  static const DataFrom DataFrom_Add = DataFrom(1, '手输');
  static const DataFrom DataFrom_Device = DataFrom(2, '机测');
  static const DataFrom DataFrom_Other = DataFrom(2, '其他'); //Android备注 shealth，iOS备注SPUG

  static DataFrom getDataFromByValue(int v) {
    switch (v) {
      case 1:
        return DataFrom_Add;
      case 2:
        return DataFrom_Device;
      case 3:
        return DataFrom_Other;
    }

    return DataFrom_None;
  }

}