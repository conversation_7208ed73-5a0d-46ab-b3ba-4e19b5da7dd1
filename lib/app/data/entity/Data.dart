
import 'package:dnurse/app/data/entity/data_source.dart';
import 'package:flutter/material.dart';

import '../common/data_status.dart';
import '../common/time_point.dart';

/// FileName: Data
///
/// @Author: ygc
/// @Date: 2022/7/15 14:06
/// @Description:

class Data {
  final int id;
  String did;
  final String sn;  //用户身份标识
  late DateTime time;

  Data({
    required this.did,
    required this.time,
    required this.sn,
    this.id = 0,
  });

  String unit() {
    return "";
  }

  String stringValue() {
    return "";
  }

  String originalValue() {
    return "";
  }

  String title() {
    return "";
  }

  String message() {
    return "";
  }

  String icon() {
    return 'assets/images/data/test.png';
  }

  void gotoEdit() {}

  double getProgressValue() {
    return 0;
  }

  Gradient getStatusGradientColor() {
    return DataValueStatus.normal.gradientColor;
  }

  Color getStatusColor() {
    return DataValueStatus.normal.color;
  }

  DataSource getDataSource() {
    return DataSource.Device_Other;
  }

  String sourceStr() {
    return '手动添加';
  }

  TimePoint getTimePoint() {
    return TimePoint.random;
  }

  String getMaxValueStr() {
    return '';
  }

  String getMiniValueStr() {
    return '';
  }


}
