
import 'package:flutter/cupertino.dart';

//1、杏；2、金；3、荷；4、SPUG；5、GSR扫描
@immutable
class DataSource {

  final int value;
  final String name;

  const DataSource._(this.value, this.name);

  static const DataSource Device_V1 = DataSource._(0, '手机血糖仪');
  static const DataSource Device_Apricot = DataSource._(1, '杏');
  static const DataSource Device_Gold = DataSource._(2, '金');
  static const DataSource Device_Ge = DataSource._(3, '荷');
  static const DataSource Device_Spug = DataSource._(4, 'SPUG');
  static const DataSource Device_GSR = DataSource._(5, 'GSR扫描');
  static const DataSource Device_uAnJin = DataSource._(6, '优安进');



  static const DataSource Device_Other = DataSource._(9999, '未知/默认值');

  static DataSource getDataSourceByValue(int v) {
    switch (v) {
      case 1:
        return Device_Apricot;
      case 2:
        return Device_Gold;
      case 3:
        return Device_Ge;
      case 4:
        return Device_Spug;
      case 5:
        return Device_GSR;
      case 6:
        return Device_uAnJin;
    }

    return Device_V1;
  }

  @override
  String toString() {
    return "DataSource{value:$value, name:$name}";
  }
}