/// FileName: reminder_plan
///
/// @Author: ygc
/// @Date: 2024/7/31 11:30
/// @Description:
///
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';

import '../../common/time_point.dart';

part 'reminder_plan.g.dart';

int reminderAllDayOpen = 0x7F;

@CopyWith()
class ReminderPlan with EquatableMixin {
  ReminderPlan({
    required this.hour,
    required this.minute,
    required this.timePoint,
    this.enabled = 0,
    this.repeated = 0,
    this.id = 0,
  });

  int id;

  int hour;
  int minute;
  int enabled;
  int repeated;
  TimePoint timePoint;

  bool checkEnable(int index) {
    return enabled & (1 << index) != 0;
  }

  void setEnable(int index, bool enable) {
    if (enable) {
      enabled |= 1 << index;
    } else {
      enabled &= ~(1 << index);
    }
  }

  void enableAll() {
    enabled = reminderAllDayOpen;
  }

  bool get isAllEnabled => enabled == reminderAllDayOpen;

  String get time =>
      "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}";

  @override
  String toString() {
    return "ReminderPlan{timePoint: $timePoint, hour: $hour, minute: $minute, enabled: $enabled, repeated:$repeated}";
  }

  static List<int> defaultHours = [0, 6, 9, 11, 14, 17, 20, 22, 3];
  static List<int> defaultMinutes = [0, 30, 0, 30, 0, 30, 0, 0, 0];

  static ReminderPlan getDefault(TimePoint timePoint) {
    return ReminderPlan(
      timePoint: timePoint,
      hour: defaultHours[timePoint.id],
      minute: defaultMinutes[timePoint.id],
      enabled: 0,
    );
  }

  Map<String, dynamic> getData() {
    return {
      "hour": hour,
      "minute": minute,
      "enable": enabled,
      "repeated": enabled,
      "timePoint": timePoint.id,
    };
  }

  @override
  List<Object?> get props => [
        hour,
        minute,
        enabled,
        repeated,
        timePoint,
      ];
}
