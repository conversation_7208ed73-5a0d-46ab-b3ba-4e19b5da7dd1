/// FileName: data_sport
///
/// @Author: ygc
/// @Date: 2025/7/16 09:41
/// @Description:
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/resource/dnu_assets.dart';
import 'package:intl/intl.dart';

import '../../user/entity/from_type.dart';
import '../common/data_utils.dart';
import 'Data.dart';

part 'data_sport.g.dart';

@CopyWith()
class DataSport extends Data {
  String name;
  int calorie;
  int duration;
  FromType fromType;
  bool isFromWalk;
  String imageUrl;

  DataSport({
    super.id,
    required super.did,
    required super.time,
    required super.sn,
    required this.name,
    required this.calorie,
    required this.duration,
    required this.fromType,
    this.isFromWalk = false,
    this.imageUrl = '',
  });

  static String generateDid() {
    return DataUtils.generateDidWithPrefix('DS');
  }

  String sportTime() {
    return time.formatDate(DateFormat.HOUR24_MINUTE);
  }

  @override
  String stringValue() {
    return "-$calorie";
  }

  @override
  String unit() {
    return "Kcal";
  }

  @override
  String title() {
    return name;
  }

  @override
  String message() {
    return time.formatDate('HH:mm');
  }

  @override
  String icon() {
    return DNUAssets.to.images.data.sport;
  }
}
