import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, 0); // 左上
    path.lineTo(size.width, 0); // 右上
    path.lineTo(size.width / 2, size.height); // 下顶点
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class TriangleWidget extends StatelessWidget {
  final double width;
  final double height;
  final Color color;

  const TriangleWidget({
    super.key,
    required this.width,
    required this.height,
    this.color = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: <PERSON><PERSON>(width, height),
      painter: <PERSON><PERSON><PERSON><PERSON>(color: color),
    );
  }
}
