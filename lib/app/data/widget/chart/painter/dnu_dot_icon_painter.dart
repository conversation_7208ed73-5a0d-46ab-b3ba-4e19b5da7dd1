/// FileName: dnu_dot_icon_painter
///
/// @Author: ygc
/// @Date: 2024/8/15 18:24
/// @Description:
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class DNUDotIconPainter extends FlDotPainter {
  DNUDotIconPainter({
    Color? color,
    double? size,
    required this.icon,
  })  : color = color ?? Colors.green,
        size = size ?? 10.0;

  /// The fill color to use for the circle
  Color color;

  /// Customizes the radius of the circle
  double size;

  IconData icon;

  /// Implementation of the parent class to draw the circle
  @override
  void draw(Canvas canvas, FlSpot spot, Offset offsetInCanvas) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(icon.codePoint),
        style: TextStyle(
          color: color,
          fontSize: size,
          fontFamily: icon.fontFamily,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    textPainter.paint(canvas, offsetInCanvas.translate(- textPainter.width / 2, - textPainter.height / 2),);
  }

  /// Implementation of the parent class to get the size of the circle
  @override
  Size getSize(FlSpot spot) {
    return Size(size, size);
  }

  /// Used for equality check, see [EquatableMixin].
  @override
  List<Object?> get props => [
        color,
        size,
        icon,
      ];

  @override
  FlDotPainter lerp(FlDotPainter a, FlDotPainter b, double t) {
    // TODO: implement lerp
    throw UnimplementedError();
  }

  @override
  // TODO: implement mainColor
  Color get mainColor => throw UnimplementedError();
}
