/// FileName: data_chart_widget
///
/// @Author: ygc
/// @Date: 2024/8/15 14:33
/// @Description:
import 'package:dnurse/app/data/widget/chart/painter/dnu_dot_icon_painter.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/ui/icons/icons.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:intl/intl.dart';

import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';
import '../../common/data_target.dart';
import '../../common/time_point.dart';

enum DataChartDotType {
  circle,
  icon,
}

class DataChartValue {
  DataChartValue({
    required this.dateTime,
    this.value = 0,
    required this.timePoint,
    this.tag,
  });

  final DateTime dateTime;
  final double value;
  final TimePoint timePoint;
  final dynamic tag;

  @override
  String toString() {
    // TODO: implement toString
    return "GlucoseChartData{dateTime:$dateTime, value:$value, timePoint:$timePoint, tag:$tag}";
  }
}

class DataChartLine {
  DataChartLine(
    this.values, {
    this.lineColor,
    this.lineWidth = 0,
    this.dotColor,
    this.dotSize,
    this.dotIcon,
    this.dotType = DataChartDotType.circle,
    this.dotColorCallback,
    this.showDot = true,
    this.isCurved = false,
  });

  final Color? lineColor;
  final double lineWidth;
  final bool isCurved;

  final bool showDot;
  final Color? dotColor;
  final double? dotSize;
  final IconData? dotIcon;
  final DataChartDotType dotType;
  final DataLinePointColor? dotColorCallback;

  final List<DataChartValue> values;
}

typedef DataLinePointColor = Color Function(
  int pointTime,
  int lineIndex,
  int dataIndex,
  dynamic tag,
);
typedef DataLineTipMessage = String Function(
  int lineIndex,
  DateTime dateTime,
  DataChartValue data,
);

class DataChartWidget extends StatelessWidget {
  const DataChartWidget({
    super.key,
    required this.pointTarget,
    required this.lines,
    required this.startTime,
    required this.endTime,
    required this.xAxisInterval,
    required this.yAxisMin,
    required this.yAxisMax,
    required this.yAxisInterval,
    this.lineTipMessage,
    this.xAxisDateFormat,
    this.yAxisDateFormat,
    this.showGrid = false,
  });

  final PointTarget pointTarget;
  final List<DataChartLine> lines;
  final DateTime startTime;
  final DateTime endTime;
  final double xAxisInterval;
  final double yAxisMin;
  final double yAxisMax;
  final double yAxisInterval;
  final DataLineTipMessage? lineTipMessage;
  final String? xAxisDateFormat;
  final NumberFormat? yAxisDateFormat;
  final bool showGrid;

  @override
  Widget build(BuildContext context) {
    double miniY = yAxisMin;
    double maxY = yAxisMax;

    // return const Placeholder();
    return Padding(
      padding: EdgeInsets.zero,
      child: LineChart(
        _buildLineChartData(miniY, maxY),
        // swapAnimationDuration: const Duration(milliseconds: 250),
      ),
    );
  }

  LineChartData _buildLineChartData(double miniY, double maxY) {
    List<LineChartBarData> lineBarsData = getLineData(miniY, maxY);
    return LineChartData(
      lineTouchData: LineTouchData(
        handleBuiltInTouches: true,
        touchTooltipData: LineTouchTooltipData(
          // showOnTopOfTheChartBoxArea: true,
          // tooltipBgColor: AppColor.textDesc,
          tooltipRoundedRadius: 8,
          getTooltipItems: (List<LineBarSpot> lineBarsSpot) {
            return lineBarsSpot.map((lineBarSpot) {
              // Log.i("getTooltipItems: $lineBarSpot");
              // Log.i("getTooltipItems barIndex: ${lineBarSpot.barIndex}");
              // Log.i("getTooltipItems spotIndex: ${lineBarSpot.spotIndex}");
              DataChartValue point =
                  lines[lineBarSpot.barIndex - 1].values[lineBarSpot.spotIndex];
              // Log.i("getTooltipItems point: $point");
              DateTime time =
                  DateTime.fromMillisecondsSinceEpoch(lineBarSpot.x.toInt());

              String? tipMessage =
                  lineTipMessage?.call(lineBarSpot.barIndex - 1, time, point);
              tipMessage ??=
                  "${lineBarSpot.y}\n${time.formatDate("M/d HH:mm")}";

              return LineTooltipItem(
                tipMessage,
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            }).toList();
          },
        ),
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          int lineIndex = lineBarsData.indexOf(barData);
          return spotIndexes.map((index) {
            return TouchedSpotIndicatorData(
              FlLine(
                strokeWidth: 0,
                dashArray: [2, 5],
                color: AppColor.primaryLight,
              ),
              FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  // int dataIndexIndex = lineIndex - 1;
                  // int lineIndex2 = lineBarsData.indexOf(barData);

                  // Log.i("dotType === 1: $dataIndexIndex, $lineIndex2");
                  // if (dataIndexIndex >= 0 && dataIndexIndex < lines.length) {
                  //   GlucoseChartLine e = lines[dataIndexIndex];
                  //   Color c;
                  //   if (e.dotColorCallback != null) {
                  //     c = e.dotColorCallback!(spot.x.toInt());
                  //   } else {
                  //     c = e.dotColor ?? Colors.white;
                  //   }
                  //
                  //   GlucoseChartDotType dotType = e.dotType;
                  //   Log.i("dotType === : $dotType");
                  //
                  //   if (dotType == GlucoseChartDotType.icon) {
                  //     return DNUDotIconPainter(
                  //       icon: e.dotIcon ?? DNUIconFont.shixinxiedi,
                  //       size: (e.dotSize ?? 20) * 1.1,
                  //       color: c,
                  //     );
                  //   } else {
                  //     return FlDotCirclePainter(
                  //       radius: (e.dotSize ?? 6) * 1.1,
                  //       color: c,
                  //       strokeColor: c,
                  //     );
                  //   }
                  // }

                  return FlDotCirclePainter(
                    radius: 6,
                    color: Colors.red.withOpacity(0.4),
                    strokeWidth: 0,
                  );
                },
              ),
            );
          }).toList();
        },
      ),
      gridData: FlGridData(
        show: showGrid,
        drawHorizontalLine: true,
        horizontalInterval: yAxisInterval,
        drawVerticalLine: false,
        getDrawingHorizontalLine: (value) {
          return FlLine(
              color: const Color(0xFFE5E5E5),
              strokeWidth: 1,
              dashArray: [4, 2]);
        },
      ),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: bottomTitles,
        ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          sideTitles: leftTitles(),
          // sideTitles: SideTitles(showTitles: false),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: const Border(
          bottom: BorderSide(color: Color(0xFFA6A6A6), width: 1),
          left: BorderSide(color: Colors.transparent),
          right: BorderSide(color: Colors.transparent),
          top: BorderSide(color: Color(0xFFE5E5E5), width: 1),
        ),
      ),
      lineBarsData: lineBarsData,
      minX: startTime.millisecondsSinceEpoch.toDouble(),
      maxX: endTime.millisecondsSinceEpoch.toDouble(),
      maxY: maxY,
      minY: miniY,
      baselineX: startTime.millisecondsSinceEpoch.toDouble(),
      baselineY: miniY,
      rangeAnnotations: RangeAnnotations(
        horizontalRangeAnnotations: [
          HorizontalRangeAnnotation(
            y1: pointTarget.mini,
            y2: pointTarget.max,
            color: const Color(0xFFE5E5E5),
          ),
        ],
      ),
    );
  }

  LineChartBarData get _emptyData => LineChartBarData(
        barWidth: 0,
        belowBarData: BarAreaData(show: false),
      );

  List<LineChartBarData> getLineData(double miniY, double maxY) {
    List<LineChartBarData> result = [_emptyData];
    int lineIndex = 0;
    for (DataChartLine e in lines) {
      result.add(LineChartBarData(
        isCurved: e.isCurved,
        color: e.lineColor,
        barWidth: e.lineWidth,
        preventCurveOverShooting: true,
        // preventCurveOvershootingThreshold: 0.35,
        //     isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter:
              (FlSpot spot, double xPercentage, LineChartBarData bar, int index,
                  {double? size}) {
            Color c;
            if (e.dotColorCallback != null) {
              c = e.dotColorCallback!(
                  spot.x.toInt(), lineIndex, index, e.values[index].tag);
            } else {
              c = e.dotColor ?? Colors.white;
            }

            DataChartDotType dotType = e.dotType;
            if (dotType == DataChartDotType.icon) {
              return DNUDotIconPainter(
                icon: e.dotIcon ?? DNUIconFont.shixinxiedi,
                size: e.dotSize ?? 20,
                color: c,
              );
            } else {
              return FlDotCirclePainter(
                radius: (e.dotSize ?? 6),
                color: c,
                strokeColor: c,
              );
            }
          },
        ),
        belowBarData: BarAreaData(show: false),
        spots: e.values
            .map((e) => FlSpot(e.dateTime.millisecondsSinceEpoch.toDouble(),
                e.value == 0 ? miniY : e.value))
            .toList(),
      ));
    }

    return result;
  }

  SideTitles leftTitles() => SideTitles(
        getTitlesWidget: leftTitleWidgets,
        showTitles: true,
        interval: yAxisInterval,
        reservedSize: 40,
      );

  SideTitles get bottomTitles => SideTitles(
        showTitles: true,
        reservedSize: 32,
        interval: xAxisInterval.toDouble(),
        getTitlesWidget: bottomTitleWidgets,
      );

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    final style = TextStyle(
        color: AppColor.textSecondary,
        fontSize: 12.sp,
        fontFamily: AppFont.rany);
    Widget widget;

    double diff = endTime.millisecondsSinceEpoch - value;
    if (diff > 0 && diff < xAxisInterval) {
      widget = Container();
    } else {
      DateTime currentTime = DateTime.fromMillisecondsSinceEpoch(value.toInt());
      widget = Text(currentTime.formatDate(xAxisDateFormat ?? "HH:mm"),
          style: style);
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 8.0,
      child: widget,
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    final style = TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 12.sp,
      color: const Color(0xFF808080),
    );
    NumberFormat numberFormat =
        yAxisDateFormat ?? NumberFormat("####", "en_US");
    String text = numberFormat.format(value);

    return Text(text, style: style, textAlign: TextAlign.center);
  }
}
