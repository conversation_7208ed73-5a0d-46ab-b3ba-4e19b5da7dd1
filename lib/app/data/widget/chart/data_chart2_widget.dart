import 'package:dnurse/app/data/widget/chart/dashed_divider.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../ui/style/color.dart';
import '../../../../ui/style/font.dart';
import '../../common/data_target.dart';
import '../../common/time_point.dart';

class DataChartValue {
  DataChartValue({
    required this.dateTime,
    this.value = 0,
    required this.timePoint,
    this.tag,
  });

  final DateTime dateTime;
  final double value;
  final TimePoint timePoint;
  final dynamic tag;

  @override
  String toString() {
    // TODO: implement toString
    return "GlucoseChartData{dateTime:$dateTime, value:$value, timePoint:$timePoint, tag:$tag}";
  }
}

class DataChart2Line {
  DataChart2Line(
    this.values, {
    this.lineColor,
    this.lineWidth = 0,
    this.dotColorCallback,
    this.showDot = true,
    this.isCurved = false,
  });

  final Color? lineColor;
  final double lineWidth;
  final bool isCurved;
  final bool showDot;

  final DataLinePointColor? dotColorCallback;

  final List<DataChartValue> values;
}

typedef DataLinePointColor = Color Function(
  int pointTime,
  int lineIndex,
  int dataIndex,
  dynamic tag,
);
typedef DataLineTipMessage = String Function(
  int lineIndex,
  DateTime dateTime,
  DataChartValue data,
);

class DataChart2Widget extends StatelessWidget {
  DataChart2Widget(
      {super.key,
      this.pointTarget,
      required this.lines,
      required this.startTime,
      required this.endTime,
      required this.xAxisInterval,
      required this.yAxisMin,
      required this.yAxisMax,
      required this.yAxisInterval,
      this.lineTipMessage,
      this.xAxisDateFormat,
      this.yAxisDateFormat,
      this.showGrid = false,
      this.betweenColor = const Color(0xFFEEE6FA),
      this.hasBottomBorder = true,
      this.yAxisMinDashLine = false,
      this.timePoints,
      this.showCustomTooltip = false,
      this.tooltipCard,
      this.height});

  final PointTarget? pointTarget;
  final List<DataChart2Line> lines;
  final DateTime startTime;
  final DateTime endTime;
  final double xAxisInterval;
  final double yAxisMin;
  final double yAxisMax;
  final double yAxisInterval;
  final DataLineTipMessage? lineTipMessage;
  final String? xAxisDateFormat;
  final NumberFormat? yAxisDateFormat;
  final bool showGrid;
  final Color betweenColor;
  // 是否显示底部边框
  final bool hasBottomBorder;
  // 底部是否显示虚线
  final bool yAxisMinDashLine;
  final List<int>? timePoints;
  final bool showCustomTooltip;
  final Widget Function(List<LineBarSpot>?, Offset?)? tooltipCard;
  final double? height;

  final Rx<List<LineBarSpot>?> touchedSpot = null.obs;
  final Rx<Offset?> touchPosition = null.obs;

  @override
  Widget build(BuildContext context) {
    double miniY = yAxisMin;
    double maxY = yAxisMax;

    if (!showCustomTooltip) {
      return LineChart(
        _buildLineChartData(miniY, maxY),
        duration: const Duration(milliseconds: 250),
      );
    }

    return Stack(
      children: [
        Column(
          children: [
            if (tooltipCard != null)
              tooltipCard!(touchedSpot.value, touchPosition.value),
            // LineChart(
            //   _buildLineChartData(miniY, maxY),
            //   duration: const Duration(milliseconds: 250),
            // ),
            SizedBox(
              height: height,
              child: LineChart(
                _buildLineChartData(miniY, maxY),
                duration: const Duration(milliseconds: 250),
              ),
            ),
          ],
        ),
        Positioned(
          top: 48.w,
          bottom: 20.w,
          left: touchPosition.value == null
              ? 20.w
              : touchPosition.value!.dx + 20.w,
          child: const DashedVerticalLine(color: AppColor.primaryLight),
        ),
      ],
    );
  }

  LineChartData _buildLineChartData(double miniY, double maxY) {
    List<LineChartBarData> lineBarsData = getLineData(miniY, maxY);
    return LineChartData(
      lineTouchData: showCustomTooltip
          ? _customTouchData()
          : LineTouchData(
              handleBuiltInTouches: true,
              touchTooltipData: LineTouchTooltipData(
                // showOnTopOfTheChartBoxArea: true,
                // tooltipBgColor: AppColor.primaryLightOpacity6,
                getTooltipColor: (LineBarSpot spot) =>
                    AppColor.primaryLightOpacity6,
                tooltipRoundedRadius: 12,
                tooltipPadding: const EdgeInsets.only(
                    left: 150, top: 8, bottom: 8, right: 160),
                getTooltipItems: (List<LineBarSpot> lineBarsSpot) {
                  return lineBarsSpot.map((lineBarSpot) {
                    DataChartValue point = lines[lineBarSpot.barIndex - 1]
                        .values[lineBarSpot.spotIndex];
                    DateTime time = DateTime.fromMillisecondsSinceEpoch(
                        lineBarSpot.x.toInt());

                    String? tipMessage = lineTipMessage?.call(
                        lineBarSpot.barIndex - 1, time, point);
                    tipMessage ??=
                        "${lineBarSpot.y}\n${time.formatDate("M/d HH:mm")}";

                    return LineTooltipItem(
                      tipMessage,
                      const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  }).toList();
                },
              ),
              getTouchedSpotIndicator:
                  (LineChartBarData barData, List<int> spotIndexes) {
                int lineIndex = lineBarsData.indexOf(barData);
                return spotIndexes.map((index) {
                  return TouchedSpotIndicatorData(
                    const FlLine(
                      strokeWidth: 0,
                      dashArray: [2, 5],
                      color: AppColor.primaryLight,
                    ),
                    FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 6,
                          color: Colors.red.withOpacity(0.4),
                          strokeWidth: 0,
                        );
                      },
                    ),
                  );
                }).toList();
              },
            ),
      betweenBarsData: lines.isEmpty
          ? []
          : [
              BetweenBarsData(
                fromIndex: 0,
                toIndex: 1,
                color: betweenColor,
              )
            ],
      // yAxisMin和yAxisMax不起作用
      gridData: FlGridData(
        show: showGrid,
        drawHorizontalLine: true,
        horizontalInterval: yAxisInterval,
        drawVerticalLine: false,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: const Color(0xFFE5E5E5),
            strokeWidth: 1.w,
            dashArray: [2, 2],
          );
        },
      ),
      // yAxisMin和yAxisMax在这里配置
      extraLinesData: ExtraLinesData(
        horizontalLines: [
          HorizontalLine(
            y: yAxisMax,
            color: const Color(0xFFE5E5E5),
            strokeWidth: 1.w,
            dashArray: [2, 2],
          ),
          if (yAxisMinDashLine)
            HorizontalLine(
              y: yAxisMin,
              color: const Color(0xFFE5E5E5),
              strokeWidth: 1.w,
              dashArray: [2, 2],
            ),
        ],
      ),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: bottomTitles,
        ),
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          sideTitles: leftTitles(),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border(
            bottom: hasBottomBorder
                ? const BorderSide(color: Color(0xFFE5E5E5), width: 1)
                : BorderSide.none),
      ),
      lineBarsData: lineBarsData,
      minX: startTime.millisecondsSinceEpoch.toDouble(),
      maxX: endTime.millisecondsSinceEpoch.toDouble(),
      maxY: maxY,
      minY: miniY,
      baselineX: startTime.millisecondsSinceEpoch.toDouble(),
      baselineY: miniY,
      rangeAnnotations: pointTarget != null
          ? RangeAnnotations(
              horizontalRangeAnnotations: [
                HorizontalRangeAnnotation(
                  y1: pointTarget!.mini,
                  y2: pointTarget!.max,
                  // color: const Color(0xFFE5E5E5),
                  gradient: const LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      Color(0x1A35A1DB),
                      Color(0x1A36BF6F),
                    ],
                  ),
                ),
              ],
            )
          : const RangeAnnotations(),
    );
  }

  LineChartBarData get _emptyData => LineChartBarData(
        barWidth: 0,
        belowBarData: BarAreaData(show: false),
      );

  List<LineChartBarData> getLineData(double miniY, double maxY) {
    List<LineChartBarData> result = [_emptyData];
    int lineIndex = 0;
    for (DataChart2Line e in lines) {
      result.add(LineChartBarData(
        isCurved: e.isCurved,
        color: e.lineColor,
        barWidth: e.lineWidth,
        preventCurveOverShooting: true,
        // preventCurveOvershootingThreshold: 0.35,
        //     isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: 4,
              color: Colors.white,
              strokeWidth: 1.5.w,
              strokeColor: Colors.deepOrangeAccent.withOpacity(0.8),
            );
          },
        ),
        belowBarData: BarAreaData(show: false),
        spots: e.values
            .map((e) => FlSpot(e.dateTime.millisecondsSinceEpoch.toDouble(),
                e.value == 0 ? miniY : e.value))
            .toList(),
      ));
    }

    return result;
  }

  LineTouchData _customTouchData() => LineTouchData(
        touchSpotThreshold: 20, // 增加触摸阈值
        getTouchLineStart: (_, __) => -double.infinity,
        getTouchLineEnd: (_, __) => double.infinity,
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          return spotIndexes.map((index) {
            return TouchedSpotIndicatorData(
              FlLine(color: Colors.blue, strokeWidth: 2.w),
              FlDotData(
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(radius: 4, color: Colors.blue),
              ),
            );
          }).toList();
        },
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) => Colors.transparent,
          showOnTopOfTheChartBoxArea: true,
          getTooltipItems: defaultLineTooltipItem,
        ),
        touchCallback: (FlTouchEvent event, LineTouchResponse? response) {
          // 触摸回调
          if (response?.lineBarSpots != null &&
              response!.lineBarSpots!.isNotEmpty) {
            print(
                'response: ${response.lineBarSpots!.first.x} ${response.lineBarSpots!.length} ');
            touchedSpot.value = response.lineBarSpots;
            touchPosition.value = event.localPosition;
          }
        },
      );

  List<LineTooltipItem> defaultLineTooltipItem(List<LineBarSpot> touchedSpots) {
    return touchedSpots.map((LineBarSpot touchedSpot) {
      const textStyle = TextStyle(
        color: Colors.transparent,
        fontWeight: FontWeight.bold,
        fontSize: 14,
      );

      return LineTooltipItem(touchedSpot.y.toString(), textStyle);
    }).toList();
  }

  SideTitles leftTitles() => SideTitles(
        getTitlesWidget: leftTitleWidgets,
        showTitles: true,
        interval: yAxisInterval,
        reservedSize: 20,
      );

  SideTitles get bottomTitles => SideTitles(
        showTitles: true,
        reservedSize: 20,
        interval: xAxisInterval.toDouble(),
        getTitlesWidget: bottomTitleWidgets,
      );

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    Widget widget;

    if (timePoints != null) {
      if (timePoints!.contains(value.toInt())) {
        DateTime currentTime =
            DateTime.fromMillisecondsSinceEpoch(value.toInt());
        widget = Text(
          currentTime.formatDate(xAxisDateFormat ?? "HH:mm"),
          style: axisTextStyle(),
        );

        return SideTitleWidget(
          axisSide: meta.axisSide,
          space: 6.w,
          child: widget,
          // fitInside:
          //     SideTitleFitInsideData.fromTitleMeta(meta, distanceFromEdge: 0),
        );
      }
    }

    double diff = endTime.millisecondsSinceEpoch - value;
    if (diff > 0 && diff < xAxisInterval) {
      widget = Container();
    } else {
      DateTime currentTime = DateTime.fromMillisecondsSinceEpoch(value.toInt());
      widget = Text(currentTime.formatDate(xAxisDateFormat ?? "HH:mm"),
          style: axisTextStyle());
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 6.w,
      child: widget,
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    NumberFormat numberFormat =
        yAxisDateFormat ?? NumberFormat("####", "en_US");
    String text = numberFormat.format(value);

    return Text(text, style: axisTextStyle(), textAlign: TextAlign.center);
  }

  TextStyle axisTextStyle() {
    return TextStyle(
      color: const Color(0xFF808080),
      fontSize: 10.sp,
      fontFamily: AppFont.rany,
      height: 1.4,
    );
  }
}
