/// FileName: DataTableRowWidget
///
/// @Author: ygc
/// @Date: 2024/8/14 10:24
/// @Description:
import 'package:auto_size_text/auto_size_text.dart';
import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../ui/style/color.dart';
import '../../entity/Data.dart';

class DataTableRowData {
  final DateTime dateTime;
  final Map<TimePoint, Data> values;

  DataTableRowData({
    required this.dateTime,
    required this.values,
  });
}

class DataTableItemValue {
  final String value;
  final Color? textColor;
  final dynamic tag;

  DataTableItemValue({
    required this.value,
    this.textColor,
    this.tag,
  });
}

class DataTableItem {
  final DateTime dateTime;
  final Map<TimePoint, DataTableItemValue>? values;

  DataTableItem({
    required this.dateTime,
    this.values,
  });

  static DataTableItem fromRowData(DataTableRowData rowData) {
    Map<TimePoint, DataTableItemValue> values = {};

    for (TimePoint timePoint in rowData.values.keys) {
      Data glucoseValue = rowData.values[timePoint]!;

      values[timePoint] = DataTableItemValue(
          value: glucoseValue.stringValue(),
          textColor: glucoseValue.getStatusColor(),
          tag: glucoseValue);
    }

    return DataTableItem(
      dateTime: rowData.dateTime,
      values: values,
    );
  }
}

final List<TimePoint> _columns = [
  TimePoint.dawn,
  TimePoint.breakfastBefore,
  TimePoint.breakfastAfter,
  TimePoint.lunchBefore,
  TimePoint.lunchAfter,
  TimePoint.supperBefore,
  TimePoint.supperAfter,
  TimePoint.sleep,
];

class DataTableRowWidget extends StatelessWidget {
  const DataTableRowWidget({
    super.key,
    this.separate = false,
    required this.value,
    this.borderColor,
    this.rowHeight,
    this.onTap,
    this.highlight = false,
  });

  final bool separate;
  final DataTableItem value;
  final Color? borderColor;
  final double? rowHeight;
  final bool highlight;

  final void Function(DateTime dateTime, TimePoint timePoint, dynamic tag)?
      onTap;

  @override
  Widget build(BuildContext context) {
    Color useBorderColor = borderColor ?? AppColor.tableBorder;
    return Container(
      color: Colors.white,
      child: Table(
        border: TableBorder(
          left: BorderSide(color: useBorderColor),
          right: BorderSide(color: useBorderColor),
          bottom:
              BorderSide(color: separate ? AppColor.primary : useBorderColor),
          horizontalInside: BorderSide(color: useBorderColor),
          verticalInside: BorderSide(color: useBorderColor),
        ),
        children: [
          TableRow(
            children: [
              _buildTableCell(
                value.dateTime.formatDate("MM/dd"),
                backgroundColor: const Color(0xFFF4F4F8),
                textColor: AppColor.textDesc,
              ),
              for (int i = 0; i < _columns.length; i++) _buildValueCell(i),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildValueCell(int index) {
    TimePoint timePoint = _columns[index];
    DataTableItemValue? itemValue = value.values?[timePoint];

    return _buildTableCell(itemValue?.value ?? '',
        textColor: itemValue?.textColor, highlight: highlight, onTap: () {
      onTap?.call(value.dateTime, timePoint, itemValue?.tag);
    });
  }

  Widget _buildTableCell(
    String value, {
    bool highlight = false,
    VoidCallback? onTap,
    Color? textColor,
    Widget? child,
    Color? backgroundColor,
  }) {
    double useRowHeight = rowHeight ?? 40.w;
    return TableCell(
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color:
                backgroundColor ?? (highlight ? const Color(0xFFF8F8FE) : null),
          ),
          height: useRowHeight,
          child: Center(
            child: child ??
                AutoSizeText(
                  value,
                  minFontSize: 10,
                  maxFontSize: 14,
                  style: TextStyle(
                    color: textColor ?? AppColor.textPrimary,
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
          ),
        ),
      ),
    );
  }
}
