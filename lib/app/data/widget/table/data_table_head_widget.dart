/// FileName: DataTableHeadWidget
///
/// @Author: ygc
/// @Date: 2024/8/14 10:15
/// @Description:
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../../../../ui/style/color.dart';

class DataTableHeadWidget extends StatelessWidget {
  const DataTableHeadWidget({
    super.key,
    this.topPadding,
  });

  final double? topPadding;

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: Colors.white,
      child: Table(
        columnWidths: const {
          2: FlexColumnWidth(2),
          3: FlexColumnWidth(2),
          4: FlexColumnWidth(2),
        },
        border: const TableBorder(
          // top: BorderSide(color: AppColor.textDisable),
          left: BorderSide(color: AppColor.tableBorder),
          right: BorderSide(color: AppColor.tableBorder),
          bottom: BorderSide(color: AppColor.tableBorder),
          horizontalInside: BorderSide(color: AppColor.tableBorder),
          verticalInside: BorderSide(color: AppColor.tableBorder),
        ),
        children: [
          TableRow(children: [
            _buildTableHeadCell(
              context,
              text: "日\n期",
              backgroundColor: AppColor.primaryOpacity6,
            ),
            _buildTableHeadCell(
              context,
              text: "凌晨",
            ),
            _buildTableHeadCell(
              context,
              child: _buildTableHeadDoubleCell(
                context,
                "早餐",
                leftText: "空腹",
              ),
            ),
            _buildTableHeadCell(
              context,
              child: _buildTableHeadDoubleCell(
                context,
                "午餐",
              ),
            ),
            _buildTableHeadCell(
              context,
              child: _buildTableHeadDoubleCell(
                context,
                "晚餐",
              ),
            ),
            _buildTableHeadCell(
              context,
              text: "睡前",
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildTableHeadCell(
    BuildContext context, {
    String? text,
    double? height,
    Widget? child,
    VoidCallback? onTap,
    Color? backgroundColor,
  }) {
    return TableCell(
      child: InkWell(
        onTap: onTap,
        child: Container(
          height: height ?? 108.w,
          width: double.infinity,
          color: backgroundColor,
          padding: EdgeInsets.only(top: topPadding ?? 0),
          child: child ?? _buildTableHeadText(context, text ?? ""),
        ),
      ),
    );
  }

  Widget _buildTableHeadText(BuildContext context, String text) {
    return Center(
      child: AutoSizeText(
        text,
        minFontSize: 10,
        maxFontSize: 16,
        style: TextStyle(
          fontSize: 15.sp,
          color: AppColor.textDesc,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildTableHeadDoubleCell(
    BuildContext context,
    String text, {
    String? leftText,
    String? rightText,
    VoidCallback? onLeftTap,
    VoidCallback? onRightTap,
  }) {
    return Column(
      children: [
        Expanded(child: _buildTableHeadText(context, text)),
        const Divider(
          height: 1,
          thickness: 1,
          color: AppColor.tableBorder,
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: onLeftTap,
                  child: _buildTableHeadText(context, leftText ?? "前"),
                ),
              ),
              const VerticalDivider(
                width: 1,
                thickness: 1,
                color: AppColor.tableBorder,
              ),
              Expanded(
                child: InkWell(
                  onTap: onRightTap,
                  child: _buildTableHeadText(context, rightText ?? "后"),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
