/// FileName: uric_acid_widget
///
/// @Author: ygc
/// @Date: 2024/8/13 13:55
/// @Description:
import 'dart:math';

import 'package:dnurse/app/data/common/uric_acid_target.dart';
import 'package:dnurse/app/user/entity/unit/uric_acid_unit.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../resource/dnu_assets.dart';
import '../../../ui/icons/icons.dart';
import '../../../ui/style/color.dart';
import '../../../ui/widgets/progress/arc_progress_widget.dart';
import '../../user/service/user_config_service.dart';
import '../common/uric_acid_data_status.dart';
import '../entity/data_uric_acid.dart';

class UricAcidWidget extends StatelessWidget {
  const UricAcidWidget({
    super.key,
    this.width,
    this.height,
    this.value,
    this.uricAcidUnit,
    required this.dataTarget,
    this.onHelpTap,
    this.showStatus,
  });

  final double? width;
  final double? height;
  final DataUricAcid? value;
  final UricAcidUnit? uricAcidUnit;
  final UricAcidTarget dataTarget;
  final VoidCallback? onHelpTap;
  final bool? showStatus;

  @override
  Widget build(BuildContext context) {
    UricAcidUnit unit = uricAcidUnit ?? UserConfigService.to.uricAcidUnit.value;
    UricAcidDataValueStatus status =
        value?.getValueStatus(dataTarget) ?? UricAcidDataValueStatus.normal;

    String timePointStr = value?.timePoint.name ?? "";
    String dateStr =
        value?.time.formatDate("${DateFormat.ABBR_MONTH_DAY} $timePointStr HH:mm") ?? '';

    return SizedBox(
      width: width ?? double.infinity,
      // color: Colors.red.withOpacity(0.2),
      child: Column(
        children: [
          SizedBox(
            height: height ?? 122.w,
            width: double.infinity,
            // color: Colors.blue.withOpacity(0.6),
            child: Stack(
              children: [
                ArcProgressWidget(
                  value: value?.getProgressValue() ?? 0,
                  backgroundStrokeWidth: 15.w,
                  progressbarStrokeWidth: 15.w,
                  gradient: status.gradientColor,
                  openAngle: 2 * pi / 3,
                ),
                _buildValue(context, unit, status),
                if (onHelpTap != null)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: InkWell(
                      onTap: onHelpTap,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 5, bottom: 5),
                        child: Image.asset(
                          DNUAssets.to.images.main.help,
                          width: 20.w,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 4.w),
            child: Text(
              dateStr,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValue(
      BuildContext context, UricAcidUnit unit, UricAcidDataValueStatus status) {
    return Padding(
      padding: EdgeInsets.only(top: 12.w),
      child: Center(
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(maxHeight: 40.w),
          child: Stack(
            children: [
              Center(
                child: Stack(
                  children: [
                    Text(
                      value?.stringValue() ?? "--",
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: status.color,
                      ),
                    ),
                    if (showStatus ?? false)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Center(
                          child: Container(
                            transform: Matrix4.translationValues(24.w, 0, 0),
                            child: Icon(
                              DNUIconFont.quxian,
                              size: 20.w,
                              color: AppColor.primaryLight,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    transform: Matrix4.translationValues(0, 12.w, 0),
                    child: Text(
                      unit.getResString(context),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColor.textSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
