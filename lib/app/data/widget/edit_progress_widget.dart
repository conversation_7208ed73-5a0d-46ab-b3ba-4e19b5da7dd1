import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenUtil.dart';

import '../../../ui/style/color.dart';
import '../../../ui/style/font.dart';
import '../../../ui/widgets/progress/arc_progress_widget.dart';
import '../common/precision_limit_formatter.dart';

class EditProgressWidget extends StatelessWidget {

  final double? width;
  final double? height;
  final double? progressValue;
  final String? unitStr;
  final String? hintText;
  final String? miniStr;
  final String? maxStr;
  final Gradient? progressGradient;
  final Color? editTextColor;
  final ValueChanged<String>? onEditChanged;
  final int? decimalPlaces;
  final TextEditingController? editController;

  const EditProgressWidget({super.key, this.width, this.height, this.progressValue, this.unitStr, this.hintText, this.miniStr, this.maxStr, this.progressGradient, this.editTextColor, this.onEditChanged, this.decimalPlaces, this.editController, });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              // color: Colors.red,
              width: width ?? 313.sp,
              height: height ?? 168.sp,
              child: ArcProgressWidget(
                backgroundColor: AppColor.background2,
                value: progressValue ?? 0,
                openAngle: pi,
                gradient: progressGradient,
                strokeCapRound: true,
                progressbarStrokeWidth: 16.sp,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 36.sp),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F6),
                borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              ),
              width: 124.sp,
              height: 60.sp,
              child: _getTextField('', editController, onEditChanged, 4),
            ),
            Container(
              margin: EdgeInsets.only(top: 135.sp),
              // decoration: BoxDecoration(
              //   color: const Color(0xFFF0F0F6),
              //   borderRadius: BorderRadius.all(Radius.circular(12.sp)),
              // ),
              // width: 124.sp,
              height: 26.sp,
              child: Text(unitStr ?? '', style: TextStyle(fontSize: 18.sp, color: AppColor.textDesc),),
            ),
          ],
        ),

        SizedBox(width: 313.sp, child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(miniStr ?? '', style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),),
            Text(maxStr ?? '', style: TextStyle(fontSize: 15.sp, color: AppColor.textDesc),),
          ],
        ),),
      ],
    );
  }

  Widget _getTextField(String hintText, TextEditingController? controller,
      ValueChanged<String>? onChanged, int? maxLength) {
    return TextField(
      cursorColor: const Color(0xFF707390),
      cursorWidth: 4,
      // focusNode: editFouFocusNode,
      textAlign: TextAlign.center,
      decoration: InputDecoration(
        hintText: hintText,
        counterText: "",
        iconColor: AppColor.textPrimary,
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: AppColor.textDisable,
          fontSize: 48.sp,
          fontFamily: AppFont.ranyBold,
        ),
      ),
      inputFormatters: [
        PrecisionLimitFormatter(decimalPlaces ?? 0),
      ],
      style: TextStyle(
        fontSize: 48.sp,
        color: editTextColor,
        fontFamily: AppFont.ranyBold,
      ),
      controller: controller,
      onChanged: onChanged,
      maxLength: maxLength,
      keyboardType: TextInputType.numberWithOptions(
          decimal: decimalPlaces != 0 ? true : false),
    );
  }
}