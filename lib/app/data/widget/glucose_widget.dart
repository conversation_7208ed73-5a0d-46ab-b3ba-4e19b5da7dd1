/// FileName: glucose_widget
///
/// @Author: ygc
/// @Date: 2024/8/12 13:57
/// @Description:
import 'dart:math';

import 'package:dnurse/app/data/common/data_status.dart';
import 'package:dnurse/app/data/common/data_target.dart';
import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/app/user/entity/unit/glucose_unit.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../resource/dnu_assets.dart';
import '../../../ui/icons/icons.dart';
import '../../../ui/style/color.dart';
import '../../../ui/widgets/progress/arc_progress_widget.dart';
import '../../user/service/user_config_service.dart';

class GlucoseWidget extends StatelessWidget {
  const GlucoseWidget({
    super.key,
    this.width,
    this.height,
    this.glucose,
    this.glucoseUnit,
    required this.dataTarget,
    this.onHelpTap,
    this.showArrow,
  });

  final double? width;
  final double? height;
  final DataGlucose? glucose;
  final GlucoseUnit? glucoseUnit;
  final DataTarget dataTarget;
  final VoidCallback? onHelpTap;
  final bool? showArrow;

  @override
  Widget build(BuildContext context) {
    GlucoseUnit unit = glucoseUnit ?? UserConfigService.to.glucoseUnit.value;
    DataValueStatus status =
        glucose?.getValueStatus(dataTarget) ?? DataValueStatus.normal;

    String timePointStr = glucose?.timePoint.name ?? "";
    String dateStr =
        glucose?.time.formatDate("${DateFormat.ABBR_MONTH_DAY} $timePointStr HH:mm") ?? '';

    return SizedBox(
      width: width ?? double.infinity,
      // color: Colors.red.withOpacity(0.2),
      child: Column(
        children: [
          SizedBox(
            height: height ?? 122.w,
            width: double.infinity,
            // color: Colors.blue.withOpacity(0.6),
            child: Stack(
              children: [
                ArcProgressWidget(
                  value: glucose?.getProgressValue() ?? 0,
                  backgroundStrokeWidth: 15.w,
                  progressbarStrokeWidth: 15.w,
                  gradient: status.gradientColor,
                  openAngle: 2 * pi / 3,
                ),
                _buildValue(context, unit, status),
                if (onHelpTap != null)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: InkWell(
                      onTap: onHelpTap,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 5, bottom: 5),
                        child: Image.asset(
                          DNUAssets.to.images.main.help,
                          width: 20.w,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 4.w),
            child: Text(
              dateStr,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValue(
      BuildContext context, GlucoseUnit unit, DataValueStatus status) {
    return Padding(
      padding: EdgeInsets.only(top: 12.w),
      child: Center(
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(maxHeight: 40.w),
          child: Stack(
            children: [
              Center(
                child: Stack(
                  children: [
                    Text(
                      glucose?.stringValue() ?? "--",
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: status.color,
                      ),
                    ),
                    if (showArrow ?? false)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Center(
                          child: Container(
                            transform: Matrix4.translationValues(24.w, 0, 0),
                            child: Icon(
                              DNUIconFont.quxian,
                              size: 20.w,
                              color: AppColor.primaryLight,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    transform: Matrix4.translationValues(0, 20.w, 0),
                    child: Text(
                      unit.getResString(context),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColor.textSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
