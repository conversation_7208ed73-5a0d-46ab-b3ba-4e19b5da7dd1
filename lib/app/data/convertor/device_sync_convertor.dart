/// FileName: device_sync_convertor
///
/// @Author: ygc
/// @Date: 2024/9/30 13:55
/// @Description:

import 'package:dnurse/app/data/db/model/device_sync_info/device_sync_info_model.dart';
import 'package:dnurse/app/data/entity/device_sync_info.dart';

class DeviceSyncConvertor {
  static DeviceSyncInfoModel toModel(DeviceSyncInfo source, String userSn) {
    return DeviceSyncInfoModel(
      id: source.id ?? 0,
      uid: userSn,
      did: source.did,
      deviceType: source.device.id,
      deviceSn: source.deviceSn,
    );
  }

  static DeviceSyncInfo toEntity(DeviceSyncInfoModel source) {
    return DeviceSyncInfo(
      id: source.id,
      did: source.did,
      deviceSn: source.deviceSn,
      device: DeviceSyncDevice.getById(source.deviceType) ??
          DeviceSyncDevice.insulink,
    );
  }
}
