import 'dart:convert';

import 'package:dnurse/app/common/entity/unit.dart';
import 'package:dnurse/app/data/entity/data_food.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../../../framework/utils/log.dart';
import '../common/food_type.dart';
import '../db/model/food/food_data_model.dart';
import '../db/model/food/food_item_model.dart';
import '../entity/data_food_item.dart';

class FoodDataConvertor {
  FoodDataConvertor._();

  static DataFood toFoodEntity(FoodDataModel model) {
    List<DataFoodItem> items = [];

    if (model.items != null && model.items!.isNotEmpty) {
      try {
        final jsonData = jsonDecode(model.items!);
        for (var item in jsonData) {
          final foodItem = FoodItemModel.fromJson(item);
          items.add(DataFoodItem(
            name: foodItem.name,
            imgUrl: foodItem.imgUrl,
            calorie: foodItem.calorie,
            amount: foodItem.amount,
            weightUnit: Unit.getUnitById(foodItem.unit),
          ));
        }
      } catch (e) {
        Log.e("FoodDataConvertor toFoodEntity error:", e);
      }
    }

    return DataFood(
      id: model.id,
      did: model.did,
      time: DateTime.fromMillisecondsSinceEpoch(model.time * 1000),
      sn: model.uid,
      calorie: model.calorie,
      foodType: FoodType.getById(model.timePoint),
      items: items,
    );
  }

  static FoodDataModel toFoodModel(DataFood entity) {
    String items = jsonEncode(entity.items.map((item) {
      return FoodItemModel(
        name: item.name,
        imgUrl: item.imgUrl,
        calorie: item.calorie,
        amount: item.amount,
        unit: item.weightUnit.id,
      );
    }).toList());

    return FoodDataModel(
      id: entity.id,
      uid: entity.sn,
      did: entity.did,
      time: entity.time.secondsSinceEpoch,
      calorie: entity.calorie,
      timePoint: entity.foodType.id,
      items: items,
    );
  }
}
