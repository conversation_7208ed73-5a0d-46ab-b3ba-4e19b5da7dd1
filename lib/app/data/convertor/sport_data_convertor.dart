import 'package:dnurse/app/data/db/model/sport/sport_data_model.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../../user/entity/from_type.dart';
import '../entity/data_sport.dart';

class SportDataConvertor {
  static DataSport toEntity(SportDataModel data) {
    return DataSport(
      id: data.id,
      did: data.did,
      sn: data.uid,
      time: DateTime.fromMillisecondsSinceEpoch(data.time * 1000),
      name: data.name,
      duration: data.duration,
      calorie: data.calorie,
      fromType: FromType.getTypeIdByValue(data.fromType),
      isFromWalk: data.isFromWalk != 0,
    );
  }

  static SportDataModel toModel(DataSport data) {
    return SportDataModel(
      id: data.id,
      did: data.did,
      uid: data.sn,
      time: data.time.secondsSinceEpoch,
      name: data.name,
      duration: data.duration,
      calorie: data.calorie,
      fromType: data.fromType.typeId,
      isFromWalk: data.isFromWalk ? 1 : 0,
    );
  }

  static Map<String, dynamic>? jsonFormat(SportDataModel data) {
    Map<String, dynamic> dataMap = {
      '_id': data.id,
      '_upid': data.upid,
      'time': data.time,
      'did': data.did,
      'deleted': data.deleted,
      'modif': data.modifiedAt,
      'name': data.name,
      'calorie': data.calorie,
      'num': data.duration
    };

    return dataMap;
  }

  static SportDataModel fromJson(Map<String, dynamic> jsonData, String uid) {
    SportDataModel data = SportDataModel(
      // id: int.parse(jsonData['id']),
      uid: uid,
      did: jsonData['did'],
      upid: int.parse(jsonData['_upid']),
      modifiedAt: int.parse(jsonData['modif']),
      deleted: int.parse(jsonData['deleted']),
      modified: 0,
      calorie: int.parse(jsonData['calorie']),
      name: jsonData['name'],
      duration: int.parse(jsonData['num']),
      time: int.parse(jsonData['time']),
      fromType: int.parse(jsonData['fromUser']),
    );

    return data;
  }
}
