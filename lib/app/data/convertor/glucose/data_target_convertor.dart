/// FileName: data_target_convertor
///
/// @Author: ygc
/// @Date: 2024/8/8 14:12
/// @Description:
import 'package:dnurse/app/data/common/data_target.dart';

import '../../db/model/glucose/target/data_target_model.dart';

class GlucoseDataTargetConvertor {
  static GlucoseDataTargetModel toModel(DataTarget source, String userSn) {
    return GlucoseDataTargetModel(
      id: source.id,
      diastatic: source.diastatic,
      dawnLow: source.dawnLow,
      dawnHigh: source.dawnHigh,
      emptyLow: source.emptyLow,
      emptyHigh: source.emptyHigh,
      mealAfterLow: source.mealAfterLow,
      mealAfterHigh: source.mealAfterHigh,
      mealBeforeLow: source.mealBeforeLow,
      mealBeforeHigh: source.mealBeforeHigh,
      sleepHigh: source.sleepHigh,
      sleepLow: source.sleepLow,
      uid: userSn,
      userModified: source.userModified ? 1 : 0,
    );
  }

  static DataTarget toEntity(GlucoseDataTargetModel source) {
    return DataTarget(
      id: source.id,
      diastatic: source.diastatic,
      dawnLow: source.dawnLow,
      dawnHigh: source.dawnHigh,
      emptyLow: source.emptyLow,
      emptyHigh: source.emptyHigh,
      mealAfterLow: source.mealAfterLow,
      mealAfterHigh: source.mealAfterHigh,
      mealBeforeLow: source.mealBeforeLow,
      mealBeforeHigh: source.mealBeforeHigh,
      sleepHigh: source.sleepHigh,
      sleepLow: source.sleepLow,
      userModified: source.userModified == 1,
    );
  }
}
