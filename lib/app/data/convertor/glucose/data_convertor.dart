/// FileName: data_target_convertor
///
/// @Author: ygc
/// @Date: 2024/8/8 14:12
/// @Description:
import 'dart:convert';

import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/app/data/entity/data_source.dart';
import 'package:dnurse/bootstrap/app.dart';

import '../../../../framework/utils/log.dart';
import '../../db/model/glucose/glucose_ua_value_model.dart';
import '../../entity/Data.dart';
import '../../entity/data_extra.dart';
import '../../entity/data_from.dart';
import '../../entity/data_glucose.dart';
import '../../entity/data_type.dart';
import '../../entity/data_uric_acid.dart';

class DataConvertor {
  DataConvertor._();

  static Data? toEntity(GlucoseUAValueModel source) {
    switch (source.dataType) {
      case dataTypeGlucose:
        return toGlucoseEntity(source);

      case dataTypeUricAcid:
        return toUricAcidEntity(source);
    }

    return null;
  }

  static DataGlucose? toGlucoseEntity(GlucoseUAValueModel source) {
    if (source.dataType != DataType.glucose.value) {
      return null;
    }

    DataExtra? extra;
    if (source.extra != null && source.extra!.isNotEmpty) {
      try {
        Map<String, dynamic> jsonOjb = jsonDecode(source.extra!);
        extra = DataExtra.fromJson(jsonOjb);
      } catch (e) {
        Log.e("toGlucoseEntity json,${source.extra} , error: ", e);
      }
    }

    return DataGlucose(
      id: source.id,
      sn: source.uid,
      did: source.did,
      time: DateTime.fromMillisecondsSinceEpoch(source.time * 1000),
      mmolValue: source.value / 100,
      timePoint: TimePoint.getById(source.timePoint),
      source: DataSource.getDataSourceByValue(source.source),
      dataFrom: DataFrom.getDataFromByValue(source.dataFrom),
      extra: extra,
      remarkExtra: source.remark2 ?? '',
    );
  }

  static GlucoseUAValueModel toGlucoseModel(DataGlucose source, String uid,
      {bool modified = false, bool deleted = false}) {
    int value = 0;
    int timePoint = 0;
    int dataSource = 0;
    int dataFrom = 0;
    int dataType = 0;
    value = (source.mmolValue * 100).round();
    timePoint = source.timePoint.id;
    dataSource = source.source.value;
    dataFrom = source.dataFrom.value;
    dataType = DataType.glucose.value;
    String? extra = jsonEncode(source.extra);

    try {
      extra = jsonEncode(source.extra);
    } catch (e) {
      Log.e("toGlucoseModel encode json,${source.extra} , error: ", e);
    }

    return GlucoseUAValueModel(
      id: source.id,
      uid: uid,
      did: source.did,
      time: (source.time.millisecondsSinceEpoch / 1000).floor(),
      value: value,
      timePoint: timePoint,
      source: dataSource,
      dataFrom: dataFrom,
      dataType: dataType,
      extra: extra,
      remark2: source.remarkExtra,
    );
  }

  static DataUricAcid? toUricAcidEntity(GlucoseUAValueModel source) {
    if (source.dataType != DataType.ua.value) {
      return null;
    }

    return DataUricAcid(
      id: source.id,
      sn: source.uid,
      did: source.did,
      time: DateTime.fromMillisecondsSinceEpoch(source.time * 1000),
      mmolValue: source.value,
      timePoint: TimePoint.getById(source.timePoint),
      source: DataSource.getDataSourceByValue(source.source),
      dataFrom: DataFrom.getDataFromByValue(source.dataFrom),
    );
  }

  static GlucoseUAValueModel toUricAcidModel(DataUricAcid source, String uid,
      {bool modified = false, bool deleted = false}) {
    int value = 0;
    int timePoint = 0;
    int dataSource = 0;
    int dataFrom = 0;
    int dataType = 0;
    value = source.mmolValue;
    timePoint = source.timePoint.id;
    dataSource = source.source.value;
    dataFrom = source.dataFrom.value;
    dataType = DataType.ua.value;

    return GlucoseUAValueModel(
      id: source.id,
      uid: uid,
      did: source.did,
      time: (source.time.millisecondsSinceEpoch / 1000).floor(),
      value: value,
      timePoint: timePoint,
      source: dataSource,
      dataFrom: dataFrom,
      dataType: dataType,
    );
  }
}

class GlucoseDataConvertor {
  static GlucoseUAValueModel toModel(Data source, String userSn) {
    int value = 0;
    int timePoint = 0;
    int dataSource = 0;
    int dataFrom = 0;
    int dataType = 0;
    if (source.runtimeType == DataGlucose) {
      source = source as DataGlucose;
      value = (source.mmolValue * 100).round();
      timePoint = source.timePoint.id;
      dataSource = source.source.value;
      dataFrom = source.dataFrom.value;
      dataType = DataType.glucose.value;
    } else if (source.runtimeType == DataUricAcid) {
      source = source as DataUricAcid;
      value = source.mmolValue;
      timePoint = source.timePoint.id;
      dataSource = source.source.value;
      dataFrom = source.dataFrom.value;
      dataType = DataType.ua.value;
    }

    return GlucoseUAValueModel(
      id: source.id,
      uid: source.sn,
      did: source.did,
      time: (source.time.millisecondsSinceEpoch / 1000).floor(),
      value: value,
      timePoint: timePoint,
      source: dataSource,
      dataFrom: dataFrom,
      dataType: dataType,
    );
  }

  static Data toEntity(GlucoseUAValueModel source) {
    if (source.dataType == DataType.glucose.value) {
      return DataGlucose(
        id: source.id,
        sn: source.uid,
        did: source.did,
        time: DateTime.fromMillisecondsSinceEpoch(source.time * 1000),
        mmolValue: source.value / 100,
        timePoint: TimePoint.getById(source.timePoint),
        source: DataSource.getDataSourceByValue(source.source),
        dataFrom: DataFrom.getDataFromByValue(source.dataFrom),
      );
    } else if (source.dataType == DataType.ua.value) {
      return DataUricAcid(
        id: source.id,
        sn: source.uid,
        did: source.did,
        time: DateTime.fromMillisecondsSinceEpoch(source.time * 1000),
        mmolValue: source.value,
        timePoint: TimePoint.getById(source.timePoint),
        source: DataSource.getDataSourceByValue(source.source),
        dataFrom: DataFrom.getDataFromByValue(source.dataFrom),
      );
    }

    return Data(
      id: source.id,
      sn: source.uid,
      did: source.did,
      time: DateTime.fromMillisecondsSinceEpoch(source.time * 1000),
    );
  }

  /// 构建上传数据的map
  static Map<String, dynamic>? jsonFormat(GlucoseUAValueModel data) {
    Map<String, dynamic> dataMap = {
      '_id': data.id,
      'time': data.time,
      'did': data.did,
      'deleted': data.deleted,
      'modif': data.modifiedAt,
      'source': data.source,
      'point': data.timePoint,
    };

    if (data.dataType == DataType.glucose.value) {
      Map<String, dynamic> d = {
        'value': data.value / 100,
        'deleted': data.deleted,
        'device': data.dataFrom,
        'sample_type': 1,
        'sn': '',
        'hwv': 0,
        'swv': 0,
        'voltage': 0,
        'temperature': 0,
      };
      Map<String, dynamic> pack = {
        'B': d,
        'sv': 3.0,
        'deleted': data.deleted,
      };
      dataMap['data'] = pack;
    } else if (data.dataType == DataType.ua.value) {
      return null;
    }

    return dataMap;
  }

  /// 构建上传尿酸数据的map
  static Map<String, dynamic>? uaJsonFormat(GlucoseUAValueModel data) {
    Map<String, dynamic> dataMap = {
      '_id': data.id,
      'time': data.time,
      'did': data.did,
      'deleted': data.deleted,
      'modif': data.modifiedAt,
      'source': data.source,
      'point': data.timePoint,
    };

    if (data.dataType == DataType.ua.value) {
      Map<String, dynamic> d = {
        'value': data.value,
        'deleted': data.deleted,
        'device': data.dataFrom,
        'sample_type': 1,
        'sn': '',
        'hwv': 0,
        'swv': 0,
        'voltage': 0,
        'temperature': 0,
      };
      Map<String, dynamic> pack = {
        'B': d,
        'sv': 3.0,
        'deleted': data.deleted,
      };
      dataMap['data'] = pack;
    } else {
      return null;
    }

    return dataMap;
  }

  /// 根据服务返回的数据结构转换成数据模型列表，老的接口返回可能包含血糖，血压，饮食，运动，用药数据。所以返回数组
  static List<GlucoseUAValueModel> fromJson(Map<String, dynamic> jsonData) {
    List<GlucoseUAValueModel> list = [];

    Map<String, dynamic>? data = jsonData['data'];

    /// 先处理血糖，其他不处理
    if (data != null && data.containsKey('B')) {
      Map<String, dynamic> bsDataJson = data['B'];
      GlucoseUAValueModel bsData = GlucoseUAValueModel(
        id: 0,
        uid: AppContext.to.userSn,
        did: jsonData['did'],
        value: (bsDataJson['value'] * 100).round(),
        time: jsonData['time'],
        timePoint: jsonData['point'],
        source: bsDataJson['source'],
        dataFrom: bsDataJson['device'],
        dataType: DataType.glucose.value,
        modifiedAt: int.parse(jsonData['modif']),
        deleted: bsDataJson['deleted'],
        modified: 0,
        upid: int.parse(jsonData['_upid']),
      );
      list.add(bsData);
    }

    /// TODO 处理其他类型数据

    return list;
  }
}
