/// FileName: reminder_plan_convertor
///
/// @Author: ygc
/// @Date: 2024/8/6 17:59
/// @Description:
import '../../common/time_point.dart';
import '../../db/model/reminder/monitor_plan_model.dart';
import '../../entity/reminder/reminder_plan.dart';

class ReminderPlanConvertor {
  static MonitorPlanModel toModel(ReminderPlan source, String userSn) {
    return MonitorPlanModel(
      id: source.id,
      hour: source.hour,
      minute: source.minute,
      repeated: source.repeated,
      enable: source.enabled,
      timePoint: source.timePoint.id,
      uid: userSn,
      type: 1,
    );
  }

  static ReminderPlan toEntity(MonitorPlanModel source) {
    return ReminderPlan(
      id: source.id,
      hour: source.hour,
      minute: source.minute,
      repeated: source.repeated,
      enabled: source.enable,
      timePoint: TimePoint.getById(source.timePoint),
    );
  }
}
