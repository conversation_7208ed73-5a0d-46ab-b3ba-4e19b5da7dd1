/// FileName: drug_data_convertor
///
/// @Author: ygc
/// @Date: 2025/7/9 15:19
/// @Description:
import 'package:dnurse/app/data/entity/data_drug.dart';
import 'package:dnurse/app/user/entity/drug_type.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../db/model/drug/drug_data_model.dart';

class DrugDataConvertor {
  DrugDataConvertor._();

  static DataDrug toEntity(DrugDataModel model) {
    return DataDrug(
      id: model.id,
      did: model.did,
      time: DateTime.fromMillisecondsSinceEpoch(model.time * 1000),
      sn: model.uid,
      name: model.name,
      amount: model.numCount,
      fromUser: model.fromUser != 0,
      isPlan: model.isPlan != 0,
      drugType: DrugType.getTypeNameById(model.category),
      uuid: model.uuid,
      insulinkPenName: model.insulinkPenName,
    );
  }

  static DrugDataModel toModel(DataDrug entity) {
    return DrugDataModel(
      id: entity.id,
      uid: entity.sn,
      did: entity.did,
      time: entity.time.secondsSinceEpoch,
      name: entity.name,
      numCount: entity.amount.toInt(),
      category: entity.drugType.typeId,
      fromUser: entity.fromUser ? 1 : 0,
      isPlan: entity.isPlan ? 1 : 0,
      uuid: entity.uuid,
      insulinkPenName: entity.insulinkPenName,
    );
  }
}
