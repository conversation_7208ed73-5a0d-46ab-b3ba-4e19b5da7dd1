/// FileName: extra_data_service
///
/// @Author: ygc
/// @Date: 2024/9/19 10:49
/// @Description:
import 'package:dnurse/app/data/pages/sport_record/state.dart';
import 'package:dnurse/app/user/entity/custom_drug_info.dart';
import 'package:dnurse/app/user/entity/drug_info.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:dnurse/resource/generated/l10n.dart';
import 'package:get/get.dart';
import 'package:sqflite/sqflite.dart';

import '../db/extra/ExtraDBManager.dart';
import '../db/model/storage/storage_model.dart';

class ExtraDataService extends GetxService {
  ExtraDataService();

  static ExtraDataService get to => Get.find();

  // 缓存数据库连接
  Database? _database;

  // 获取数据库连接的getter方法
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await ExtraDBManager.instance.getDB(extraDataDB);
    return _database!;
  }

  Future<List<Map<String, dynamic>>> getStorage() async {
    final db = await database;
    final list = await db.query("storage_table");

    return list;
  }

  Future<List<Map<String, dynamic>>> getDrug(String className) async {
    final db = await database;
    final list = await db.query("storage_table",
        where:
            "class =? AND name != '' AND generalname NOT NULL AND generalname != ''",
        whereArgs: [className]);

    return list;
  }

  Future<List<Map<String, dynamic>>> getDrugList(
      List<int> types, String category) async {
    Log.i("getDrugList: $types, $category");
    final db = await database;
    // 先根据type筛选出符合要求的大药物列表
    final list = await db.query("storage_table",
        where:
            "type IN (?,?) AND name!= '' AND generalname NOT NULL AND generalname!= ''",
        whereArgs: types);
    // 再根据具体分类进一步筛选（这里示例简单用category字段匹配，你可能需要根据实际数据库中分类字段情况调整）
    if (category.isNotEmpty) {
      list.retainWhere((drug) => drug['category'] == category);
    }
    return list;
  }

  // 存储药品
  Future<void> saveDrug(CustomDrugInfo drug) async {
    final db = await database;

    await db.insert('storage_table', {
      'did': drug.did,
      'name': drug.name,
      'type': drug.type.typeId,
      'class': drug.classColumn,
      'drugcount': drug.drugcount,
      'abstract': drug.abstract,
      'fromuser': '1',
      'generalname': drug.name,
    });
  }

  // 根据did
  Future<CustomDrugInfo?> getDrugByDid(String did) async {
    try {
      final db = await database;
      final list =
          await db.query('storage_table', where: 'did = ?', whereArgs: [did]);
      if (list.isEmpty) {
        return null;
      }

      final data = list.first;

      return CustomDrugInfo.fromJson(data);
    } catch (e, stackTrace) {
      Log.e('DrugInfo.fromJson失败: $e');
      Log.e('堆栈跟踪: $stackTrace');
      return null;
    }
  }

  // 根据名字
  Future<CustomDrugInfo?> getDrugByName(String name) async {
    final db = await database;
    final list =
        await db.query('storage_table', where: 'name = ?', whereArgs: [name]);
    if (list.isEmpty) {
      return null;
    }
    return CustomDrugInfo.fromJson(list.first);
  }

  Future<List<StorageModel>> getFoodList(int type) async {
    final db = await database;
    final list = await db.query("storage_table",
        where: "type =? AND generalname NOT NULL AND generalname != ''",
        whereArgs: [type]);
    // Log.i("getStorage: $list");
    return list.map((e) => StorageModel.fromJson(e)).toList();
  }

  Future<List<StorageModel>> queryLibFoodBeansByKey(String name,
      {int category = 0}) async {
    final db = await database;

    print('queryLibFoodBeansByKey------->>>>>: $name  $category');

    List<Object?> whereArgs;
    String where ;
    if(category == 0) {
      where = "type = ? and name like ? and class != ?";
      whereArgs = ["1", "%$name%", "自定义食物"];
    } else {
      if(name.isEmpty) {
        where = "type = ? and class = ?";
        whereArgs = ["1",  "自定义食物"];
      } else {
        where = "type = ? and name like ? and class = ?";
        whereArgs = ["1", "%$name%", "自定义食物"];
      }
    }


    final list = await db.query(
      "storage_table",
      where: where,
      whereArgs: whereArgs,
    );

    return list.map((e) => StorageModel.fromJson(e)).toList();
  }

  Future<List<StorageModel>> queryCustomFoodBeansByKey(String name) async {
    final db = await database;
    String where = "type = ? and class = ?";
    List<dynamic> whereArgs = ["1", "自定义食物"];
    if (name.isNotEmpty) {
      where = "$where and name like ?";
      whereArgs.add("%$name%");
    }

    final list = await db.query(
      "storage_table",
      where: where,
      whereArgs: whereArgs,
    );

    return list.map((e) => StorageModel.fromJson(e)).toList();
  }

  // 根据name获取自定义食物
  Future<StorageModel?> getCustomFoodByName(String name) async {
    final db = await database;
    final list = await db.query("storage_table",
        where: "name = ? and type = 1 and class = '自定义食物'", whereArgs: [name]);
    return list.map((e) => StorageModel.fromJson(e)).toList().firstOrNull;
  }

// 保存自定义食物
  Future<void> saveCustomFood(Map<String, dynamic> food) async {
    final db = await database;
    await db.insert("storage_table", food);
  }

  Future<List<StorageModel>> getFoodByName(String name) async {
    Database database = await ExtraDBManager.instance.getDB(extraDataDB);
    final list = await database.query("storage_table",
        where: "name = ? and type = 1", whereArgs: [name]);
    // Log.i("getStorage: $list");
    return list.map((e) => StorageModel.fromJson(e)).toList();
  }

  Future<List<StorageModel>> getSportList() async {
    Database database = await ExtraDBManager.instance.getDB(extraDataDB);
    final list = await database.query("storage_table",
        where: "type =2 AND generalname NOT NULL AND generalname != ''", orderBy: "fromuser desc");
    // Log.i("getStorage: $list");
    return list.map((e) => StorageModel.fromJson(e)).toList();
  }

  Future<String> getSportImageByName(String name) async {
    Database database = await ExtraDBManager.instance.getDB(extraDataDB);
    final list = await database.query("storage_table",
        where: "name = ? and type = 2", whereArgs: [name]);
    if (list.isEmpty) {
      return "";
    }
    return list.first['imageurl'] as String;
  }

  Future<List<Map<String, dynamic>>> fuzzyQuery(String keyword) async {
    final db = await database;
    final list = await db.query("storage_table",
        columns: ["name", "abstract", "did", "type"],
        where: "name LIKE? AND abstract IS NOT NULL AND abstract != ''",
        whereArgs: ['%$keyword%']);
    Log.i("getStorage: $list");
    return list;
  }

  Future<StorageModel?> getDataByDid(String did) async {
    final db = await database;
    final list = await db.query(
      "storage_table",
      where: "did =? ",
      whereArgs: [did],
      limit: 1,
    );
    Log.i("getStorage: $list");

    if (list.isEmpty) {
      return null;
    }

    return StorageModel.fromJson(list.first);
  }

  // 添加关闭数据库的方法
  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  // Future<List<StorageModel>> getSportList() async {
  //   final db = await database;
  //   final list = await db.query("storage_table",
  //       where: "type = 2 AND generalname NOT NULL AND generalname != ''");
  //   return list.map((e) => StorageModel.fromJson(e)).toList();
  // }

// 根据名字获取
  Future<StorageModel?> getSportByName(String name) async {
    final db = await database;
    final list = await db.query("storage_table",
        where: "name = ? and type = 2", whereArgs: [name], );
    return list.map((e) => StorageModel.fromJson(e)).toList().firstOrNull;
  }

  // 添加自定义运动
  Future<void> saveCustomSport(Map<String, dynamic> sport) async {
    final db = await database;

    await db.insert("storage_table", {
      'name': sport['name'],
      'type': 2,
      'class': '运动',
      'generalname': sport['name'],
      'calories': sport['calorie'],
      'sport_time': sport['time'],
      "fromuser": "1",
    });
  }
}
