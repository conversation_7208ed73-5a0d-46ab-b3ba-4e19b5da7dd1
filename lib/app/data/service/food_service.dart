import 'package:dnurse/app/common/service/base_service.dart';
import 'package:dnurse/app/data/db/model/food/food_data_model.dart';
import 'package:dnurse/app/data/db/model/food/food_data_provider.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/service/event_service.dart';
import '../../../framework/utils/log.dart';
import '../api/data_api.dart';
import '../api/dto/food_search/food_search_result_dto.dart';
import '../common/food_type.dart';
import '../convertor/food_data_convertor.dart';
import '../db/model/food/day_calories_model.dart';
import '../entity/data_food.dart';
import '../entity/day_calories_info.dart';
import '../entity/food_type_food_list.dart';
import '../event/data_changed.dart';

class FoodService extends BaseService {
  FoodService();

  late FoodDataProvider _foodDataProvider;

  static FoodService get to => Get.find();

  @override
  void onInit() {
    super.onInit();

    _foodDataProvider = FoodDataProvider.instance;
  }

  Future<DataFood> saveFood(String uid, DataFood dataFood) async {
    // DataModel dataModel = DataConvertor.toFoodModel(dataFood, uid, modified: true);
    FoodDataModel? oldData =
    await _foodDataProvider.getDataByDid(dataFood.did, uid);

    if (oldData != null) {
      dataFood = dataFood.copyWith(
        id: oldData.id,
      );

      FoodDataModel dataModel = FoodDataConvertor.toFoodModel(dataFood);

      await _foodDataProvider.updateDataByDid(dataModel);
      // await uploadData(AppContext.to.userId);
      // await saveFoodTask(
      //     uid, dataFood, dataFood.totalCalorie - oldData!.value);
      DataFood data = FoodDataConvertor.toFoodEntity(dataModel);
      EventService.to.bus.fire(DataChanged.update(
          data: data, oldData: FoodDataConvertor.toFoodEntity(oldData)));
    } else {
      // DataModel? oldData = await _dataProvider.queryByTypeTime(
      //     uid, DataModel.dataTypeFood, dataModel.time);
      // if (oldData != null) {
      //   dataModel = dataModel.copyWith(id: oldData.id);
      //   await _dataProvider.update(dataModel);
      //   dataFood = dataFood.copyWith(id: oldData.id);
      //   EventService.to.bus.fire(DataChanged.update(
      //       data: dataFood, oldData: DataConvertor.toFoodEntity(oldData)));
      // } else {
      // dataModel = dataModel.copyWith(isNew: 1);

      FoodDataModel dataModel = FoodDataConvertor.toFoodModel(dataFood);
      dataModel.markModified();
      dataModel = await _foodDataProvider.insert(dataModel);
      // await uploadData(AppContext.to.userId);

      // await saveFoodTask(uid, dataFood, dataFood.totalCalorie);

      dataFood = dataFood.copyWith(id: dataModel.id);
      EventService.to.bus.fire(DataChanged.add(data: dataFood));
    }

    return dataFood;
  }

  Future<void> deleteFood(String uid, DataFood dataFood) async {
    FoodDataModel? oldData =
    await _foodDataProvider.getDataByDid(dataFood.did, uid);
    if (oldData != null) {
      if (oldData.upId == 0) {
        // 没上传直接删除
        _foodDataProvider.deleteById(oldData.id);
      } else {
        oldData.markModified(markDeleted: true);
        await _foodDataProvider.updateDataByDid(oldData);
      }
    }

    EventService.to.bus.fire(
      DataChanged.deleted(
        data: dataFood,
      ),
    );
  }

//上传饮食数据
  Future<int> uploadData(int uid) async {
    // bool hasNetwork = await NetworkUtils.hasNetwork();
    // if (!hasNetwork) {
    //   return 0;
    // }
    //
    // List<DataModel> dataList = await _dataProvider.getModifiedData(uid);
    // if (dataList.isEmpty) {
    //   return 0;
    // }
    //
    // Log.i("uploadData: $dataList");
    // String didStr = '';
    // for (DataModel dataModel in dataList) {
    //   Log.i("uploadData=======: $dataModel");
    //   if (didStr == '') {
    //     didStr = "${dataModel.did}";
    //   } else {
    //     didStr = "$didStr,${dataModel.did}";
    //   }
    //
    //   JsonResponse response =
    //   await DataApi.uploadData(DataDTOConvertor.toDTO(dataModel));
    //   if (!response.successful || !response.hasData) {
    //     continue;
    //   }
    //
    //   SyncResultDTO syncResultDTO = SyncResultDTO.fromJson(response.data);
    //   if (syncResultDTO.localId > 0 &&
    //       syncResultDTO.localId == dataModel.id &&
    //       syncResultDTO.modifiedAt > 0) {
    //     dataModel = dataModel.copyWith(
    //         modified: 0, modifiedAt: syncResultDTO.modifiedAt, isNew: 0);
    //     await _dataProvider.update(dataModel);
    //   }
    // }
    //
    // Log.i("同步数据的did======= $didStr");
    // int mScore = 0;
    // JsonResponse response = await DataApi.v2FinishTask(didStr);
    // Log.i("取积分接口数据======= $response");
    // if (response.successful) {
    //   mScore = response.data['scoreNum'];
    // }
    //
    // EventService.to.bus
    //     .fire(SyncFinished.upload(uid: uid, dataType: SyncType.glucose, score: mScore));
    //
    return 0;
  }

// FoodTypeFoodList
  Future<int> getFoodCalorie(String sn,
      DateTime startTime,
      DateTime endTime, {
        FoodType? type,
      }) {
    return _foodDataProvider.getFoodCalorie(
        sn, startTime.secondsSinceEpoch, endTime.secondsSinceEpoch,
        type: type?.id);
  }

  Future<List<DataFood>> getFoodByTimeType(String sn,
      DateTime startTime,
      DateTime endTime, {
        FoodType? type,
      }) async {
    // List<FoodTypeFoodList> result = [];

    List<FoodDataModel> foodDataList =
    await _foodDataProvider.getFoodByTimeType(
        sn, startTime.secondsSinceEpoch, endTime.secondsSinceEpoch,
        type: type?.id);

    return foodDataList.map((foodData) =>
        FoodDataConvertor.toFoodEntity(foodData)).toList();
    // FoodType? currentFoodType;
    // List<DataFood> foods = [];
    //
    // for (var foodData in foodDataList) {
    //   DataFood food = FoodDataConvertor.toFoodEntity(foodData);
    //
    //   if (currentFoodType == null) {
    //     currentFoodType = food.foodType;
    //   } else if (currentFoodType != food.foodType) {
    //     result.add(FoodTypeFoodList(currentFoodType, foods));
    //
    //     foods = [];
    //     currentFoodType = food.foodType;
    //   }
    //
    //   foods.add(food);
    // }
    //
    // if (foods.isNotEmpty && currentFoodType != null) {
    //   result.add(FoodTypeFoodList(currentFoodType, foods));
    // }
    //
    // return result;
  }

  Future<List<DayCaloriesInfo>> queryIntakeCaloriesByDay(String sn,
      DateTime startTime,
      DateTime endTime,) async {
    List<DayCaloriesInfo> result = [];

    List<DayCaloriesModel> dataList =
    await _foodDataProvider.queryIntakeCaloriesByDay(
      sn,
      startTime.secondsSinceEpoch,
      endTime.secondsSinceEpoch,
    );

    for (var data in dataList) {
      if (data.day.isEmpty) {
        continue;
      }

      DateTime itemDatetime = DateTime.parse(data.day);
      result.add(DayCaloriesInfo(
        itemDatetime,
        data.calorie,
        IntakeStatus.normal,
      ));
    }

    return result;
  }

  Future<FoodSearchResultDTO?> searchFood(String name,
      int page, {
        int pageSize = 10,
      }) async {
    JsonResponse response = await DataApi.searchFood(
      name: name,
      page: page,
      size: pageSize,
    );
    Log.i("searchFood: $response");
    if (response.successful) {
      if (!response.hasData) {
        return null;
      }

      return FoodSearchResultDTO.fromJson(response.data);
    } else {
      throw DNUError(response.message);
    }
  }
}
