/// FileName: sign_in_service
///
/// @Author: ygc
/// @Date: 2024/10/28 13:57
/// @Description:
///
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/utils/log.dart';
import '../../common/service/base_service.dart';
import '../api/daily_sign_api.dart';
import '../api/dto/sign_in/sign_in_diy_dto.dart';
import '../api/dto/sign_in/sign_in_dto.dart';

class MonthSignIn {
  MonthSignIn(this.month, this.signIns);

  final String month;
  final List<SignInDTO> signIns;

  @override
  String toString() => 'MonthSignIn{month: $month, signIns: $signIns}';
}

class SignInService extends BaseService {
  SignInService()
      : super(
          needAppBootFinished: false,
          needLoginChanged: false,
          needMainHasShow: false,
        );

  static SignInService get to => Get.find();

  Future<List<MonthSignIn>> loadDailyCardData(String date) async {
    JsonResponse response = await DailySignApi.getMonthList(
      month_time: date,
      is_group: "1",
    );

    List<MonthSignIn> result = [];
    Log.i("loadDailyCardData: $response");
    if (response.successful) {
      Map<String, dynamic> data = response.data;
      if (data.isEmpty) {
        return result;
      }

      for (String month in data.keys) {
        List<dynamic> signInList = data[month] as List<dynamic>;

        List<SignInDTO> list =
            signInList.map((e) => SignInDTO.fromJson(e)).toList();
        Log.i("loadDailyCardData list: $list");
        result.add(MonthSignIn(month, list));
      }

      return result;
    } else {
      throw DNUError(response.message);
    }
  }

  Future<SignInDiyDTO?> getDiyConfig() async {
    JsonResponse response = await DailySignApi.getDiyConfig();

    Log.i("getDiyConfig: $response");
    if (response.successful) {
      if (!response.hasData) {
        return null;
      }

      return SignInDiyDTO.fromJson(response.data);
    } else {
      throw DNUError(response.message);
    }
  }
}
