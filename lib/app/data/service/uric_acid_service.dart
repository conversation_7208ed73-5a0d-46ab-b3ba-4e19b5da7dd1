/// FileName: uric_acid_service
///
/// @Author: ygc
/// @Date: 2024/9/3 16:36
/// @Description:
///
import 'package:dnurse/app/data/entity/data_uric_acid.dart';
import 'package:get/get.dart';

import '../../../bootstrap/app.dart';
import '../../../framework/module/sync_handler.dart';
import '../../../framework/service/event_service.dart';
import '../../common/service/base_service.dart';
import '../../user/event/login_changed.dart';
import '../common/uric_acid_target.dart';
import '../convertor/glucose/data_convertor.dart';
import '../db/model/glucose/glucose_ua_provider.dart';
import '../db/model/glucose/glucose_ua_value_model.dart';
import '../entity/data_type.dart';
import '../event/data_changed.dart';

class UricAcidService extends BaseService {
  UricAcidService();

  final currentUricAcidDataTarget = UricAcidTarget.defaultTarget().obs;

  static UricAcidService get to => Get.find();
  late GlucoseProvider glucoseProvider;

  @override
  void onInit() {
    super.onInit();

    glucoseProvider = GlucoseProvider.instance;

    loadUserData();
  }

  @override
  Future<void> onLoginChanged(LoginChanged event) async {
    await super.onLoginChanged(event);
  }

  Future<void> loadUserData() async {}

  Future<DataUricAcid> saveUaData(DataUricAcid data, String uid) async {
    GlucoseUAValueModel model = DataConvertor.toUricAcidModel(data, uid);
    model.markModified();
    GlucoseUAValueModel? rData = await glucoseProvider.getDataByDid(model.did);
    DataChanged dataChanged;
    if (rData != null) {
      await glucoseProvider.updateDataByDid(model);
      dataChanged = DataChanged.update(
          data: data, oldData: DataConvertor.toGlucoseEntity(rData));
    } else {
      model = await glucoseProvider.insert(model);
      data = DataConvertor.toUricAcidEntity(model)!;
      dataChanged = DataChanged.add(data: data);
    }
    EventService.to.bus.fire(dataChanged);
    AppContext.to.doSync(SyncParams(
        onStart: () {},
        onEnd: () {},
        onProgress: (String msg, int i, int i2) {},
        syncType: SyncType.syncUpload));

    return data;
  }

  Future<List<DataUricAcid>> queryDataByTime({
    required String uid,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    List<GlucoseUAValueModel> list = await glucoseProvider.queryUserDataList(
      uid,
      start: startTime,
      end: endTime,
      dataType: DataType.ua,
      orderAsc: true,
    );

    List<DataUricAcid> result = [];
    for (var model in list) {
      DataUricAcid? data = DataConvertor.toUricAcidEntity(model);
      if (data != null) {
        result.add(data);
      }
    }
    return result;
  }

  Future<DataUricAcid?> queryMinMaxByTime({
    required String uid,
    required DateTime startTime,
    required DateTime endTime,
    bool isMax = false,
  }) async {
    GlucoseUAValueModel? model = await glucoseProvider.queryMaxMin(
      uid,
      start: startTime,
      end: endTime,
      dataType: DataType.ua,
      isMax: isMax,
    );

    if (model == null) {
      return null;
    }

    return DataConvertor.toUricAcidEntity(model);
  }

  Future<DataUricAcid?> getLastData(String uid) async {
    GlucoseUAValueModel? model =
        await glucoseProvider.getLastDataWithType(DataType.ua.value, sn: uid);
    if (model == null) {
      return null;
    }

    return DataConvertor.toUricAcidEntity(model);
  }

  Future<double?> getGoodRate(String uid, DateTime startTime, DateTime endTime,
      UricAcidTarget pointTarget) async {
    List<DataUricAcid> dataList =
        await queryDataByTime(uid: uid, startTime: startTime, endTime: endTime);
    if (dataList.isEmpty) {
      return null;
    }

    int goodCount = dataList
        .where((element) =>
            element.mmolValue >= pointTarget.low &&
            element.mmolValue <= pointTarget.high)
        .length;
    return goodCount / dataList.length;
  }
}
