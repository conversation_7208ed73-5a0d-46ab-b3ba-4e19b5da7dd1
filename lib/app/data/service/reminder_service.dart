/// FileName: reminder_service
///
/// @Author: ygc
/// @Date: 2024/8/6 15:00
/// @Description:

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dnurse/app/data/api/reminder_api.dart';
import 'package:dnurse/app/data/entity/reminder/reminder_plan.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse_reminder_plugin/dnurse_reminder_plugin.dart';
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/service/key_value_storage.dart';
import '../../../framework/utils/log.dart';
import '../../common/service/base_service.dart';
import '../../user/event/login_changed.dart';
import '../api/dto/monitor/monitor_plan_dto.dart';
import '../api/dto/monitor/monitor_plan_item_dto.dart';
import '../common/monitor_plan.dart';
import '../common/time_point.dart';
import '../convertor/reminder/reminder_plan_convertor.dart';
import '../db/model/reminder/monitor_plan_model.dart';
import '../db/model/reminder/monitor_plan_provider.dart';

const _storageKeyMonitorEnable = "monitor_enable";
const _storageKeyMonitorType = "monitor_type";
const _storageKeyMonitorPlanCustom = "monitor_plan_custom";

class ReminderService extends BaseService {
  ReminderService();

  static ReminderService get to => Get.find();

  final currentMonitorPlanType = MonitorPlanType.custom.obs;
  final currentMonitorPlan = <ReminderPlan>[].obs;
  final currentMonitorEnable = false.obs;

  late MonitorPlanProvider _monitorPlanProvider;

  @override
  void onInit() {
    super.onInit();

    _monitorPlanProvider = MonitorPlanProvider.instance;

    _loadUserData();
  }

  @override
  Future<void> onLoginChanged(LoginChanged event) async {
    await _loadUserData();
  }

  Future<void> _loadUserData() async {
    currentMonitorEnable.value =
        KeyValueStorage.to.getBool(getStorageKey(_storageKeyMonitorEnable));

    int monitorTypeId =
        KeyValueStorage.to.getInt(getStorageKey(_storageKeyMonitorType));
    currentMonitorPlanType.value =
        MonitorPlanType.fromId(monitorTypeId) ?? MonitorPlanType.custom;

    List<ReminderPlan> plans =
        await _loadMonitorPlanFromDB(AppContext.to.userSn);
    currentMonitorPlan.clear();
    currentMonitorPlan.addAll(plans);
  }

  Future<void> downloadMonitor() async {
    if (AppContext.to.isTemp) {
      return;
    }

    final JsonResponse response = await ReminderApi.getMonitor({"_up": 0});

    Log.i('downloadMonitor: $response');
    if (response.successful) {
      if (response.hasData) {
        MonitorPlanDTO dto = MonitorPlanDTO.fromJson(response.dataMap!);
        if (dto.s != 1 || dto.d == null) {
          return;
        }

        String mpi = dto.d!.mpi.replaceAll('\\"', '"');

        Map<String, dynamic>? data = jsonDecode(mpi);
        if (data == null) {
          return;
        }
        bool reminderMonitorEnable = (int.tryParse(dto.d!.mps) ?? 0) > 0;
        Log.i('reminderMonitorEnable: $reminderMonitorEnable');
        int methodIndex = (int.tryParse(dto.d!.mpc) ?? 0);
        Log.i('methodIndex: $methodIndex');
        bool reminderDrugEnable = (int.tryParse(dto.d!.dps) ?? 0) > 0;
        Log.i('reminderDrugEnable: $reminderDrugEnable');

        List<ReminderPlan> plans = [];
        for (TimePoint timePoint in TimePoint.values) {
          if (timePoint != TimePoint.random) {
            String searchKey = "time${timePoint.id}";
            if (data.containsKey(searchKey)) {
              MonitorPlanItemDTO itemDTO =
                  MonitorPlanItemDTO.fromJson(data[searchKey]);
              Log.i('itemDTO:$timePoint, $itemDTO');
              plans.add(ReminderPlan(
                hour: itemDTO.hour,
                minute: itemDTO.minute,
                enabled: itemDTO.enable,
                timePoint: timePoint,
              ));
            }
          }
        }

        Log.i('plans data: $plans');
        KeyValueStorage.to.setBool(
            getStorageKey(_storageKeyMonitorEnable), reminderMonitorEnable);
        KeyValueStorage.to
            .setInt(getStorageKey(_storageKeyMonitorType), methodIndex);

        _saveMonitorPlanToDB(plans, AppContext.to.userSn);

        currentMonitorEnable.value = reminderMonitorEnable;
        currentMonitorPlanType.value =
            MonitorPlanType.fromId(methodIndex) ?? MonitorPlanType.custom;

        currentMonitorPlan.clear();
        currentMonitorPlan.addAll(plans);
      }
    } else {
      throw DNUError(response.message);
    }
  }

  Future<void> saveMonitorPlan({
    required List<ReminderPlan> plans,
    required bool monitorEnable,
    required MonitorPlanType monitorType,
  }) async {
    Map<String, dynamic> plansData = {};
    for (ReminderPlan plan in plans) {
      String searchKey = "time${plan.timePoint.id}";
      plansData[searchKey] = plan.getData();
    }

    Map<String, dynamic> requestData = {
      "mps": monitorEnable ? 1 : 0,
      "dps": 1,
      "mpc": monitorType.id,
      "mpi": jsonEncode(plansData),
    };

    final JsonResponse response = await ReminderApi.uploadMonitor(requestData);

    Log.i('downloadMonitor: $response');
    if (response.successful) {
      KeyValueStorage.to
          .setBool(getStorageKey(_storageKeyMonitorEnable), monitorEnable);
      KeyValueStorage.to
          .setInt(getStorageKey(_storageKeyMonitorType), monitorType.id);

      _saveMonitorPlanToDB(plans, AppContext.to.userSn);

      currentMonitorEnable.value = monitorEnable;
      currentMonitorPlanType.value = monitorType;

      currentMonitorPlan.clear();
      currentMonitorPlan.addAll(plans);
    } else {
      throw DNUError(response.message);
    }
  }

  void _saveMonitorPlanToDB(List<ReminderPlan> plans, String userSn) async {
    for (ReminderPlan plan in plans) {
      MonitorPlanModel planModel = ReminderPlanConvertor.toModel(plan, userSn);
      MonitorPlanModel? oldPlanModel = await _monitorPlanProvider
          .queryMonitorPlanByTimePoint(userSn, planModel.timePoint);
      if (oldPlanModel == null) {
        planModel = await _monitorPlanProvider.insertMonitorPlan(planModel);
        plan.id = planModel.id;
      } else {
        oldPlanModel.hour = planModel.hour;
        oldPlanModel.minute = planModel.minute;
        oldPlanModel.enable = planModel.enable;
        oldPlanModel.timePoint = planModel.timePoint;

        await _monitorPlanProvider.updateMonitorPlan(oldPlanModel);
        plan.id = oldPlanModel.id;
      }
    }

    _resetMonitorPlanReminder(plans);
  }

  static String K_MONITORPLANREMINDERCLASS = 'MonitorPlanReminder';
  Future<void> _resetMonitorPlanReminder(List<ReminderPlan> plans) async {
    //取消所有监测计划提醒
    DnurseReminderPlugin.getInstance().cancelNotification(K_MONITORPLANREMINDERCLASS);

    bool enable = KeyValueStorage.to.getBool(getStorageKey(_storageKeyMonitorEnable));
    Log.d('操作系统：${Platform.operatingSystem}-${Platform.operatingSystemVersion}');
    if (enable) {
      for (ReminderPlan item in plans) {
        Map<String, dynamic> reminderData = item.getData();
        if (item.enabled == 127) {    //每天都提醒
          DateTime now = DateTime.now();
          DateTime reminderTime = now.startOfDay().add(Duration(hours: item.hour, minutes: item.minute));
          if (now.compareTo(reminderTime) == 1) {
            reminderTime = reminderTime.add(const Duration(days: 1));
          }
          DnurseReminderPlugin.getInstance().addNotification(reminderTime.millisecondsSinceEpoch ~/ 1000, '该测血糖了~', '测量${item.timePoint.name}血糖', K_MONITORPLANREMINDERCLASS, '血糖时间段${item.timePoint.id}', 2, data: reminderData);
        } else {
          if (Platform.operatingSystem == 'ohos') { //鸿蒙系统的闹铃提醒重复设置和Android/iOS不一样，分开设置
            if (item.enabled > 0) {
              DateTime now = DateTime.now();
              DateTime reminderTime = now.startOfDay().add(Duration(hours: item.hour, minutes: item.minute));
              DnurseReminderPlugin.getInstance().addNotification(reminderTime.millisecondsSinceEpoch ~/ 1000, '该测血糖了~', '测量${item.timePoint.name}血糖', K_MONITORPLANREMINDERCLASS, '血糖时间段${item.timePoint.id}', 1, data: reminderData);
            }
          } else {
            DateTime weekDayTime = DateTime.now().startOfWeek();
            for (int i = 0; i < 7; i ++) {
              if (1 << i & item.enabled > 0) {
                DateTime reminderTime = weekDayTime.add(Duration(days:i, hours: item.hour, minutes: item.minute));
                if (DateTime.now().compareTo(reminderTime) == 1) {    //如果设置以前的闹钟，则往后推一周
                  reminderTime = reminderTime.add(const Duration(days: 7));
                }
                DnurseReminderPlugin.getInstance().addNotification(reminderTime.millisecondsSinceEpoch ~/ 1000, '该测血糖了~', '测量${item.timePoint.name}血糖', K_MONITORPLANREMINDERCLASS, '血糖时间段${item.timePoint.id}', 1, data: reminderData);
              }
            }
          }
        }
      }

    }
  }

  Future<List<ReminderPlan>> _loadMonitorPlanFromDB(String userSn) async {
    List<MonitorPlanModel> plans =
        await _monitorPlanProvider.queryMonitorPlan(userSn);
    Log.i('_loadMonitorPlanFromDB: $plans');
    if (plans.isEmpty) {
      List<ReminderPlan> planList = [];
      for (var element in TimePoint.normalValues) {
        ReminderPlan reminderPlan = ReminderPlan.getDefault(element);
        MonitorPlanModel planModel =
            ReminderPlanConvertor.toModel(reminderPlan, userSn);
        planModel = await _monitorPlanProvider.insertMonitorPlan(planModel);
        reminderPlan.id = planModel.id;

        planList.add(reminderPlan);
      }

      return planList;
    }

    return plans.map((e) => ReminderPlanConvertor.toEntity(e)).toList();
  }

  Future<void> setDefaultMonitorPlan(String userSn) async {
    List<ReminderPlan> plans = await _loadMonitorPlanFromDB(userSn);
    int enableSum =
        plans.map((e) => e.enabled).reduce((value, element) => value + element);
    if (enableSum > 0) {
      return;
    }

    for (ReminderPlan reminderPlan in plans) {
      if (reminderPlan.timePoint == TimePoint.breakfastBefore ||
          reminderPlan.timePoint == TimePoint.breakfastAfter ||
          reminderPlan.timePoint == TimePoint.lunchAfter ||
          reminderPlan.timePoint == TimePoint.supperAfter) {
        reminderPlan.enabled = reminderAllDayOpen;
        reminderPlan.repeated = reminderAllDayOpen;

        MonitorPlanModel planModel =
            ReminderPlanConvertor.toModel(reminderPlan, userSn);
        await _monitorPlanProvider.updateMonitorPlan(planModel);
      }
    }
  }

  Future<void> loadUserCustomReminderPlan(
    String userSn,
    List<ReminderPlan> plans,
  ) async {
    String planStr = KeyValueStorage.to
        .getString(getStorageKey(_storageKeyMonitorPlanCustom, userSn: userSn));
    if (planStr.isEmpty) {
      for (ReminderPlan plan in plans) {
        if (plan.timePoint == TimePoint.breakfastBefore) {
          plan.enabled = 2;
          plan.repeated = 2;
        } else if (plan.timePoint == TimePoint.supperAfter) {
          plan.enabled = 8;
          plan.repeated = 8;
        } else {
          plan.enabled = 0;
          plan.repeated = 0;
        }
      }
    } else {
      Map<TimePoint, ReminderPlan> planMap = Map.fromEntries(
        plans.map((obj) => MapEntry(obj.timePoint, obj)),
      );

      for (String planItemStr in planStr.split(",")) {
        List<String> planItem = planItemStr.split("-");
        if (planItem.length != 3) {
          continue;
        }
        try {
          TimePoint timePoint = TimePoint.values[int.parse(planItem[0])];
          ReminderPlan plan = planMap[timePoint]!;
          plan.enabled = int.parse(planItem[1]);
          plan.repeated = int.parse(planItem[2]);
        } catch (e) {
          Log.e('loadUserCustomReminderPlan error: $e');
        }
      }
    }
  }

  Future<void> saveUserCustomReminderPlan(
    String userSn,
    List<ReminderPlan> plans,
  ) async {
    String planStr = plans
        .map((e) => "${e.timePoint.id}-${e.enabled}-${e.repeated}")
        .join(",");
    if (planStr.isNotEmpty) {
      KeyValueStorage.to.setString(
          getStorageKey(_storageKeyMonitorPlanCustom, userSn: userSn), planStr);
    }
  }
}
