import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../framework/network/response.dart';
import '../../../framework/service/event_service.dart';
import '../../../framework/utils/log.dart';
import '../api/data_api.dart';
import '../convertor/sport_data_convertor.dart';
import '../db/model/food/day_calories_model.dart';
import '../db/model/sport/sport_data_model.dart';
import '../db/model/sport/sport_data_provider.dart';
import '../entity/data_sport.dart';
import '../entity/day_calories_info.dart';
import '../event/data_changed.dart';

class SportService extends GetxService {
  SportService();

  static SportService get to => Get.find();

  late SportDataProvider _sportDataProvider;

  @override
  void onInit() {
    super.onInit();

    _sportDataProvider = SportDataProvider.instance;
  }

  saveSportList(List<SportDataModel> sports) async {
    for (SportDataModel item in sports) {
      item.markModified();
      if (item.id > 0 && item.did != null && item.did.isNotEmpty) {
        //更新表数据
        _sportDataProvider.updateDataByDid(item);
      } else {
        //插入数据
        int now = (DateTime.now().millisecondsSinceEpoch / 1000).round();
        item = item.copyWith(
            did: 'S${DateTime.now().microsecondsSinceEpoch}', time: now);
        _sportDataProvider.insert(item);
      }
    }
  }

  Future<DataSport> saveSport(DataSport dataSport) async {
    SportDataModel? oldData =
        await _sportDataProvider.getDataByDid(dataSport.did, dataSport.sn);

    if (oldData != null) {
      dataSport = dataSport.copyWith(
        id: oldData.id,
      );

      SportDataModel dataModel = SportDataConvertor.toModel(dataSport);

      await _sportDataProvider.updateDataByDid(dataModel);
      // await uploadData(AppContext.to.userId);
      // await saveFoodTask(
      //     uid, dataFood, dataFood.totalCalorie - oldData!.value);
      DataSport data = SportDataConvertor.toEntity(dataModel);
      EventService.to.bus.fire(DataChanged.update(
          data: data, oldData: SportDataConvertor.toEntity(oldData)));
    } else {
      // DataModel? oldData = await _dataProvider.queryByTypeTime(
      //     uid, DataModel.dataTypeFood, dataModel.time);
      // if (oldData != null) {
      //   dataModel = dataModel.copyWith(id: oldData.id);
      //   await _dataProvider.update(dataModel);
      //   dataFood = dataFood.copyWith(id: oldData.id);
      //   EventService.to.bus.fire(DataChanged.update(
      //       data: dataFood, oldData: DataConvertor.toFoodEntity(oldData)));
      // } else {
      // dataModel = dataModel.copyWith(isNew: 1);

      SportDataModel dataModel = SportDataConvertor.toModel(dataSport);
      dataModel.markModified();
      dataModel = await _sportDataProvider.insert(dataModel);
      // await uploadData(AppContext.to.userId);

      // await saveFoodTask(uid, dataFood, dataFood.totalCalorie);

      dataSport = dataSport.copyWith(id: dataModel.id);
      EventService.to.bus.fire(DataChanged.add(data: dataSport));
    }

    return dataSport;
  }

  Future<List<DataSport>> getSportList(
    String uid, {
    DateTime? start,
    DateTime? end,
  }) async {
    final list =
        await _sportDataProvider.queryUserDataList(uid, start: start, end: end);

    return list.map((item) => SportDataConvertor.toEntity(item)).toList();
  }

  Future<void> deleteSport(DataSport dataSport) async {
    SportDataModel? oldData =
        await _sportDataProvider.getDataByDid(dataSport.did, dataSport.sn);

    if (oldData != null) {
      if (oldData.upid == 0) {
        // 没上传直接删除
        _sportDataProvider.deleteById(oldData.id);
      } else {
        oldData.markModified(markDeleted: true);
        await _sportDataProvider.updateDataByDid(oldData);
      }
    }

    EventService.to.bus.fire(
      DataChanged.deleted(
        data: dataSport,
      ),
    );
  }

  Future<List<DayCaloriesInfo>> queryConsumeCaloriesByDay(
    String sn,
    DateTime startTime,
    DateTime endTime,
  ) async {
    List<DayCaloriesInfo> result = [];

    List<DayCaloriesModel> dataList =
        await _sportDataProvider.queryConsumeCaloriesByDay(
      sn,
      startTime.secondsSinceEpoch,
      endTime.secondsSinceEpoch,
    );

    for (var data in dataList) {
      if (data.day.isEmpty) {
        continue;
      }

      DateTime itemDatetime = DateTime.parse(data.day);
      result.add(DayCaloriesInfo(
        itemDatetime,
        data.calorie,
        IntakeStatus.normal,
      ));
    }

    return result;
  }

  Future<int> syncDataUpload(String uid) async {
    // 上传运动数据
    List<SportDataModel> sportDataModifyList =
    await _sportDataProvider.queryUserModifyList(uid);
    if (sportDataModifyList.isEmpty) {
      return 0;
    }

    List<Map<String, dynamic>> postList = [];
    for (SportDataModel item in sportDataModifyList) {
      Map<String, dynamic>? jsonData = SportDataConvertor.jsonFormat(item);
      if (jsonData != null) {
        postList.add(jsonData);
      }
    }
    JsonResponse response1 = await DataApi.uploadSportData(postList);
    Log.d('上传运动数据结果：$response1 ');
    if (response1.successful) {
      final responseList = response1.dataMap!['data'] as List;

      final upidMap = {for (var res in responseList) res['_id']: res['upid']};

      for (SportDataModel item in sportDataModifyList) {
        _sportDataProvider.markModified(item.id, modified: false);
      }
    }

    return 0;
  }

  // 同步运动数据
  Future syncDataDownload(String uid) async {
    // int upid = await _sportDataProvider.queryMaxUpid(uid,
    //     start: DateTime.fromMillisecondsSinceEpoch(startTime * 1000),
    //     end: DateTime.fromMillisecondsSinceEpoch(endTime * 1000));

    DateTime endTime = DateTime.now().addDay(1).startOfDay();
    DateTime startTime = endTime.addMonth(-3);

    int upid = 0;
    int offset = 1;
    int size = 300;
    bool hasMore = true;
    while (hasMore) {
      JsonResponse response = await DataApi.syncGetSportData(
        startTime.secondsSinceEpoch,
        endTime.secondsSinceEpoch,
        upid,
        offset: offset,
        size: size,
      );

      Log.d('同步运动取数据数量：$response');
      if (response.successful && response.hasData) {
        List<dynamic> dataList = response.dataMap!['list'];
        for (Map<String, dynamic> data in dataList) {
          SportDataModel dataModel = SportDataConvertor.fromJson(data, uid);

          await _sportDataProvider.insertOrUpdateData(dataModel);
        }
        hasMore = response.dataMap!['more'] == 1;
        if (hasMore) {
          offset++; // 准备获取下一页
        }
      } else {
        // 请求失败，终止循环
        hasMore = false;
        Log.e('获取数据失败：${response.message}');
      }
    }
  }
}
