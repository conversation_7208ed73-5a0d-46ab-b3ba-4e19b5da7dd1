/// FileName: glucose_service
///
/// @Author: ygc
/// @Date: 2024/8/8 13:32
/// @Description:
import 'dart:async';

import 'package:dnurse/app/data/api/data_target_api.dart';
import 'package:dnurse/app/data/common/time_point.dart';
import 'package:dnurse/app/data/convertor/glucose/data_convertor.dart';
import 'package:dnurse/app/data/db/model/glucose/target/data_target_model.dart';
import 'package:dnurse/app/data/entity/data_glucose.dart';
import 'package:dnurse/app/data/entity/reminder/reminder_plan.dart';
import 'package:dnurse/app/data/service/reminder_service.dart';
import 'package:dnurse/app/user/entity/user_info.dart';
import 'package:dnurse/app/user/service/user_info_service.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/module/sync_handler.dart';
import 'package:dnurse/framework/service/event_service.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../framework/exception/dnu_error.dart';
import '../../../framework/network/response.dart';
import '../../../framework/utils/log.dart';
import '../../common/entity/diabetes_type.dart';
import '../../common/service/base_service.dart';
import '../../user/entity/recommend_target_values.dart';
import '../../user/event/login_changed.dart';
import '../api/dto/data/glucose/glucose_data_target_dto.dart';
import '../common/data_target.dart';
import '../common/data_utils.dart';
import '../convertor/glucose/data_target_convertor.dart';
import '../db/model/cgm/cgm_data_model.dart';
import '../db/model/cgm/cgm_data_provider.dart';
import '../db/model/glucose/glucose_ua_provider.dart';
import '../db/model/glucose/glucose_ua_value_model.dart';
import '../db/model/glucose/target/data_target_provider.dart';
import '../entity/data_glucose_per_day.dart';
import '../entity/data_source.dart';
import '../entity/data_type.dart';
import '../entity/user_glucose_good_rate.dart';
import '../event/data_changed.dart';

class GlucoseService extends BaseService {
  GlucoseService();

  final currentDataTarget = DataTarget.defaultTarget().obs;

  // final currentUricAcidDataTarget = UricAcidTarget.defaultTarget().obs;

  static GlucoseService get to => Get.find();
  late GlucoseDataTargetProvider _glucoseDataTargetProvider;
  late GlucoseProvider _glucoseProvider;
  late CGMDataProvider _cgmDataProvider;

  @override
  void onInit() {
    super.onInit();

    _glucoseDataTargetProvider = GlucoseDataTargetProvider.instance;
    _glucoseProvider = GlucoseProvider.instance;
    _cgmDataProvider = CGMDataProvider.instance;

    loadUserData();
  }

  @override
  Future<void> onLoginChanged(LoginChanged event) async {
    await super.onLoginChanged(event);
    await loadCurrentUserDataTarget();
  }

  Future<void> loadUserData() async {
    await loadCurrentUserDataTarget();
  }

  Future<void> loadCurrentUserDataTarget() async {
    DataTarget? dataTarget = await loadDataTarget(AppContext.to.userSn);
    if (dataTarget != null) {
      currentDataTarget.value = dataTarget;
    } else {
      currentDataTarget.value = DataTarget.defaultTarget();
    }
  }

  Future<DataTarget?> loadDataTarget(
    String userSn, {
    bool recommendTarget = true,
  }) async {
    GlucoseDataTargetModel? model =
        await _glucoseDataTargetProvider.queryTarget(userSn);
    if (model == null) {
      if (!recommendTarget) {
        return null;
      }

      UserInfo userInfo = UserInfoService.to.currentUserInfo.value;
      RecommendTargetValues recommendTargetValues =
          RecommendTargetValues.getByGroup(userInfo.userGroup);

      return DataTarget(
        diastatic: recommendTargetValues.analogSugar,
        dawnLow: recommendTargetValues.dawnLow,
        dawnHigh: recommendTargetValues.dawnHigh,
        emptyLow: recommendTargetValues.emptyStomachLow,
        emptyHigh: recommendTargetValues.emptyStomachHigh,
        mealBeforeLow: recommendTargetValues.beforeMealLow,
        mealBeforeHigh: recommendTargetValues.beforeMealHigh,
        mealAfterLow: recommendTargetValues.afterMealLow,
        mealAfterHigh: recommendTargetValues.afterMealHigh,
        sleepLow: recommendTargetValues.sleepLow,
        sleepHigh: recommendTargetValues.sleepHigh,
      );
    } else {
      Log.i("loadDataTarget: $model");
      return GlucoseDataTargetConvertor.toEntity(model);
    }
  }

  Future<DataTarget?> downloadDataTarget() async {
    String userSn = AppContext.to.userSn;
    final JsonResponse response = await DataTargetApi.getTarget();
    Log.i('downloadDataTarget: $response');
    if (response.successful) {
      if (!response.hasData) {
        throw DNUError("请求结果无效");
      }

      GlucoseDataTargetDTO dto = GlucoseDataTargetDTO.fromJson(response.data);
      Log.i('downloadDataTarget dto: $dto');
      DataTarget target = DataTarget(
        diastatic: dto.diastatic,
        dawnLow: dto.dawnLow,
        dawnHigh: dto.dawnHigh,
        emptyLow: dto.emptyLow,
        emptyHigh: dto.emptyHigh,
        mealBeforeLow: dto.mealBeforeLow,
        mealBeforeHigh: dto.mealBeforeHigh,
        mealAfterLow: dto.mealAfterLow,
        mealAfterHigh: dto.mealAfterHigh,
        sleepLow: dto.sleepLow,
        sleepHigh: dto.sleepHigh,
        userModified: dto.userModified == 1,
      );

      if (userSn == AppContext.to.userSn) {
        target.id = currentDataTarget.value.id;
        saveDataTarget(target, userSn);
        currentDataTarget.update((val) {
          currentDataTarget.value = target;
        });
      }
      Log.i('downloadDataTarget target: $target');
      return target;
    } else {
      throw DNUError(response.message);
    }
  }

  Future<void> syncDataTarget(DataTarget target) async {
    String userSn = AppContext.to.userSn;

    GlucoseDataTargetDTO dto = GlucoseDataTargetDTO(
      diastatic: target.diastatic,
      dawnLow: target.dawnLow,
      dawnHigh: target.dawnHigh,
      emptyLow: target.emptyLow,
      emptyHigh: target.emptyHigh,
      mealBeforeLow: target.mealBeforeLow,
      mealBeforeHigh: target.mealBeforeHigh,
      mealAfterLow: target.mealAfterLow,
      mealAfterHigh: target.mealAfterHigh,
      sleepLow: target.sleepLow,
      sleepHigh: target.sleepHigh,
      userModified: target.userModified ? 1 : 0,
      modifyTime: DateTime.now().secondsSinceEpoch,
    );
    Map<String, dynamic> targetJson = dto.toJson();
    targetJson['userModify'] = target.userModified ? 1 : 0;
    targetJson['modifyTime'] = DateTime.now().secondsSinceEpoch;

    Map<String, dynamic> data = {
      "target": targetJson,
    };

    Log.i("syncDataTarget: $data");
    final JsonResponse response = await DataTargetApi.saveTarget(data);
    Log.i('syncDataTarget: $response');
    if (!response.successful) {
      throw DNUError(response.message);
    }
    await saveDataTarget(target, userSn);
    if (userSn == AppContext.to.userSn) {
      currentDataTarget.value = target;
    }
  }

  Future<void> saveDataTarget(DataTarget dataTarget, String userSn) async {
    GlucoseDataTargetModel model =
        GlucoseDataTargetConvertor.toModel(dataTarget, userSn);
    if (model.id > 0) {
      await _glucoseDataTargetProvider.updateTarget(model);
    } else {
      model = await _glucoseDataTargetProvider.insertTarget(model);
      dataTarget.id = model.id;
    }
  }

  TimePoint getCurUserTimePointByDateTime(DateTime dateTime) {
    return getTimePointByDateTime(
        dateTime, ReminderService.to.currentMonitorPlan);
  }

  TimePoint getTimePointByDateTime(
      DateTime dateTime, List<ReminderPlan> plans) {
    List<TimePoint> timePointList = [
      TimePoint.dawn,
      TimePoint.breakfastBefore,
      TimePoint.breakfastAfter,
      TimePoint.lunchBefore,
      TimePoint.lunchAfter,
      TimePoint.supperBefore,
      TimePoint.supperAfter,
      TimePoint.sleep,
    ];

    if (plans.isEmpty) {
      plans = timePointList.map((e) => ReminderPlan.getDefault(e)).toList();
    }

    int curMinutes = dateTime.hour * 60 + dateTime.minute;
    Map<TimePoint, ReminderPlan> timePointMap = Map.fromEntries(
        plans.map((element) => MapEntry(element.timePoint, element)));

    List<ReminderPlan> orderedReminderPlan = timePointList
        .map((e) => timePointMap[e] ?? ReminderPlan.getDefault(e))
        .toList();

    List<int> timePointMinutes = orderedReminderPlan
        .map((element) => element.hour * 60 + element.minute)
        .toList();
    if (curMinutes >= timePointMinutes.first &&
        curMinutes <= timePointMinutes.last) {
      for (int i = 0; i < timePointMinutes.length - 1; i++) {
        int offset1 = (curMinutes - timePointMinutes[i]);
        int offset2 = (curMinutes - timePointMinutes[i + 1]);
        if (offset1 >= 0 && offset2 <= 0) {
          if (offset1.abs() <= offset2.abs()) {
            return orderedReminderPlan[i].timePoint;
          } else {
            return orderedReminderPlan[i + 1].timePoint;
          }
        }
      }
      return TimePoint.breakfastBefore;
    } else {
      int rangeStart = timePointMinutes.last;
      int rangeEnd = timePointMinutes.first + 1440;

      if (curMinutes < timePointMinutes.last) {
        curMinutes += 1440;
      }

      int offset1 = (curMinutes - rangeStart).abs();
      int offset2 = (curMinutes - rangeEnd).abs();
      if (offset1 <= offset2) {
        return orderedReminderPlan.last.timePoint;
      } else {
        return orderedReminderPlan.first.timePoint;
      }
    }
  }

  Future<DataGlucose> saveGlucose(
    DataGlucose dataGlucose,
    String uid, {
    bool sync = true,
  }) async {
    GlucoseUAValueModel model = DataConvertor.toGlucoseModel(dataGlucose, uid);
    model.markModified();
    GlucoseUAValueModel? rData = await _glucoseProvider.getDataByDid(model.did);
    DataChanged dataChanged;
    if (rData != null) {
      await _glucoseProvider.updateDataByDid(model);
      dataChanged = DataChanged.update(
          data: dataGlucose, oldData: DataConvertor.toGlucoseEntity(rData));
    } else {
      model = await _glucoseProvider.insert(model);
      dataGlucose = DataConvertor.toGlucoseEntity(model)!;
      dataChanged = DataChanged.add(data: dataGlucose);
    }

    EventService.to.bus.fire(dataChanged);

    if (sync) {
      AppContext.to.doSync(SyncParams(
          onStart: () {},
          onEnd: () {},
          onProgress: (String msg, int i, int i2) {},
          syncType: SyncType.syncUpload));
    }

    return dataGlucose;
  }

  Future<List<DataGlucose>> queryGlucoseByTime({
    required String uid,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    List<GlucoseUAValueModel> list = await _glucoseProvider.queryUserDataList(
      uid,
      start: startTime,
      end: endTime,
      dataType: DataType.glucose,
      orderAsc: true,
    );

    List<DataGlucose> result = [];
    for (var model in list) {
      DataGlucose? data = DataConvertor.toGlucoseEntity(model);
      if (data != null) {
        result.add(data);
      }
    }
    return result;
  }

  Future<DataGlucose?> queryGlucoseMinMaxByTime({
    required String uid,
    required DateTime startTime,
    required DateTime endTime,
    bool isMax = false,
  }) async {
    GlucoseUAValueModel? model = await _glucoseProvider.queryMaxMin(
      uid,
      start: startTime,
      end: endTime,
      dataType: DataType.glucose,
      isMax: isMax,
    );

    if (model == null) {
      return null;
    }

    return DataConvertor.toGlucoseEntity(model);
  }

  Future<DataGlucose?> getLastData(String uid) async {
    GlucoseUAValueModel? model = await _glucoseProvider
        .getLastDataWithType(DataType.glucose.value, sn: uid);
    if (model == null) {
      return null;
    }

    return DataConvertor.toGlucoseEntity(model);
  }

  Future<DataGlucose?> getUserGlucoseBySourceAndTime(
      String userSn, DataSource source, int time) async {
    //
    GlucoseUAValueModel? model = await _glucoseProvider
        .getUserGlucoseDataBySourceAndTime(userSn, source.value, time);
    if (model == null) {
      return null;
    }

    return DataConvertor.toGlucoseEntity(model);
  }

  Future<int> getContourLastSequenceIndex(String uid, String contourSn) async {
    List<GlucoseUAValueModel>? list =
        await _glucoseProvider.getContourData(uid, contourSn);

    Log.i("getContourLastSequenceIndex: $list");

    if (list == null || list.isEmpty) {
      return 0;
    }

    int max = 0;
    for (var item in list) {
      String remark = item.remark2 ?? '';
      if (remark.isEmpty || !remark.contains('-')) {
        continue;
      }

      remark = remark.split('-')[0];
      int? index = int.tryParse(remark);
      if (index == null) {
        continue;
      }

      if (index > max) {
        max = index;
      }
    }

    return max;
  }

  Future<List<DataGlucosePerDay>?> getDeviceSyncDataByDid(
      String uid, List<String> didList) async {
    List<GlucoseDataPerDay>? list =
        await _glucoseProvider.getDeviceSyncDataByDid(uid, didList);
    if (list == null) {
      return null;
    }
    Log.d("getDeviceSyncDataByDid: $list");

    List<DataGlucosePerDay> result = [];
    for (var item in list) {
      DateTime dateTime = DateTime.parse(item.date);
      List<DataGlucose> dataList = [];
      for (var glucoseItem in item.glucoseList) {
        DataGlucose? data = DataConvertor.toGlucoseEntity(glucoseItem);
        if (data != null) {
          dataList.add(data);
        }
      }

      if (dataList.isNotEmpty) {
        result.add(DataGlucosePerDay(date: dateTime, glucoseList: dataList));
      }
    }

    return result;
  }

  ///  计算时间段内的模拟糖化
  Future<double> getUserSimulate(
      String userSn, DateTime startTime, DateTime endTime) async {
    int avgValue = await _glucoseProvider.getGlucoseLatestAverageValue(
        userSn, startTime.secondsSinceEpoch, endTime.secondsSinceEpoch);
    return calculateSimulate(avgValue);
  }

  double calculateSimulate(int avgValue) {
    if (avgValue == 0) {
      return 0;
    }

    double value = DataUtils.convertToEntityValue(avgValue) * 18;
    double simulate = ((value + 46.7) / 28.7);
    simulate = simulate.toPrecision(1);
    return simulate;
  }

  Future<UserGlucoseGoodRate> calculateGoodRate(
    String userSn,
    DateTime dayTime,
    DiabetesType? diabetesType,
  ) async {
    double targetMini = 3.9;
    double targetMax = 10;
    if (diabetesType == DiabetesType.pregnancy) {
      targetMini = 3.5;
      targetMax = 7.8;
    }

    DateTime startTime = dayTime.startOfDay();
    DateTime endTime = dayTime.addDay(1);

    int goodCount = 0;
    int totalCount = 0;

    List<CGMDataModel> cgmDataList = await _cgmDataProvider.queryUserDataList(
      userSn,
      start: startTime,
      end: endTime,
    );

    totalCount = cgmDataList.length;

    for (var item in cgmDataList) {
      if (item.value >= targetMini && item.value <= targetMax) {
        goodCount++;
      }
    }

    List<GlucoseUAValueModel> fingerDataList =
        await _glucoseProvider.queryUserDataListOrderByTimePoint(
      userSn,
      start: startTime,
      end: endTime,
      dataType: DataType.glucose,
    );
    if (fingerDataList.isNotEmpty) {
      TimePoint? timePoint;
      List<int> values = [];

      for (var item in fingerDataList) {
        if (timePoint == null) {
          timePoint = TimePoint.getById(item.timePoint);
        } else {
          if (timePoint.id != item.timePoint) {
            List<int> goods = _calculateGoodTimePoint(
              timePoint,
              values,
              targetMini,
              targetMax,
            );

            goodCount += goods[0];
            totalCount += goods[1];

            timePoint = TimePoint.getById(item.timePoint);
            values = [];
          }
        }

        values.add(item.value);
      }

      if (timePoint != null) {
        List<int> goods = _calculateGoodTimePoint(
          timePoint,
          values,
          targetMini,
          targetMax,
        );

        goodCount += goods[0];
        totalCount += goods[1];
      }
    }

    double goodRate = 0;
    UserGlucoseGoodRateType type = UserGlucoseGoodRateType.none;
    if (totalCount > 0) {
      goodRate = goodCount / totalCount;
      double standard = 0.7;
      if (diabetesType == DiabetesType.earlier) {
        standard = 0.9;
      }
      if (goodRate >= standard) {
        type = UserGlucoseGoodRateType.pass;
      } else {
        type = UserGlucoseGoodRateType.failed;
      }
    }

    return UserGlucoseGoodRate(
      dayTime: startTime,
      goodRate: goodRate,
      type: type,
    );
  }

  List<int> _calculateGoodTimePoint(
    TimePoint timePoint,
    List<int> dataList,
    double targetMini,
    double targetMax,
  ) {
    Map<TimePoint, int> timePointMap = {
      TimePoint.breakfastBefore: 84,
      TimePoint.breakfastAfter: 36,
      TimePoint.lunchBefore: 24,
      TimePoint.lunchAfter: 48,
      TimePoint.supperBefore: 24,
      TimePoint.supperAfter: 48,
      TimePoint.sleep: 24,
    };

    if (timePointMap.containsKey(timePoint)) {
      int needCount = timePointMap[timePoint]!;
      if (dataList.length < needCount) {
        int repeatCount = needCount ~/ dataList.length;
        int moreCount = needCount % dataList.length;
        List<int> newList = [];
        int moreAddIndex = 0;
        for (var item in dataList) {
          newList.addAll(List.generate(repeatCount, (index) => item));
          if (moreAddIndex < moreCount) {
            newList.add(item);
          }
        }

        dataList = newList;
      }
    }

    int goodCount = 0;
    for (var item in dataList) {
      double v = item / 100;
      if (v >= targetMini && v <= targetMax) {
        goodCount++;
      }
    }

    return [goodCount, dataList.length];
  }
}
