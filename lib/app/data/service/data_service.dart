import 'dart:async';

import 'package:dnurse/app/data/api/data_api.dart';
import 'package:dnurse/app/data/convertor/sport_data_convertor.dart';
import 'package:dnurse/app/data/db/model/cgm/cgm_data_package_provider.dart';
import 'package:dnurse/app/data/db/model/sport/sport_data_model.dart';
import 'package:dnurse/app/data/db/model/sport/sport_data_provider.dart';
import 'package:dnurse/app/data/event/bs_state_changed.dart';
import 'package:dnurse/app/data/event/data_changed.dart';
import 'package:dnurse/app/data/event/spug_state_changed.dart';
import 'package:dnurse/framework/module/sync_handler.dart';
import 'package:dnurse_bs_test_plugin/dnurse_bs_test_plugin.dart';
import 'package:dnurse_spug_plugin/dnurse_spug_plugin.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../bootstrap/app.dart';
import '../../../framework/network/response.dart';
import '../../../framework/service/event_service.dart';
import '../../../framework/utils/log.dart';
import '../../user/event/login_changed.dart';
import '../convertor/glucose/data_convertor.dart';
import '../db/model/cgm/cgm_data_model.dart';
import '../db/model/cgm/cgm_data_package_model.dart';
import '../db/model/cgm/cgm_data_provider.dart';
import '../db/model/glucose/data_sync_time_interval_model.dart';
import '../db/model/glucose/data_sync_time_interval_provider.dart';
import '../db/model/glucose/glucose_ua_provider.dart';
import '../db/model/glucose/glucose_ua_value_model.dart';
import '../entity/Data.dart';
import '../entity/data_type.dart';

class DataService extends GetxService {
  DataService();

  static DataService get to => Get.find();

  late StreamSubscription loginChangedSubscription;

  SPUGStateChanged lastSPUGState = SPUGStateChanged(98, {});
  final _dnurseSpugPlugin = DnurseSpugPlugin.getInstance();
  BSStateChanged lastBSState = BSStateChanged(0, {});
  final _dnurseBSPlugin = DnurseBsTestPlugin.getInstance();
  late GlucoseProvider glucoseProvider;
  late CGMDataPackageProvider cgmDataPackageProvider;
  late CGMDataProvider cgmDataProvider;
  late SportDataProvider sportProvider;

  @override
  void onInit() {
    super.onInit();

    _initSubscription();

    glucoseProvider = GlucoseProvider.instance;
    cgmDataPackageProvider = CGMDataPackageProvider.instance;
    cgmDataProvider = CGMDataProvider.instance;
    sportProvider = SportDataProvider.instance;

    _dnurseSpugPlugin.setNativeCallback(_onSPUGDeviceStateChanged);
    _dnurseBSPlugin.setNativeCallback(_onBloodTesterStatusChanged);
  }

  @override
  void onClose() {
    _cancelSubscription();

    super.onClose();
  }

  void initTestLib() {
    _dnurseSpugPlugin.initSPUGLib(null);
    _dnurseBSPlugin.initBSLib();
  }

  void _initSubscription() {
    loginChangedSubscription =
        EventService.to.bus.on<LoginChanged>().listen(_handleLoginChanged);
  }

  void _cancelSubscription() {
    loginChangedSubscription.cancel();
  }

  void _handleLoginChanged(LoginChanged event) async {}

  //SPUG
  Future<dynamic> _onSPUGDeviceStateChanged(MethodCall call) async {
    switch (call.method) {
      case 'onSPUGDeviceStateChanged':
        dynamic spugData = call.arguments;
        Log.i('dnurseSpugPlugin-状态变更：${call.arguments}');
        int state = spugData['state'];
        lastSPUGState = SPUGStateChanged(state, spugData['data']);
        EventService.to.bus.fire(lastSPUGState);
        Log.i('通知SPUG状态更新成功');
        break;
      default:
        Log.i('dnurseSpugPlugin收到原生回调${call.method} ---- ${call.arguments}');
    }
    return 'dnurseSpugPlugin原生回调结束';
  }

  Future<dynamic> _onBloodTesterStatusChanged(MethodCall call) async {
    switch (call.method) {
      case 'bloodTesterStatusChanged':
        dynamic bsData = call.arguments;
        Log.i('处理血糖仪上报数据：$bsData');
        int state = bsData['status'];
        lastBSState = BSStateChanged(state, bsData['data']);
        EventService.to.bus.fire(lastBSState);
        Log.i('通知血糖仪状态更新成功');
        break;
      default:
        Log.i('收到原生回调${call.method} ---- ${call.arguments}');
    }
    return '原生回调结束';
  }

  /// 同步数据-下载
  Future<int> syncDataDownload(String uid, DateTime start, DateTime end) async {
    List<DataSyncTimeIntervalModel> timeIntervalList =
        await DataSyncTimeIntervalProvider.instance
            .queryUserSyncTimeInterval(uid);
    if (timeIntervalList.isEmpty) {
      DataSyncTimeIntervalModel model = DataSyncTimeIntervalModel(
          uid: uid,
          start: (start.millisecondsSinceEpoch / 1000).round(),
          end: (end.millisecondsSinceEpoch / 1000).round());
      DataSyncTimeIntervalProvider.instance.insert(model);
      timeIntervalList.add(model);
    } else {
      //如果end不为当前时间，则需要加判断
      DataSyncTimeIntervalModel lastModel = timeIntervalList.last;
      timeIntervalList.removeLast();
      await DataSyncTimeIntervalProvider.instance.deleteItem(lastModel);
      DataSyncTimeIntervalModel newLastModel =
          lastModel.copyWith(end: (end.millisecondsSinceEpoch / 1000).round());
      timeIntervalList.add(newLastModel);
      DataSyncTimeIntervalProvider.instance.insert(lastModel);
    }
    // for (DataSyncTimeIntervalModel item in timeIntervalList) {
    //   await _downloadDataFromNet(uid, item.start, item.end);
    //   syncSportData(uid, item.start, item.end);
    // }

    return 0;
  }

  /// 从服务器下载指定时间的数据
  Future<int> _downloadDataFromNet(
      String uid, int startTime, int endTime) async {
    int dataCount = 0;
    int upid = await glucoseProvider.queryMaxUpid(uid,
        start: DateTime.fromMillisecondsSinceEpoch(startTime * 1000),
        end: DateTime.fromMillisecondsSinceEpoch(endTime * 1000));
    // 血糖同步条数
    JsonResponse response =
        await DataApi.getDataCount(startTime, endTime, upid);
    Log.d('同步数据取数据数量：$response');
    if (response.successful && response.hasData) {
      dataCount = response.dataMap!['count'] ?? 0;
      int size = 300;
      for (int i = 0; i < dataCount; i += size) {
        response = await DataApi.getDataInfoV2(startTime, endTime, upid,
            offset: i, size: size);
        Log.d('服务器获取数据：$response');
        if (response.successful && response.hasData) {
          List<dynamic> dataList = response.dataMap!['list'];

          for (Map<String, dynamic> data in dataList) {
            List<GlucoseUAValueModel> list =
                GlucoseDataConvertor.fromJson(data);
            Log.d('转换数据：$list');
            if (list.isNotEmpty) {
              for (GlucoseUAValueModel item in list) {
                glucoseProvider.insertOrUpdateData(item);
              }
            }
          }
        }
      }
    }

    return dataCount;
  }



  /// 同步数据-上传
  Future<int> syncDataUpload(String uid) async {
    int upNum = 0;
    List<GlucoseUAValueModel> modifyList =
        await glucoseProvider.queryUserModifyList(AppContext.to.userSn);
    List<Map<String, dynamic>> postList = [];
    List<GlucoseUAValueModel> syncList = [];
    for (GlucoseUAValueModel item in modifyList) {
      Map<String, dynamic>? jsonData = GlucoseDataConvertor.jsonFormat(item);
      if (jsonData != null) {
        postList.add(jsonData);
        syncList.add(item);
      }
    }
    JsonResponse response = await DataApi.uploadData(postList);
    Log.d('同步血糖数据结果：$response');
    if (response.successful && response.hasData) {
      List<dynamic> list = response.dataMap!['data'];
      upNum += list.length;
      Log.d('上传的数据id：$list');
      for (Map<String, dynamic> item in list) {
        int id = item['_id'];
        await glucoseProvider.markModified(
          id,
          modified: false,
        );
      }
    }

    //因为尿酸数据和血糖数据上传不是同一个接口，所以分开处理。
    modifyList.removeWhere((element) => syncList.contains(element));
    Log.d('同步尿酸数据：$modifyList');
    if (modifyList.isNotEmpty) {
      List<Map<String, dynamic>> uaPostList = [];
      for (GlucoseUAValueModel item in modifyList) {
        Map<String, dynamic>? jsonData =
            GlucoseDataConvertor.uaJsonFormat(item);
        if (jsonData != null) {
          uaPostList.add(jsonData);
        }
      }
      JsonResponse response1 = await DataApi.uploadUaData(uaPostList);
      Log.d('同步尿酸数据结果：$response1');
      if (response1.successful && response1.hasData) {
        List<dynamic> list = response1.dataMap!['data'];
        upNum += list.length;
        for (Map<String, dynamic> item in list) {
          int id = item['_id'];
          await glucoseProvider.markModified(
            id,
            modified: false,
          );
        }
      }
    }

    //上传CGM读取的包数据
    List<CGMDataPackageModel> cgmDataPackageModifyList =
        await cgmDataPackageProvider.queryUserModifyList(AppContext.to.userSn);
    if (cgmDataPackageModifyList.isNotEmpty) {
      for (CGMDataPackageModel item in cgmDataPackageModifyList) {
        JsonResponse response1 =
            await DataApi.syncUploadCGMRawData(item.toJson());
        Log.d('上传CGM扫描包数据结果：$response1');
        if (response1.successful) {
          cgmDataPackageProvider.markModified(item.id, modified: false);
        }
      }
    }

    //上传CGM解析后的数据
    List<CGMDataModel> cgmDataModifyList =
        await cgmDataProvider.queryUserModifyList(AppContext.to.userSn);
    if (cgmDataModifyList.isNotEmpty) {
      List<Map<String, dynamic>> upload = [];
      for (CGMDataModel item in cgmDataModifyList) {
        upload.add(item.toJson());
      }
      JsonResponse response1 = await DataApi.syncUploadCGMData(upload);
      Log.d('上传CGM扫描包数据结果：$response1');
      if (response1.successful) {
        for (CGMDataModel item in cgmDataModifyList) {
          cgmDataProvider.markModified(item.id, modified: false);
        }
      }
    }



    return upNum;
  }

  /// 加载用户数据列表，如果本地没有同步则从服务器下载
  /// sn 用户sn，必传
  /// start 起始时间，包含时间戳为起始时间的数据 >=
  /// end 结束时间，不包含时间戳为结束时间的数据 <
  /// dataType 数据类型，不传为全部数据
  /// 取出所有已同步的时间段，判断要加载的数据是否在已同步时间段内，如果要加载的数据已同步到本地，则从数据库查询。如果未完全同步则从服务器同步
  Future<List<GlucoseUAValueModel>> loadDataList(
      String sn, DateTime start, DateTime end,
      {DataType? dataType}) async {
    DateTime now = DateTime.now();
    if (end.compareTo(now) > 0) {
      //加载数据时间不能大于当前时间
      end = now;
    }
    if (!AppContext.to.isTemp) {
      List<DataSyncTimeIntervalModel> timeIntervalList =
          await DataSyncTimeIntervalProvider.instance
              .queryUserSyncTimeInterval(sn);
      //第一次循环，查找包含要加载数据起止时间的时间段
      bool synchronized = false; //已同步过了
      for (DataSyncTimeIntervalModel item in timeIntervalList) {
        if (item.start <= start.millisecondsSinceEpoch / 1000 &&
            item.end >= end.millisecondsSinceEpoch / 1000) {
          //要加载的数据起始时间在某一段已同步的时间段内
          synchronized = true;
          Log.d('要取的数据在已同步的的时间段内');
          break;
        }
      }

      if (!synchronized) {
        DataSyncTimeIntervalModel? startItem, endItem;
        List<DataSyncTimeIntervalModel> list = [];

        /// 目标时间段结束时间小于已经同步的最小时间 或 目标时间段起始时间大于已经同步的最大时间，则下载目标时间段
        int startV = (start.millisecondsSinceEpoch / 1000).round();
        int endV = (end.millisecondsSinceEpoch / 1000).round();
        if (timeIntervalList.isEmpty) {
          list.add(
              DataSyncTimeIntervalModel(uid: sn, start: startV, end: endV));
        } else {
          if (endV <= timeIntervalList.first.start) {
            // await _downloadDataFromNet(sn, startV, endV);
            // DataSyncTimeIntervalProvider.instance.insert(DataSyncTimeIntervalModel(uid: sn, start: startV, end: endV));
            list.add(
                DataSyncTimeIntervalModel(uid: sn, start: startV, end: endV));
            if (endV == timeIntervalList.first.start) {
              list.add(timeIntervalList.first);
            }
            Log.d('要取的数据在已同步的的所有时间之前，直接添加');
          } else if (startV >= timeIntervalList.last.end) {
            list.add(
                DataSyncTimeIntervalModel(uid: sn, start: startV, end: endV));
            if (startV == timeIntervalList.last.end) {
              list.insert(0, timeIntervalList.last);
            }
            Log.d('要取的数据在已同步的的所有时间之后，直接添加');
          } else {
            Log.d('同步时间有交叉的，需要处理');
            if (startV < timeIntervalList.first.start) {
              startItem = DataSyncTimeIntervalModel(
                  uid: sn, start: startV, end: timeIntervalList.first.start);
            }
            for (DataSyncTimeIntervalModel item in timeIntervalList) {
              if (item.start <= startV) {
                //要加载的数据起始时间在某一段已同步的时间段内
                startItem = item;
              } else if (item.end >= endV) {
                endItem = item;
                break;
              } else {
                if (startItem != null) {
                  list.add(item);
                }
              }
            }

            if (startItem != null) {
              list.insert(0, startItem);
            }

            endItem ??= DataSyncTimeIntervalModel(
                uid: sn, start: timeIntervalList.last.end, end: endV);
            list.add(endItem);
          }
        }

        Log.d('需要同步数据的时间段：$list');

        /// 下载每一段中间的数据
        for (int i = 0; i < list.length; i++) {
          //先同步时间段，
          DataSyncTimeIntervalModel item = list[i];
          await _downloadDataFromNet(item.uid, item.start, item.end);

          //再同步时间段中间没同步过的
          if (i < list.length - 1) {
            DataSyncTimeIntervalModel item1 = list[i + 1];
            await _downloadDataFromNet(item.uid, item.end, item1.start);
          }
          //不用存储时间段，后面合并；
        }

        _mergeTimeInterval(sn, list);
      }
    }

    List<GlucoseUAValueModel> list = await glucoseProvider.queryUserDataList(
        AppContext.to.userSn,
        start: start,
        end: end,
        dataType: dataType);
    return list;
  }

  /// 合并已同步数据时间段
  void _mergeTimeInterval(
      String sn, List<DataSyncTimeIntervalModel> list) async {
    for (DataSyncTimeIntervalModel item in list) {
      await DataSyncTimeIntervalProvider.instance.deleteItem(item);
    }

    await DataSyncTimeIntervalProvider.instance.insert(
        DataSyncTimeIntervalModel(
            uid: sn, start: list.first.start, end: list.last.end));
  }

  Future<Map<DateTime, int>> getDataCountByDay(
      int uid, int startTime, int endTime) async {
    // return await _dataProvider.getDataCountByDay(uid, startTime, endTime);
    return {};
  }

  Future<int> deleteData(Data data, String userSn) async {
    GlucoseUAValueModel d = GlucoseDataConvertor.toModel(data, userSn);
    d.markModified(markDeleted: true);
    int ret = await glucoseProvider.updateDataByDid(d);
    EventService.to.bus.fire(DataChanged.deleted(data: data));
    AppContext.to.doSync(SyncParams(syncType: SyncType.syncUpload));
    return ret;
  }
}
