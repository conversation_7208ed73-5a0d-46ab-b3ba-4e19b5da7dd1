/// FileName: data_log_service
///
/// @Author: ygc
/// @Date: 2025/7/10 16:22
/// @Description:
import 'package:dnurse/app/common/service/base_service.dart';
import 'package:dnurse/app/data/convertor/food_data_convertor.dart';
import 'package:dnurse/app/data/convertor/glucose/data_convertor.dart';
import 'package:dnurse/app/data/db/model/food/food_data_model.dart';
import 'package:dnurse/app/data/db/model/glucose/glucose_ua_value_model.dart';
import 'package:dnurse/app/data/entity/Data.dart';
import 'package:get/get.dart';

import '../convertor/drug_data_convertor.dart';
import '../convertor/sport_data_convertor.dart';
import '../db/model/drug/drug_data_model.dart';
import '../db/model/log/data_log_model.dart';
import '../db/model/log/data_log_provider.dart';
import '../db/model/sport/sport_data_model.dart';
import '../entity/data_type.dart';

class DataLogService extends BaseService {
  DataLogService();

  late DataLogProvider _dataLogProvider;

  static DataLogService get to => Get.find();

  @override
  void onInit() {
    super.onInit();

    _dataLogProvider = DataLogProvider.instance;
  }

  Future<List<Data>> queryDataLog(
    String uid,
    DateTime start,
    DateTime end,
  ) async {
    List<Data> result = [];
    List<Map<String, Object?>> dataList =
        await _dataLogProvider.queryDataMap(uid, start, end);
    for (var dataMap in dataList) {
      // print(dataMap);
      bool valid = (dataMap.containsKey(DataLogViewColumns.dataType) &&
          dataMap[DataLogViewColumns.dataType] is int);
      if (!valid) {
        continue;
      }

      DataType dataType = DataType.getDataFromByValue(
          dataMap[DataLogViewColumns.dataType] as int);
      if (dataType == DataType.glucose || dataType == DataType.ua) {
        GlucoseUAValueModel glucoseUAValueModel =
            GlucoseUAValueModel.fromJson(dataMap);
        Data? data = DataConvertor.toEntity(glucoseUAValueModel);
        if (data != null) {
          result.add(data);
        }
      } else if (dataType == DataType.food) {
        FoodDataModel foodDataModel = FoodDataModel.fromJson(dataMap);
        Data data = FoodDataConvertor.toFoodEntity(foodDataModel);
        result.add(data);
      } else if (dataType == DataType.drug) {
        DrugDataModel drugDataModel = DrugDataModel.fromJson(dataMap);
        Data data = DrugDataConvertor.toEntity(drugDataModel);
        result.add(data);
      } else if (dataType == DataType.sport) {
        SportDataModel sportDataModel = SportDataModel.fromJson(dataMap);
        Data data = SportDataConvertor.toEntity(sportDataModel);
        result.add(data);
      }
    }

    // print(result);

    return result;
  }
}
