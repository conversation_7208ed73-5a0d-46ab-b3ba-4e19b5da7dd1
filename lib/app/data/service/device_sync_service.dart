/// FileName: device_sync_service
///
/// @Author: ygc
/// @Date: 2024/9/30 10:22
/// @Description:

import 'package:dnurse/app/data/db/model/device_sync_info/device_sync_info_model.dart';
import 'package:dnurse/app/data/db/model/device_sync_info/device_sync_info_provider.dart';
import 'package:dnurse/app/data/entity/device_sync_info.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/service/key_value_storage.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/framework/utils/log.dart';
import 'package:get/get.dart';

import '../../common/service/base_service.dart';
import '../convertor/device_sync_convertor.dart';

class DeviceSyncService extends BaseService {
  DeviceSyncService();

  late DeviceSyncInfoProvider _provider;

  static DeviceSyncService get to => Get.find();

  @override
  void onInit() {
    _provider = DeviceSyncInfoProvider.instance;

    super.onInit();
  }

  Future<void> saveSyncInfo(DeviceSyncInfo syncInfo) async {
    DeviceSyncInfoModel model =
        DeviceSyncConvertor.toModel(syncInfo, AppContext.to.userSn);
    await _provider.insertInfo(model);
  }

  Future<List<String>?> getDeviceSyncDataId(
    String userSn,
    String deviceSn,
    DeviceSyncDevice device,
  ) {
    return _provider.getDeviceSyncDataId(userSn, deviceSn, device.id);
  }

  String _getDeviceSyncTimeStorageKey(DeviceSyncDevice device,
      {String? userSn}) {
    return getStorageKey("device_sync_time_${device.id}_", userSn: userSn);
  }

  void saveSyncTipTime(String userSn, DeviceSyncDevice device, int time) {
    String key = _getDeviceSyncTimeStorageKey(device, userSn: userSn);
    KeyValueStorage.to.setInt(key, time);
  }

  DateTime getSyncTipTime(String userSn, DeviceSyncDevice device) {
    String key = _getDeviceSyncTimeStorageKey(device, userSn: userSn);
    int time = KeyValueStorage.to.getInt(key);
    if (time <= 0) {
      return DateTime.now().startOfDay();
    }

    return DateTime.fromMillisecondsSinceEpoch(time);
  }

  void setSyncTipTimeNextDay(String userSn, DeviceSyncDevice device) {
    DateTime c = DateTime.now().startOfDay().addDay(1);
    Log.i("setSyncTipTimeNextDay: $c");
    saveSyncTipTime(userSn, device,
        DateTime.now().startOfDay().addDay(1).millisecondsSinceEpoch);
  }

  void setSyncTipTimeToday(String userSn, DeviceSyncDevice device) {
    saveSyncTipTime(
        userSn, device, DateTime.now().startOfDay().millisecondsSinceEpoch);
  }

  bool isSyncTipEnable(String userSn, DeviceSyncDevice device) {
    DateTime now = DateTime.now();
    DateTime saveTime = getSyncTipTime(userSn, device);
    Log.i("isSyncTipEnable: $now, $saveTime");

    return now.isAfter(saveTime);
  }
}
