/// FileName: drug_service
///
/// @Author: ygc
/// @Date: 2025/7/8 20:16
/// @Description:

import 'package:dnurse/app/common/service/base_service.dart';
import 'package:dnurse/app/data/db/model/drug/day_drug_model.dart';
import 'package:dnurse/app/data/db/model/drug/drug_data_model.dart';
import 'package:dnurse/app/data/db/model/drug/drug_data_provider.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:get/get.dart';

import '../../../framework/service/event_service.dart';
import '../convertor/drug_data_convertor.dart';
import '../entity/data_drug.dart';
import '../entity/day_drug_info.dart';
import '../event/data_changed.dart';

class DrugService extends BaseService {
  DrugService();

  late DrugDataProvider _drugDataProvider;

  static DrugService get to => Get.find();

  @override
  void onInit() {
    super.onInit();

    _drugDataProvider = DrugDataProvider.instance;
  }

  Future<DataDrug> saveDrug(String uid, DataDrug dataDrug) async {
    DrugDataModel? oldData =
        await _drugDataProvider.getDataByDid(dataDrug.did, uid);

    if (oldData != null) {
      dataDrug = dataDrug.copyWith(
        id: oldData.id,
      );

      DrugDataModel dataModel = DrugDataConvertor.toModel(dataDrug);

      await _drugDataProvider.updateDataByDid(dataModel);
      DataDrug data = DrugDataConvertor.toEntity(dataModel);
      EventService.to.bus.fire(DataChanged.update(
          data: data, oldData: DrugDataConvertor.toEntity(oldData)));
    } else {
      // DataModel? oldData = await _dataProvider.queryByTypeTime(
      //     uid, DataModel.dataTypeFood, dataModel.time);
      // if (oldData != null) {
      //   dataModel = dataModel.copyWith(id: oldData.id);
      //   await _dataProvider.update(dataModel);
      //   dataFood = dataFood.copyWith(id: oldData.id);
      //   EventService.to.bus.fire(DataChanged.update(
      //       data: dataFood, oldData: DataConvertor.toFoodEntity(oldData)));
      // } else {
      // dataModel = dataModel.copyWith(isNew: 1);

      DrugDataModel dataModel = DrugDataConvertor.toModel(dataDrug);
      dataModel.markModified();
      dataModel = await _drugDataProvider.insert(dataModel);
      // await uploadData(AppContext.to.userId);

      // await saveFoodTask(uid, dataFood, dataFood.totalCalorie);

      dataDrug = dataDrug.copyWith(id: dataModel.id);
      EventService.to.bus.fire(DataChanged.add(data: dataDrug));
    }

    return dataDrug;
  }

  Future<List<DataDrug>> getDrugDataByTime(
    String sn,
    DateTime startTime,
    DateTime endTime,
  ) async {
    List<DrugDataModel> drugDataList =
        await _drugDataProvider.queryUserDataList(
      sn,
      start: startTime,
      end: endTime,
      orderAsc: true,
    );

    if (drugDataList == null || drugDataList.isEmpty) {
      return [];
    }

    return drugDataList.map((ele) => DrugDataConvertor.toEntity(ele)).toList();
  }

  Future<void> deleteDrug(String uid, DataDrug dataDrug) async {
    DrugDataModel? oldData =
        await _drugDataProvider.getDataByDid(dataDrug.did, uid);
    if (oldData != null) {
      if (oldData.upid == 0) {
        // 没上传直接删除
        _drugDataProvider.deleteById(oldData.id);
      } else {
        oldData.markModified(markDeleted: true);
        await _drugDataProvider.updateDataByDid(oldData);
      }
    }

    EventService.to.bus.fire(
      DataChanged.deleted(
        data: dataDrug,
      ),
    );
  }

  Future<DataDrug?> getLatestDrug(String sn) async {
    DrugDataModel? model = await _drugDataProvider.getLatestDrug(sn);
    if (model == null) {
      return null;
    }

    return DrugDataConvertor.toEntity(model);
  }

  Future<List<DayDrugInfo>> queryDrugCountByDay(
      String sn,
      DateTime startTime,
      DateTime endTime,
      ) async {
    List<DayDrugInfo> result = [];

    List<DayDrugModel> dataList =
    await _drugDataProvider.queryDrugCountByDay(
      sn,
      startTime.secondsSinceEpoch,
      endTime.secondsSinceEpoch,
    );

    for (var data in dataList) {
      if (data.day.isEmpty) {
        continue;
      }

      DateTime itemDatetime = DateTime.parse(data.day);
      result.add(DayDrugInfo(
        itemDatetime,
        data.count,
      ));
    }

    return result;
  }
}
