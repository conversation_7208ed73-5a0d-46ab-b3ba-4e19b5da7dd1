/// FileName: reminder_api
///
/// @Author: ygc
/// @Date: 2024/8/6 15:01
/// @Description:

import '../../../framework/network/http_client.dart';
import '../../../framework/network/response.dart';

class ReminderApi {
  static Future<JsonResponse> getMonitor(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/reminder/get-monitor',
      data: data,
      needAuth: true,
    );
  }

  static Future<JsonResponse> uploadMonitor(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/reminder/save-monitor',
      data: data,
      needAuth: true,
    );
  }
}
