/// FileName: food_search_item_dto.dart
///
/// @Author: ygc
/// @Date: 2025-07-23 16:47:36
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'food_search_item_dto.freezed.dart';
part 'food_search_item_dto.g.dart';

@freezed
class FoodSearchItemDTO with _$FoodSearchItemDTO {
  factory FoodSearchItemDTO({
    @Default(0)  int calories,
    @Default('')  String did,
    @Default('')  @Json<PERSON>ey(name: 'image_url')  String imageUrl,
    @Default('')  String name,
    @Default(0)  @JsonKey(name: 'star_num')  double starNum,
    @Default('')  String unit,
  }) = _FoodSearchItemDTO;

  FoodSearchItemDTO._();

  factory FoodSearchItemDTO.fromJson(Map<String, Object?> json) =>
      _$FoodSearchItemDTOFromJson(json);
}
