/// FileName: food_search_result_dto.dart
///
/// @Author: ygc
/// @Date: 2025-07-23 16:46:40
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

import 'food_search_item_dto.dart';

part 'food_search_result_dto.freezed.dart';
part 'food_search_result_dto.g.dart';

@freezed
class FoodSearchResultDTO with _$FoodSearchResultDTO {
  factory FoodSearchResultDTO({
    @Default([])  List<FoodSearchItemDTO> list,
    @Default(0)  int page,
    @Default(0)  @<PERSON><PERSON><PERSON><PERSON>(name: 'page_size')  int pageSize,
  }) = _FoodSearchResultDTO;

  FoodSearchResultDTO._();

  factory FoodSearchResultDTO.fromJson(Map<String, Object?> json) =>
      _$FoodSearchResultDTOFromJson(json);
}
