/// FileName: sign_in_diy_dto.dart
///
/// @Author: ygc
/// @Date: 2024-10-29 09:49:11
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:dnurse/app/data/api/dto/sign_in/sign_in_diy_background_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'sign_in_diy_dto.freezed.dart';
part 'sign_in_diy_dto.g.dart';

@freezed
class SignInDiyDTO with _$SignInDiyDTO {
  factory SignInDiyDTO({
    @Default([]) @JsonKey(name: 'background_list') List<SignInDiyBackgroundDTO> backgroundList,
    @Default([]) @JsonKey(name: 'icon_list') List<String> iconList,
  }) = _SignInDiyDTO;

  SignInDiyDTO._();

  factory SignInDiyDTO.fromJson(Map<String, Object?> json) =>
      _$SignInDiyDTOFromJson(json);
}
