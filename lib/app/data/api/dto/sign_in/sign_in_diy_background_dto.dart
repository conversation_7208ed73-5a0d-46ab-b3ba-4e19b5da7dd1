/// FileName: sign_din_diy_background_dto.dart
///
/// @Author: ygc
/// @Date: 2024-10-29 09:45:44
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'sign_in_diy_background_dto.freezed.dart';
part 'sign_in_diy_background_dto.g.dart';

@freezed
class SignInDiyBackgroundDTO with _$SignInDiyBackgroundDTO {
  factory SignInDiyBackgroundDTO({
    @Default('') @JsonKey(name: 'bg_end_color') String bgEndColor,
    @Default('') @<PERSON><PERSON><PERSON>ey(name: 'bg_start_color') String bgStartColor,
    @Default('') @Json<PERSON>ey(name: 'date_color') String dateColor,
    @Default('') @JsonKey(name: 'img_url') String imgUrl,
    @Default('') @JsonKey(name: 'text_color') String textColor,
  }) = _SignInDiyBackgroundDTO;

  SignInDiyBackgroundDTO._();

  factory SignInDiyBackgroundDTO.fromJson(Map<String, Object?> json) =>
      _$SignInDiyBackgroundDTOFromJson(json);
}
