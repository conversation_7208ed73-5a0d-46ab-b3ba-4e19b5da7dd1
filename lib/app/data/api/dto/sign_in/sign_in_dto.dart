/// FileName: sign_in_dto.dart
///
/// @Author: ygc
/// @Date: 2024-10-28 14:02:44
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'sign_in_dto.freezed.dart';
part 'sign_in_dto.g.dart';

@freezed
class SignInDTO with _$SignInDTO {
  factory SignInDTO({
    @Default('') String aid,
    @Default('') String content_detail,
    @Default('') String content_title,
    @Default('') String date,
    @Default('') String day,
    @Default('') String desc,
    @Default('') String image,
  }) = _SignInDTO;

  SignInDTO._();

  factory SignInDTO.fromJson(Map<String, Object?> json) =>
      _$SignInDTOFromJson(json);
}
