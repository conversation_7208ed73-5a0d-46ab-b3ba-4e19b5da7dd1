/// FileName: monitor_plan_data_dto.dart
///
/// @Author: ygc
/// @Date: 2024-08-06 15:28:47
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'monitor_plan_data_dto.freezed.dart';
part 'monitor_plan_data_dto.g.dart';

@freezed
class MonitorPlanDataDTO with _$MonitorPlanDataDTO {
  factory MonitorPlanDataDTO({
    @Default('') @Json<PERSON>ey(name: '_up') String up,
    @Default('') String dps,
    @Default('') String mpc,
    @Default('') String mpi,
    @Default('') String mps,
  }) = _MonitorPlanDataDTO;

  MonitorPlanDataDTO._();

  factory MonitorPlanDataDTO.fromJson(Map<String, Object?> json) =>
      _$MonitorPlanDataDTOFromJson(json);
}
