/// FileName: monitor_plan_item_dto.dart
///
/// @Author: ygc
/// @Date: 2024-08-06 15:42:18
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'monitor_plan_item_dto.freezed.dart';
part 'monitor_plan_item_dto.g.dart';

@freezed
class MonitorPlanItemDTO with _$MonitorPlanItemDTO {
  factory MonitorPlanItemDTO({
    @Default(0) int enable,
    @Default(0) int hour,
    @Default(0) int minute,
    @Default(0) int repeated,
  }) = _MonitorPlanItemDTO;

  MonitorPlanItemDTO._();

  factory MonitorPlanItemDTO.fromJson(Map<String, Object?> json) =>
      _$MonitorPlanItemDTOFromJson(json);
}
