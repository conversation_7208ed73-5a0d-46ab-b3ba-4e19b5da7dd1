/// FileName: monitor_plan_dto.dart
///
/// @Author: ygc
/// @Date: 2024-08-06 15:27:54
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

import 'monitor_plan_data_dto.dart';

part 'monitor_plan_dto.freezed.dart';
part 'monitor_plan_dto.g.dart';

@freezed
class MonitorPlanDTO with _$MonitorPlanDTO {
  factory MonitorPlanDTO({
    MonitorPlanDataDTO? d,
    @Default(0) int s,
  }) = _MonitorPlanDTO;

  MonitorPlanDTO._();

  factory MonitorPlanDTO.fromJson(Map<String, Object?> json) =>
      _$MonitorPlanDTOFromJson(json);
}
