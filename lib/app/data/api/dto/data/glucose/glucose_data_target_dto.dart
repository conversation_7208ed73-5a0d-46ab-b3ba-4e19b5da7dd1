/// FileName: glucose_data_target_dto.dart
///
/// @Author: ygc
/// @Date: 2024-08-09 10:28:25
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'glucose_data_target_dto.freezed.dart';
part 'glucose_data_target_dto.g.dart';

@freezed
class GlucoseDataTargetDTO with _$GlucoseDataTargetDTO {
  factory GlucoseDataTargetDTO({
    @Default(0) @JsonKey(name: 'dv') double diastatic,
    @Default(0) @<PERSON><PERSON><PERSON><PERSON>(name: 'ham') double mealAfterHigh,
    @Default(0) @J<PERSON><PERSON><PERSON>(name: 'lam') double mealAfterLow,
    @Default(0) @Json<PERSON>ey(name: 'hbd') double dawnHigh,
    @Default(0) @Json<PERSON>ey(name: 'lbd') double dawnLow,
    @Default(0) @JsonKey(name: 'hbm') double mealBeforeHigh,
    @Default(0) @JsonKey(name: 'lbm') double mealBeforeLow,
    @Default(0) @JsonKey(name: 'hes') double emptyHigh,
    @Default(0) @JsonKey(name: 'les') double emptyLow,
    @Default(0) @JsonKey(name: 'hn') double sleepHigh,
    @Default(0) @JsonKey(name: 'ln') double sleepLow,
    @Default(0) @JsonKey(name: 'mt') int modifyTime,
    @Default(0) @JsonKey(name: 'um') int userModified,
  }) = _GlucoseDataTargetDTO;

  GlucoseDataTargetDTO._();

  factory GlucoseDataTargetDTO.fromJson(Map<String, Object?> json) =>
      _$GlucoseDataTargetDTOFromJson(json);
}
