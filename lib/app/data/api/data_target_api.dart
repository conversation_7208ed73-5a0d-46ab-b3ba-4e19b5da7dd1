/// FileName: data_target_api
///
/// @Author: ygc
/// @Date: 2024/8/9 10:17
/// @Description:
import '../../../framework/network/http_client.dart';
import '../../../framework/network/response.dart';

class DataTargetApi {
  static Future<JsonResponse> getTarget() {
    return HttpClient().postSignData(
      '/api/data/get_target',
      data: {},
      needAuth: true,
    );
  }

  static Future<JsonResponse> saveTarget(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/data/update_target',
      data: data,
      needAuth: true,
    );
  }
}
