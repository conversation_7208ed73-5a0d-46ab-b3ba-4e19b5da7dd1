/// FileName: sugar_plan_api
///
/// @Author: Claude
/// @Date: 2025/01/28
/// @Description: 控糖方案相关API接口
import '../../../framework/network/http_client.dart';
import '../../../framework/network/response.dart';

class SugarPlanApi {
  /// 获取用户控糖方案记录
  static Future<JsonResponse> getUserSugarPlanRecord() {
    return HttpClient().postSignData(
      '/api/HomeDataSugar/get_user_sugar_plan_record',
      data: {},
      needAuth: true,
    );
  }

  /// 分享控糖方案成就获取经验值
  static Future<JsonResponse> shareSugarPlanExp() {
    return HttpClient().postSignData(
      '/api/HomeDataSugar/share_sugar_plan_exp',
      data: {},
      needAuth: true,
    );
  }

  /// 生成用户控糖方案
  static Future<JsonResponse> createUserSugarPlan(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/HomeDataSugar/create_user_sugar_plan',
      data: data,
      needAuth: true,
    );
  }

  // 新版本-获取控糖方案的弹窗状态
  static Future<JsonResponse> getUserSugarPlanPop(data) {
    return HttpClient().postSignData(
      '/api/HomeDataSugar/get_user_sugar_plan_pop',
      data: data,
      needAuth: true,
    );
  }

  // 新版本-控糖方案的弹窗标记
  static Future<JsonResponse> saveUserSugarPlanPop(data) {
    return HttpClient().postSignData(
      '/api/HomeDataSugar/save_user_sugar_plan_pop',
      data: data,
      needAuth: true,
    );
  }
}