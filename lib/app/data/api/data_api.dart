import '../../../framework/network/response.dart';
import '../../../framework/network/http_client.dart';

class DataApi {
  // 获取单点建议接口
  static Future<JsonResponse> getRecommend(Map<String, dynamic> data) {
    return HttpClient()
        .postSignData('/api/data/get_recommend', data: data, needAuth: true);
  }

  // 获取尿酸单点建议接口
  static Future<JsonResponse> getUARecommend(Map<String, dynamic> data) {
    return HttpClient().postSignData('/api/Uric_acid_data/get_recommend',
        data: data, needAuth: true);
  }

  // 上传血糖数据
  static Future<JsonResponse> uploadData(List<Map<String, dynamic>> data) {
    return HttpClient().postSignData(
      '/api/data/update_data',
      data: {'data': data},
      needAuth: true,
    );
  }

  // 上传尿酸数据
  static Future<JsonResponse> uploadUaData(List<Map<String, dynamic>> data) {
    return HttpClient().postSignData(
      '/api/uric_acid_data/update_data',
      data: {'data': data},
      needAuth: true,
    );
  }

  // 取指定时间段内数据条数
  // 尿酸数据接口：uric_acid_data/get_count
  static Future<JsonResponse> getDataCount(int start, int end, int upid) {
    Map<String, dynamic> data = {
      'start_time': start,
      'end_time': end,
      'version': upid,
    };
    return HttpClient().postSignData(
      '/api/data/get_count',
      data: data,
      needAuth: true,
    );
  }

  // 取指定时间段内数据列表
  // 尿酸数据接口：uric_acid_data/data_info_v2
  static Future<JsonResponse> getDataInfoV2(int start, int end, int upid,
      {int offset = 0, int size = 300}) {
    Map<String, dynamic> data = {
      'start_time': start,
      'end_time': end,
      'version': upid,
      'offset': offset,
      'size': size,
      'sn': 3.0,
    };
    return HttpClient()
        .postSignData('/api/data/data_info_v2', data: data, needAuth: true);
  }

  // 上传动态血糖仪包数据
  static Future<JsonResponse> syncUploadCGMRawData(Map<String, dynamic> data) {
    return HttpClient().postSignData(
      '/api/dynamic_data/insert_basic_data',
      data: data,
      needAuth: true,
    );
  }

  static Future<JsonResponse> syncUploadCGMData(
      List<Map<String, dynamic>> data) {
    return HttpClient().postSignData(
      '/api/dynamic_data/insert_blood_data',
      data: {'blood': data},
      needAuth: true,
    );
  }

  static Future<JsonResponse> syncDownloadCGMData(
      int upid, int page, int size) {
    return HttpClient().postSignData(
      '/api/dynamic_data/get_blood_data_page',
      data: {'version': upid, 'page': page, 'size': size},
      needAuth: true,
    );
  }

  // 上传运动数据
  static Future<JsonResponse> uploadSportData(List<Map<String, dynamic>> data) {
    return HttpClient().postSignData(
      '/api/HomeData/sport_insert_save_data',
      data: {'data': data},
      needAuth: true,
    );
  }

  // 获取运动数据
  static Future<JsonResponse> syncGetSportData(int start, int end, int upid,
      {int offset = 0, int size = 300}) {
    Map<String, dynamic> data = {
      'start_time': start,
      'end_time': end,
      'version': upid,
      'page': offset,
      'size': size,
      'sn': 3.0,
    };
    return HttpClient().postSignData(
      '/api/HomeData/sport_list_page',
      data: data,
      needAuth: true,
    );
  }

  static Future<JsonResponse> searchFood({
    required String name,
    int page = 1,
    int size = 10,
  }) {
    Map<String, dynamic> data = {
      'key': name,
      'page': page,
      'page_size': size,
    };
    return HttpClient().postSignData(
      '/api/data/search_food',
      data: data,
      needAuth: true,
    );
  }
}
