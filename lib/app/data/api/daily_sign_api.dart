import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../../../framework/network/http_client.dart';
import '../../../framework/network/response.dart';

class DailySignApi {
  static Future<JsonResponse> getMonthList({
    String month_time = "",
    String is_group = "1",
  }) {
    return HttpClient().postSignData(
      '/api/daily_sign/get_month_list',
      data: {
        "month_time": month_time,
        "is_group": is_group,
      },
      needAuth: true,
    );
  }

  static Future<JsonResponse> getDiyConfig() {
    return HttpClient().postSignData(
      '/api/daily_sign/get_diy_config',
      needAuth: true,
    );
  }

  static Future<JsonResponse> saveDiyInfo({
    String content = "",
  }) {
    int timestamp = DateTime.now().secondsSinceEpoch;
    return HttpClient().postSignData(
      '/api/daily_sign/diy_advice_info',
      data: {
        "content": content,
        "date": timestamp.toString(),
      },
      needAuth: true,
    );
  }
}
