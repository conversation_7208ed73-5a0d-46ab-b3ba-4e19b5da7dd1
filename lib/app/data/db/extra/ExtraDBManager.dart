/// FileName: ExtraDBManager
///
/// @Author: ygc
/// @Date: 2024/9/19 10:01
/// @Description:

import 'dart:io';

import 'package:dnurse/framework/utils/platform_utils.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/services.dart' ;



const extraDataDB = 'data.db';

class ExtraDBManager {
  ExtraDBManager._internal();

  static final ExtraDBManager _instance = ExtraDBManager._internal();

  static ExtraDBManager get instance {
    return _instance;
  }

  final Map<String, Database> _dbMap = {};

  Future<Database> getDB(String dbName) async {
    Database? db = _dbMap[dbName];
    if (db != null) {
      return db;
    }

    String dbStoragePath = await _getDBStoragePath(dbName);

    File dbFile = File(dbStoragePath);
    if(!dbFile.existsSync()) {
      ByteData data = await rootBundle.load('assets/db/$dbName');
      List<int> bytes = data.buffer.asUint8List();

      await dbFile.writeAsBytes(bytes, flush: true);
    }

    String dbPath = await _getDBPath(dbName);
    db = await openDatabase(
      dbPath,
    );

    _dbMap[dbName] = db;

    return db;
  }

  Future<String> _getDBPath(String dbName) async {
    final dbPath = await getDatabasesPath();
    return join(dbPath, dbName);
  }

  Future<String> _getDBStoragePath(String dbName) async {
    final dbPath = await getDatabasesPath();
    String part = "";
    if(DNUPlatform.isOhos) {
      part = "rdb";
    }

    return join(dbPath, part, dbName);
  }
}