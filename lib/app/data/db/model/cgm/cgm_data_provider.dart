import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/framework/utils/log.dart';

import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import 'cgm_data_model.dart';

class CGMDataProvider extends Provider {
  CGMDataProvider._internal() : super(cgmDataTable);
  static final CGMDataProvider _instance = CGMDataProvider._internal();

  static CGMDataProvider get instance {
    return _instance;
  }

  /// 插入一条数据
  Future<CGMDataModel> insert(
      CGMDataModel cgmDataModel) async {
    if (cgmDataModel.did.isEmpty) {
      cgmDataModel.did = DataUtils.generateDidWithPrefix('CGM');
    }
    int id = await insertMap(cgmDataModel.toDB());
    cgmDataModel.id = id;

    return cgmDataModel;
  }

  /// 根据did更新数据
  Future<int> updateDataByDid(CGMDataModel cgmDataModel) async {
    String where = "${cgmDataModel.did} = ?";
    List<Object?> whereArgs = [
      cgmDataModel.did,
    ];

    return await updateMap(cgmDataModel.toDB(),
        where: where, whereArgs: whereArgs);
  }

  /// 插入或更新数据
  Future<int> insertOrUpdateData(CGMDataModel cgmDataModel) async {
    CGMDataModel? row = await getDataByDid(cgmDataModel.did);

    if (row == null) {
      CGMDataModel ret = await insert(cgmDataModel);
      return ret.id;
    }

    return await updateDataByDid(cgmDataModel);
  }

  /// 标记数据修改状态和upid
  Future<int> markModified(int id, {bool modified = false, int? upid}) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      id,
    ];

    Map<String, dynamic> data = {DNUColumns.modified: modified ? 1 : 0};
    if (upid != null) {
      data[CGMDataTableColumns.upid] = upid;
    }
    return await updateMap(data, where: where, whereArgs: whereArgs);
  }

  /// 根据did查询数据
  Future<CGMDataModel?> getDataByDid(String did) async {
    String where = "${CGMDataTableColumns.did} = ?";
    List<Object?> whereArgs = [
      did,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );
    if (result.isNotEmpty) {
      return CGMDataModel.fromJson(result.first);
    }

    return null;
  }

  /// 查询数据列表
  /// sn 用户sn，必传
  /// start 起始时间，包含时间戳为起始时间的数据 >=
  /// end 结束时间，不包含时间戳为结束时间的数据 <
  /// dataType 数据类型，不传为全部数据
  Future<List<CGMDataModel>> queryUserDataList(String sn,
      {DateTime? start,
      DateTime? end,
      bool orderAsc = false}) async {
    String where =
        "${CGMDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${CGMDataTableColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${CGMDataTableColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${CGMDataTableColumns.time}  ${orderAsc ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return result.map((e) => CGMDataModel.fromJson(e)).toList();
    }

    return [];
  }

  /// 查询修改的数据
  Future<List<CGMDataModel>> queryUserModifyList(String sn) async {
    String where =
        "${CGMDataTableColumns.uid} = ? AND ${DNUColumns.modified} = 1";
    List<Object?> whereArgs = [
      sn,
    ];

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${CGMDataTableColumns.time} ASC');
    if (result.isNotEmpty) {
      return result.map((e) => CGMDataModel.fromJson(e)).toList();
    }

    return [];
  }

  Future<int> queryMaxUpid(String sn, {DateTime? start, DateTime? end}) async {
    String where = "${CGMDataTableColumns.uid} = ?";
    List<Object?> whereArgs = [
      sn,
    ];

    if (start != null) {
      where = '$where AND ${CGMDataTableColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${CGMDataTableColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
      columns: ['Max(${CGMDataTableColumns.upid}) AS maxUpid'],
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isNotEmpty) {
      try {
        return result.first['maxUpid'] as int;
      } catch (e) {
        return 0;
      }
    }

    return 0;
  }

  /// 查询一天数据的条数
  /// 参数sn 用户sn
  /// 参数dateTime 某一天的起始时间，查询指定时间点的24小时
  Future<int> queryNumberOfDataInDay(String sn, DateTime dateTime) async {
    int n = 0;
    List<String> sel = ['COUNT(${CGMDataTableColumns.did}) AS num'];
    String where =
        '${CGMDataTableColumns.uid} = ? AND ${DNUColumns.deleted} = 0 AND ${CGMDataTableColumns.time} >= ? AND ${CGMDataTableColumns.time} < ?';
    int start = (dateTime.millisecondsSinceEpoch / 1000).round();
    List<dynamic> whereArgs = [sn, start, start + 86400];
    List<Map<String, Object?>> result =
        await queryMap(columns: sel, where: where, whereArgs: whereArgs);
    Log.d('$dateTime的数据条数：$result');
    if (result.isNotEmpty) {
      Map<String, Object?> item = result.first;
      n = item['num'] as int ;
    }

    return n;
  }

  Future<CGMDataModel?> queryMaxMin(String sn,
      {DateTime? start,
      DateTime? end,
      bool isMax = true}) async {
    String where =
        "${CGMDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${CGMDataTableColumns.time} >= ?';
      whereArgs.add(start.secondsSinceEpoch);
    }

    if (end != null) {
      where = '$where AND ${CGMDataTableColumns.time} < ?';
      whereArgs.add(end.secondsSinceEpoch);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        limit: 1,
        orderBy: '${CGMDataTableColumns.value}  ${!isMax ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return CGMDataModel.fromJson(result.first);
    }

    return null;
  }

  Future<CGMDataModel?> getUserGlucoseDataByDevTypeAndTime(
      String uid, int devType, int time) async {
    String where =
        "${DNUColumns.deleted} = 0  AND ${CGMDataTableColumns.devType} = ? "
        "AND ${CGMDataTableColumns.time} = ? ";

    List<Object?> whereArgs = [
      uid,
      devType,
      time,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );

    if (result.isEmpty) {
      return null;
    }

    return CGMDataModel.fromJson(result.first);
  }
}
