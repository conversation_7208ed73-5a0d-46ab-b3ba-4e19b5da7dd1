/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'cgm_data_model.freezed.dart';
part 'cgm_data_model.g.dart';

const cgmDataTable = 'cgm_data';

class CGMDataTableColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String value = "value";
  static const String time = "time";
  static const String pid = "pid";
  static const String rValue = "r_value";
  static const String temp = "temp";
  static const String flags = "flags";
  static const String point = "point";
  static const String upid = "upid";
  static const String devSn = "dev_sn";
  static const String devType = "dev_type";
  static const String startTime = "start_time";

}

@unfreezed
class CGMDataModel with _$CGMDataModel {
  CGMDataModel._();

  factory CGMDataModel({
    @Default(0) int id,
    required String uid,
    required String did,
    required int value,
    required int time,
    @Default('') String rawdid,  //包数据id
    @Default(0) int upid,
    @Default(0) int temperature,  //传感器温度
    @Default(0) int flags,
    @Default(0) double rectifyValue, //校正后值
    @Default(0) int timePoint,

    @Default('') String deviceSn,
    @Default(0) int cgmDeviceType,
    @Default(0) int startTime,

    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
  }) = _CGMDataModel;

  factory CGMDataModel.fromJson(Map<String, Object?> json) =>
      _$CGMDataModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = (DateTime.now().millisecondsSinceEpoch / 1000).round();
  }
}
