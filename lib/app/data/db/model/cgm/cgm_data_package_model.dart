/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'cgm_data_package_model.freezed.dart';
part 'cgm_data_package_model.g.dart';

const cgmDataPackageTable = 'cgm_data_package';

class CGMDataPackageTableColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String dataStr = "data_str";
  static const String devSn = "dev_sn";
  static const String devType = "dev_type";
  static const String captureTime = "captureTime";
  static const String upid = "upid";

  static const String startTime = "start_time";
  static const String identifierStr = "identifierStr";
  static const String icManufacturerCode = "icManufacturerCode";
  static const String icSerialNumber = "icSerialNumber";

  static const String isAnalytic = "isAnalytic";
}

@unfreezed
class CGMDataPackageModel with _$CGMDataPackageModel {
  CGMDataPackageModel._();

  factory CGMDataPackageModel({
    @Default(0) int id,
    required String uid,
    required String did,
    required String packageDataStr,   //包数据base64编码字符串
    required String deviceSn,
    required int cgmDeviceType,
    required int captureTime,
    @Default(0) int upid,

    @Default(0) int startTime,
    required String identifierStr,    //id数据base64编码字符串
    @Default(0) int icManufacturerCode,
    required String icSerialNumber,    //icSerialNumber数据base64编码字符串

    @Default(0) int isAnalytic,

    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
  }) = _CGMDataPackageModel;

  factory CGMDataPackageModel.fromJson(Map<String, Object?> json) =>
      _$CGMDataPackageModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = (DateTime.now().millisecondsSinceEpoch / 1000).round();
  }
}
