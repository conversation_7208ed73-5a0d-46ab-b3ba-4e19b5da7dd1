
import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import 'data_sync_time_interval_model.dart';

class DataSyncTimeIntervalProvider extends Provider {
  DataSyncTimeIntervalProvider._internal() : super(dataSyncTimeIntervalTable);
  static final DataSyncTimeIntervalProvider _instance = DataSyncTimeIntervalProvider._internal();

  static DataSyncTimeIntervalProvider get instance {
    return _instance;
  }

  Future<List<DataSyncTimeIntervalModel>> queryUserSyncTimeInterval(String uid) async {
    String where = "${DataSyncTimeIntervalColumns.uid} = ?";
    List<Object?> whereArgs = [
      uid,
    ];

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${DataSyncTimeIntervalColumns.start} ASC');
    if (result.isNotEmpty) {
      return result.map((e) => DataSyncTimeIntervalModel.fromJson(e)).toList();
    }

    return [];
  }

  Future<int> insert(DataSyncTimeIntervalModel intervalModel) async {
    int id = await insertMap(intervalModel.toDB());

    return id;
  }

  Future<int> deleteItem(DataSyncTimeIntervalModel intervalModel) async {
    if (intervalModel.id > 0) {
      return await delete(where: '${DNUColumns.id} = ?', whereArgs: [intervalModel.id]);
    }

    return await delete(where: '${DataSyncTimeIntervalColumns.start} = ? AND ${DataSyncTimeIntervalColumns.end} = ?', whereArgs: [intervalModel.start, intervalModel.end]);
  }
}
