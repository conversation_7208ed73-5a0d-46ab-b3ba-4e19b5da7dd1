/// FileName: data_sync_time_interval_model.dart
///
/// @Author: weili
/// @Date: 2024-08-27 15:10:10
/// @Description:
///
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'data_sync_time_interval_model.freezed.dart';
part 'data_sync_time_interval_model.g.dart';

const dataSyncTimeIntervalTable = 'dataSyncTimeIntervalTable';

class DataSyncTimeIntervalColumns extends DNUColumns {
  static const String end = 'end';
  static const String start = 'start';
  static const String uid = 'uid';
}

@freezed
class DataSyncTimeIntervalModel with _$DataSyncTimeIntervalModel {
  factory DataSyncTimeIntervalModel({
    @Default(0) int id,
    @Default('') String uid,
    @Default(0) int start,
    @Default(0) int end,
  }) = _DataSyncTimeIntervalModel;

  DataSyncTimeIntervalModel._();

  factory DataSyncTimeIntervalModel.fromJson(Map<String, Object?> json) =>
      _$DataSyncTimeIntervalModelFromJson(json);

  Map<String, dynamic> toDB() {
    final Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);

    return mapping;
  }
}
