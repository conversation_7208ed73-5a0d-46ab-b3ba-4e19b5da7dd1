import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/framework/utils/log.dart';

import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import '../../../entity/data_type.dart';
import 'glucose_ua_value_model.dart';

class GlucoseDataPerDay {
  GlucoseDataPerDay(
    this.date,
    this.glucoseList,
  );

  final String date;
  final List<GlucoseUAValueModel> glucoseList;

  @override
  String toString() {
    return 'GlucoseDataPerDay{date: $date, glucoseList: $glucoseList}';
  }
}

class GlucoseProvider extends Provider {
  GlucoseProvider._internal() : super(glucoseValueTable);
  static final GlucoseProvider _instance = GlucoseProvider._internal();

  static GlucoseProvider get instance {
    return _instance;
  }

  /// 插入一条数据
  Future<GlucoseUAValueModel> insert(
      GlucoseUAValueModel glucoseValueModel) async {
    if (glucoseValueModel.did.isEmpty) {
      glucoseValueModel.did = DataUtils.generateDidWithPrefix('D');
    }
    int id = await insertMap(glucoseValueModel.toDB());
    glucoseValueModel.id = id;

    return glucoseValueModel;
  }

  /// 根据did更新数据
  Future<int> updateDataByDid(GlucoseUAValueModel glucoseValueModel) async {
    String where = "${GlucoseUAValueColumns.did} = ?";
    List<Object?> whereArgs = [
      glucoseValueModel.did,
    ];

    return await updateMap(glucoseValueModel.toDB(),
        where: where, whereArgs: whereArgs);
  }

  /// 插入或更新数据
  Future<int> insertOrUpdateData(GlucoseUAValueModel glucoseValueModel) async {
    GlucoseUAValueModel? row = await getDataByDid(glucoseValueModel.did);

    if (row == null) {
      GlucoseUAValueModel ret = await insert(glucoseValueModel);
      return ret.id;
    }

    return await updateDataByDid(glucoseValueModel);
  }

  /// 标记数据修改状态和upid
  Future<int> markModified(int id, {bool modified = false, int? upid}) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      id,
    ];

    Map<String, dynamic> data = {DNUColumns.modified: modified ? 1 : 0};
    if (upid != null) {
      data[GlucoseUAValueColumns.upid] = upid;
    }
    return await updateMap(data, where: where, whereArgs: whereArgs);
  }

  /// 根据did查询数据
  Future<GlucoseUAValueModel?> getDataByDid(String did) async {
    String where = "${GlucoseUAValueColumns.did} = ?";
    List<Object?> whereArgs = [
      did,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );
    if (result.isNotEmpty) {
      return GlucoseUAValueModel.fromJson(result.first);
    }

    return null;
  }

  /// 按类型查最新一条数据
  /// type 数据类型
  /// sn 用户sn
  Future<GlucoseUAValueModel?> getLastDataWithType(int type,
      {String? sn}) async {
    String uid = sn ?? AppContext.to.userSn;
    String where =
        "${GlucoseUAValueColumns.dataType} = ? AND ${GlucoseUAValueColumns.uid} = ? AND ${DNUColumns.deleted} = 0";
    List<Object?> whereArgs = [
      type,
      uid,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      orderBy: "${GlucoseUAValueColumns.time} desc",
      limit: 1,
    );
    if (result.isNotEmpty) {
      return GlucoseUAValueModel.fromJson(result.first);
    }

    return null;
  }

  /// 查询数据列表
  /// sn 用户sn，必传
  /// start 起始时间，包含时间戳为起始时间的数据 >=
  /// end 结束时间，不包含时间戳为结束时间的数据 <
  /// dataType 数据类型，不传为全部数据
  Future<List<GlucoseUAValueModel>> queryUserDataList(String sn,
      {DateTime? start,
      DateTime? end,
      DataType? dataType,
      bool orderAsc = false}) async {
    String where =
        "${GlucoseUAValueColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    if (dataType != null) {
      where = '$where AND ${GlucoseUAValueColumns.dataType} = ?';
      whereArgs.add(dataType.value);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${GlucoseUAValueColumns.time}  ${orderAsc ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return result.map((e) => GlucoseUAValueModel.fromJson(e)).toList();
    }

    return [];
  }

  /// 查询修改的数据
  Future<List<GlucoseUAValueModel>> queryUserModifyList(String sn,
      {DataType? dataType}) async {
    String where =
        "${GlucoseUAValueColumns.uid} = ? AND ${DNUColumns.modified} = 1";
    List<Object?> whereArgs = [
      sn,
    ];

    if (dataType != null) {
      where = '$where AND ${GlucoseUAValueColumns.dataType} = ?';
      whereArgs.add(dataType.value);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${GlucoseUAValueColumns.time} ASC');
    if (result.isNotEmpty) {
      return result.map((e) => GlucoseUAValueModel.fromJson(e)).toList();
    }

    return [];
  }

  Future<int> queryMaxUpid(String sn, {DateTime? start, DateTime? end}) async {
    String where = "${GlucoseUAValueColumns.uid} = ?";
    List<Object?> whereArgs = [
      sn,
    ];

    if (start != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
      columns: ['Max(${GlucoseUAValueColumns.upid}) AS maxUpid'],
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isNotEmpty) {
      try {
        return result.first['maxUpid'] as int;
      } catch (e) {
        return 0;
      }
    }

    return 0;
  }

  /// 查询一天数据的条数
  /// 参数sn 用户sn
  /// 参数dateTime 某一天的起始时间，查询指定时间点的24小时
  Future<int> queryNumberOfDataInDay(String sn, DateTime dateTime) async {
    int n = 0;
    List<String> sel = ['COUNT(${GlucoseUAValueColumns.did}) AS num'];
    String where =
        '${GlucoseUAValueColumns.uid} = ? AND ${DNUColumns.deleted} = 0 AND ${GlucoseUAValueColumns.time} >= ? AND ${GlucoseUAValueColumns.time} < ?';
    int start = (dateTime.millisecondsSinceEpoch / 1000).round();
    List<dynamic> whereArgs = [sn, start, start + 86400];
    List<Map<String, Object?>> result =
        await queryMap(columns: sel, where: where, whereArgs: whereArgs);
    Log.d('$dateTime的数据条数：$result');
    if (result.isNotEmpty) {
      Map<String, Object?> item = result.first;
      n = item['num'] as int;
    }

    return n;
  }

  Future<GlucoseUAValueModel?> queryMaxMin(String sn,
      {DateTime? start,
      DateTime? end,
      DataType? dataType,
      bool isMax = true}) async {
    DataType type = dataType ?? DataType.glucose;
    String where =
        "${GlucoseUAValueColumns.uid} = ? and ${DNUColumns.deleted} = ? and ${GlucoseUAValueColumns.dataType} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
      type.value,
    ];

    if (start != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} >= ?';
      whereArgs.add(start.secondsSinceEpoch);
    }

    if (end != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} < ?';
      whereArgs.add(end.secondsSinceEpoch);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        limit: 1,
        orderBy: '${GlucoseUAValueColumns.value}  ${!isMax ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return GlucoseUAValueModel.fromJson(result.first);
    }

    return null;
  }

  Future<GlucoseUAValueModel?> getUserGlucoseDataBySourceAndTime(
      String uid, int source, int time) async {
    String where =
        "${GlucoseUAValueColumns.dataType} = ? AND ${GlucoseUAValueColumns.uid} = ? "
        "AND ${DNUColumns.deleted} = 0  AND ${GlucoseUAValueColumns.source} = ? "
        "AND ${GlucoseUAValueColumns.time} = ? ";

    List<Object?> whereArgs = [
      DataType.glucose.value,
      uid,
      source,
      time,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );

    if (result.isEmpty) {
      return null;
    }

    return GlucoseUAValueModel.fromJson(result.first);
  }

  Future<List<GlucoseUAValueModel>?> getContourData(
      String uid, String contourSn) async {
    String where =
        "${GlucoseUAValueColumns.uid} = ? AND ${DNUColumns.deleted} = 0 "
        " AND ${GlucoseUAValueColumns.dataType} = ? "
        "AND ${GlucoseUAValueColumns.remark2} like ? ";

    List<Object?> whereArgs = [
      uid,
      DataType.glucose.value,
      "%-$contourSn",
    ];

    Log.d('getContourData: $where, $whereArgs');

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: "${GlucoseUAValueColumns.time} DESC",
        limit: 100);
    if (result.isEmpty) {
      return null;
    }

    return result.map((e) => GlucoseUAValueModel.fromJson(e)).toList();
  }

  Future<List<GlucoseDataPerDay>?> getDeviceSyncDataByDid(
      String uid, List<String> didList) async {
    if (didList.isEmpty) {
      return null;
    }
    String didArgs = didList.map((e) => "'$e'").join(",");
    String where =
        "${GlucoseUAValueColumns.uid} = ? AND ${DNUColumns.deleted} = 0 "
        " AND ${GlucoseUAValueColumns.dataType} = ? "
        "AND ${GlucoseUAValueColumns.did} in ($didArgs) ";

    String dayCol =
        "datetime(${GlucoseUAValueColumns.time},'unixepoch', 'localtime','start of day')  AS day";

    List<Object?> whereArgs = [
      uid,
      DataType.glucose.value,
    ];

    Log.d('getContourData: $where, $whereArgs');

    List<Map<String, Object?>> result = await queryMap(
      columns: [dayCol, "*"],
      where: where,
      whereArgs: whereArgs,
      orderBy: "day DESC, ${GlucoseUAValueColumns.time} DESC",
    );
    if (result.isEmpty) {
      return null;
    }

    String? currentDate;

    GlucoseDataPerDay? currentItem;
    List<GlucoseDataPerDay> resultList = [];

    for (var item in result) {
      final dataItem = GlucoseUAValueModel.fromJson(item);

      if (currentDate == null) {
        currentDate = item['day'] as String;
        resultList = [];

        currentItem = GlucoseDataPerDay(currentDate, []);
      } else {
        if (currentDate != item['day'] as String) {
          resultList.add(currentItem!);

          currentDate = item['day'] as String;
          currentItem = GlucoseDataPerDay(currentDate, []);
        }
      }

      currentItem?.glucoseList.add(dataItem);
    }
    if (currentItem != null) {
      resultList.add(currentItem);
    }

    return resultList;
  }

  static void genGetLatestSql(StringBuffer sqlBuffer, List<Object> argsArray,
      String uid, int startTime, int endTime) {
    if (uid.isEmpty) {
      return;
    }

    sqlBuffer.write("${DNUColumns.id} IN ( "
        "SELECT ${DNUColumns.id} FROM "
        "( SELECT * FROM $glucoseDataView WHERE ${GlucoseUAValueColumns.uid} = ? AND ${GlucoseUAValueColumns.timePoint} != 9 AND ( ${GlucoseUAValueColumns.time} BETWEEN ? AND ? )  ORDER BY ${DNUColumns.id} DESC ) as t1 GROUP BY datetime( ${GlucoseUAValueColumns.time}/1000, 'unixepoch', 'localtime','start of day'), ${GlucoseUAValueColumns.time} HAVING ${GlucoseUAValueColumns.time}=MAX( ${GlucoseUAValueColumns.time})"
        ")");

    argsArray.add(uid);
    argsArray.add(startTime);
    argsArray.add(endTime);
  }

  Future<int> getGlucoseLatestAverageValue(
      String userSn, int startTime, int endTime) async {
    if (userSn.isEmpty) {
      return 0;
    }

    StringBuffer where = StringBuffer();
    List<Object> whereArgs = [];

    GlucoseProvider.genGetLatestSql(
        where, whereArgs, userSn, startTime, endTime);

    List<String> columns = ["AVG( ${GlucoseUAValueColumns.value} ) as a"];

    List<Map<String, Object?>> result = await queryMap(
      columns: columns,
      where: where.toString(),
      whereArgs: whereArgs,
    );
    return getColumnDouble(result);
  }

  int getColumnDouble(List<Map<String, Object?>> result,
      {String column = "a"}) {
    if (result.isEmpty || !result.first.containsKey(column)) {
      return 0;
    }

    try {
      double v = result.first[column] as double;
      return v.roundToDouble().toInt();
    } catch (e) {
      Log.e("getColumnDouble: $e");
      return 0;
    }
  }

  Future<List<GlucoseUAValueModel>> queryUserDataListOrderByTimePoint(
    String sn, {
    DateTime? start,
    DateTime? end,
    DataType? dataType,
  }) async {
    String where =
        "${GlucoseUAValueColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${GlucoseUAValueColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    if (dataType != null) {
      where = '$where AND ${GlucoseUAValueColumns.dataType} = ?';
      whereArgs.add(dataType.value);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy:
            '${GlucoseUAValueColumns.timePoint} asc, ${GlucoseUAValueColumns.time} asc');
    if (result.isNotEmpty) {
      return result.map((e) => GlucoseUAValueModel.fromJson(e)).toList();
    }

    return [];
  }
}
