/// FileName: data_target_model
///
/// @Author: ygc
/// @Date: 2024/8/8 13:47
/// @Description:
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../../framework/database/model/columns.dart';

part 'data_target_model.freezed.dart';
part 'data_target_model.g.dart';

const glucoseDataTargetTable = 'data_target';

class GlucoseDataTargetColumns extends DNUColumns {
  static const String uid = 'uid';
  static const String diastatic = 'diastatic';
  static const String dawnLow = 'dawnLow';
  static const String dawnHigh = 'dawnHigh';
  static const String emptyLow = 'emptyLow';
  static const String emptyHigh = 'emptyHigh';
  static const String mealBeforeLow = 'mealBeforeLow';
  static const String mealBeforeHigh = 'mealBeforeHigh';
  static const String mealAfterLow = 'mealAfterLow';
  static const String mealAfterHigh = 'mealAfterHigh';
  static const String sleepLow = 'sleepLow';
  static const String sleepHigh = 'sleepHigh';
  static const String userModified = 'userModified';
}

@unfreezed
class GlucoseDataTargetModel with _$GlucoseDataTargetModel {
  factory GlucoseDataTargetModel({
    @Default(0) int id,
    @Default('') String uid,
    @Default(0) double diastatic,
    @Default(0) double dawnLow,
    @Default(0) double dawnHigh,
    @Default(0) double emptyLow,
    @Default(0) double emptyHigh,
    @Default(0) double mealBeforeLow,
    @Default(0) double mealBeforeHigh,
    @Default(0) double mealAfterLow,
    @Default(0) double mealAfterHigh,
    @Default(0) double sleepLow,
    @Default(0) double sleepHigh,
    @Default(0) int userModified,
  }) = _GlucoseDataTargetModel;

  GlucoseDataTargetModel._();

  factory GlucoseDataTargetModel.fromJson(Map<String, Object?> json) =>
      _$GlucoseDataTargetModelFromJson(json);

  Map<String, dynamic> toDB() {
    final Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);

    return mapping;
  }
}
