/// FileName: data_target_provider
///
/// @Author: ygc
/// @Date: 2024/8/8 14:01
/// @Description:
import '../../../../../../framework/database/model/columns.dart';
import '../../../../../../framework/database/provider.dart';
import 'data_target_model.dart';

class GlucoseDataTargetProvider extends Provider {
  GlucoseDataTargetProvider._internal() : super(glucoseDataTargetTable);
  static final GlucoseDataTargetProvider _instance =
      GlucoseDataTargetProvider._internal();

  static GlucoseDataTargetProvider get instance {
    return _instance;
  }

  Future<GlucoseDataTargetModel?> queryTarget(String userSn) async {
    const String where = '${GlucoseDataTargetColumns.uid} = ?';
    final List<Map<String, Object?>> result =
        await queryMap(where: where, whereArgs: [userSn], limit: 1);
    if (result.isEmpty) {
      return null;
    }

    return GlucoseDataTargetModel.from<PERSON>son(result.first);
  }

  Future<GlucoseDataTargetModel> insertTarget(
      GlucoseDataTargetModel model) async {
    final int id = await insertMap(model.toDB());
    model.id = id;
    return model;
  }

  Future<int> updateTarget(GlucoseDataTargetModel model) async {
    if (model.id == 0) {
      return 0;
    }

    const String where = '${DNUColumns.id} = ?';
    final List<Object?> whereArgs = [
      model.id,
    ];

    return updateMap(model.toDB(), where: where, whereArgs: whereArgs);
  }
}
