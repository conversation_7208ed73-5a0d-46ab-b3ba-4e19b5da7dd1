/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'glucose_ua_value_model.freezed.dart';
part 'glucose_ua_value_model.g.dart';

const glucoseValueTable = 'glucose_ua';
const glucoseDataView = 'glucose_data_view';

class GlucoseUAValueColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String value = "value";
  static const String time = "time";
  static const String timePoint = "timePoint";
  static const String source = "source";
  static const String dataFrom = "dataFrom";
  static const String dataType = "dataType";
  static const String remark = "remark";
  static const String remark2 = "remark2";
  static const String extra = "extra";
  static const String sampleType = "sampleType";
  static const String upid = "upid";
}

// enum DataType {
//   glucose,
//   ua,
//   food,
//   sport,
// }

const int dataTypeGlucose = 0;
const int dataTypeUricAcid = 1;

// 0、血糖； 1、尿酸
// @immutable
// class DataType {
//   final int value;
//   final String name;
//
//   const DataType._(this.value, this.name);
//
//   static const DataType glucose = DataType._(dataTypeGlucose, '血糖');
//   static const DataType ua = DataType._(dataTypeUricAcid, '尿酸');
//
//   static DataType getDataFromByValue(int v) {
//     switch (v) {
//       case 1:
//         return ua;
//     }
//
//     return glucose;
//   }
// }

@unfreezed
class GlucoseUAValueModel with _$GlucoseUAValueModel {
  GlucoseUAValueModel._();

  factory GlucoseUAValueModel({
    @Default(0) int id,
    required String uid,
    required String did,
    required int value,
    required int time,
    required int timePoint,
    @Default(0) int source,
    @Default(1) int dataFrom,
    @Default(0) int dataType,
    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
    String? remark,
    String? remark2,
    String? extra,
    @Default(0) int sampleType,
    @Default(0) int upid,
  }) = _GlucoseUAValueModel;

  factory GlucoseUAValueModel.fromJson(Map<String, Object?> json) =>
      _$GlucoseUAValueModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = (DateTime.now().millisecondsSinceEpoch / 1000).round();
  }
}
