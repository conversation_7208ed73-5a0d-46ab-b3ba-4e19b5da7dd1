import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'storage_model.freezed.dart';

part 'storage_model.g.dart';

const storageTable = 'storage_table';

class StorageColumns extends DNUColumns {
  static const String identifier = 'identifier';
  static const String did = 'did';
  static const String type = 'type';
  static const String name = 'name';
  static const String generalname = 'generalname';
  static const String classColumn = 'class';
  static const String calories = 'calories';
  static const String starnum = 'starnum';
  static const String unit = 'unit';
  static const String imageurl = 'imageurl';
  static const String abstract = 'abstract';
  static const String level = 'level';
  static const String version = 'version';
  static const String fromuser = 'fromuser';
  static const String frequent = 'frequent';
  static const String imagedata = 'imagedata';
  static const String drugcount = 'drugcount';
  static const String carbohydrate = 'carbohydrate';
  static const String peoplesize = 'peoplesize';
  static const String effect = 'effect';
  static const String degree = 'degree';
  static const String sport_time = 'sport_time';
  static const String glycemic_Index = 'glycemic_Index';
  static const String price = 'price';
  static const String order = 'order';
}

@unfreezed
class StorageModel with _$StorageModel {
  factory StorageModel({
    @Default(0) int id,
    @Default(0) int did,
    @Default(0) int type,
    @Default('') String name,
    @Default('') @JsonKey(name: 'generalname') String generalName,
    @Default('') @JsonKey(name: 'class') String classValue,
    @Default('') String calories,
    @Default(0) @JsonKey(name: 'starnum') int starNum,
    @Default('') String unit,
    @Default('') @JsonKey(name: 'imageurl') String imageUrl,
    @Default('') @JsonKey(name: 'abstract') String linkUrl,
    @Default(0) int level,
    @Default(0) int version,
    @Default('') @JsonKey(name: 'fromuser') String fromUser,
    @Default(0) int frequent,
    @JsonKey(name: 'imagedata') dynamic imageData,
    @Default(0.0) double drugcount,
    @Default('') String carbohydrate,
    @Default('') @JsonKey(name: 'peoplesize') String peopleSize,
    @Default('') String effect,
    @Default('') String degree,
    @Default('') @JsonKey(name: 'sport_time') String sportTime,
    @Default('') @JsonKey(name: 'glycemic_Index') String glycemicIndex,
    @Default('') @JsonKey(name: 'price') String price,
    @Default(0) int order,
    @Default(0) @JsonKey(name: 'ua_starnum') int uaStarNum,
    @Default('') String gi,
    @Default('') String purine,
  }) = _StorageModel;

  StorageModel._();

  factory StorageModel.fromJson(Map<String, Object?> json) =>
      _$StorageModelFromJson(json);

  Map<String, dynamic> toDB() {
    final Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }
}
