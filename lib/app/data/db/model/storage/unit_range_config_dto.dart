/// FileName: unit_range_config_dto.dart
///
/// @Author: ygc
/// @Date: 2025-07-02 09:57:49
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:freezed_annotation/freezed_annotation.dart';

part 'unit_range_config_dto.freezed.dart';
part 'unit_range_config_dto.g.dart';

@freezed
class UnitRangeConfigDTO with _$UnitRangeConfigDTO {
  factory UnitRangeConfigDTO({
    @Default('')  String max,
    @Default('')  String min,
    @Default('') @<PERSON><PERSON><PERSON>ey(name: 'x')  String distance,
  }) = _UnitRangeConfigDTO;

  UnitRangeConfigDTO._();

  factory UnitRangeConfigDTO.fromJson(Map<String, Object?> json) =>
      _$UnitRangeConfigDTOFromJson(json);
}
