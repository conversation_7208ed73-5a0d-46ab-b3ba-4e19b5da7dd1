/// FileName: unit_standard_dto.dart
///
/// @Author: ygc
/// @Date: 2025-07-02 09:58:07
/// @Description:
///
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

import 'package:dnurse/app/data/db/model/storage/unit_range_config_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'unit_standard_dto.freezed.dart';
part 'unit_standard_dto.g.dart';

@freezed
class UnitStandardDTO with _$UnitStandardDTO {
  factory UnitStandardDTO({
    @Default('')  @JsonKey(name: 'r') String kilogram,
    @JsonKey(name: 's') UnitRangeConfigDTO? rangeConfig,
    @Default('') @J<PERSON><PERSON><PERSON>(name: 'u')  String id,
  }) = _UnitStandardDTO;

  UnitStandardDTO._();

  factory UnitStandardDTO.fromJson(Map<String, Object?> json) =>
      _$UnitStandardDTOFromJson(json);
}
