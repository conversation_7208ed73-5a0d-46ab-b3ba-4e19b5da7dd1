/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';

import '../../../../../framework/database/model/columns.dart';

part 'sport_data_model.freezed.dart';
part 'sport_data_model.g.dart';

const sportDataTable = 'data_sport';

class SportDataTableColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String upid = "upid";
  static const String name = "name";
  static const String time = "time";
  static const String calorie = "calorie";
  static const String duration = "duration";
  static const String fromType = "fromType";
  static const String isFromWalk = "isFromWalk";
}

@unfreezed
class SportDataModel with _$SportDataModel {
  SportDataModel._();

  factory SportDataModel({
    @Default(0) int id,
    required String uid,
    required String did,
    @Default(0) int upid,
    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
    @Default(0) int calorie,
    required int time,
    required String name,
    required int duration,
    @Default(0)  int fromType,
    @Default(0)  int isFromWalk,
  }) = _SportDataModel;

  factory SportDataModel.fromJson(Map<String, Object?> json) =>
      _$SportDataModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = (DateTime.now().millisecondsSinceEpoch / 1000).round();
  }

  String sportTime() {
    return DateTime.fromMillisecondsSinceEpoch(time * 1000)
        .formatDate(DateFormat.HOUR24_MINUTE);
  }
}
