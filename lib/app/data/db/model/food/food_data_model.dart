/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'food_data_model.freezed.dart';
part 'food_data_model.g.dart';

const foodDataTable = 'data_food';

class FoodDataTableColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String upId = "upId";
  static const String time = "time";
  static const String timePoint = "timePoint";
  static const String calorie = "calorie";
  static const String items = "items";
}

@unfreezed
class FoodDataModel with _$FoodDataModel {
  FoodDataModel._();

  factory FoodDataModel({
    @Default(0) int id,
    required String uid,
    required String did,
    @Default(0) int upId,
    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
    @Default(0) int calorie,
    String? items,
    required int time,
    required int timePoint,
  }) = _FoodDataModel;

  factory FoodDataModel.fromJson(Map<String, Object?> json) =>
      _$FoodDataModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = DateTime.now().secondsSinceEpoch;
  }
}
