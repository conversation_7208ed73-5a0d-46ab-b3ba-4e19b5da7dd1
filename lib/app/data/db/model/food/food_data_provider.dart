import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/framework/utils/datetime_extension.dart';
import 'package:dnurse/framework/utils/log.dart';

import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import 'food_data_model.dart';
import 'day_calories_model.dart';

class FoodDataProvider extends Provider {
  FoodDataProvider._internal() : super(foodDataTable);
  static final FoodDataProvider _instance = FoodDataProvider._internal();

  static FoodDataProvider get instance {
    return _instance;
  }

  /// 插入一条数据
  Future<FoodDataModel> insert(FoodDataModel sportDataModel) async {
    if (sportDataModel.did.isEmpty) {
      sportDataModel.did = DataUtils.generateDidWithPrefix('CGM');
    }
    int id = await insertMap(sportDataModel.toDB());
    sportDataModel.id = id;

    return sportDataModel;
  }

  /// 根据did更新数据
  Future<int> updateDataByDid(FoodDataModel dataFood) async {
    String where =
        "${FoodDataTableColumns.did} = ? and ${FoodDataTableColumns.uid} = ?";
    List<Object?> whereArgs = [
      dataFood.did,
      dataFood.uid,
    ];

    return await updateMap(dataFood.toDB(), where: where, whereArgs: whereArgs);
  }

  /// 插入或更新数据
  Future<int> insertOrUpdateData(FoodDataModel sportDataModel) async {
    FoodDataModel? row = await getDataByDid(
      sportDataModel.did,
      sportDataModel.uid,
    );

    if (row == null) {
      FoodDataModel ret = await insert(sportDataModel);
      return ret.id;
    }

    return await updateDataByDid(sportDataModel);
  }

  /// 标记数据修改状态和upid
  Future<int> markModified(int id, {bool modified = false, int? upid}) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      id,
    ];

    Map<String, dynamic> data = {DNUColumns.modified: modified ? 1 : 0};
    if (upid != null) {
      data[FoodDataTableColumns.upId] = upid;
    }
    return await updateMap(data, where: where, whereArgs: whereArgs);
  }

  /// 根据did查询数据
  Future<FoodDataModel?> getDataByDid(String did, String uid) async {
    String where =
        "${FoodDataTableColumns.uid} = ?  and ${FoodDataTableColumns.did} = ?";
    List<Object?> whereArgs = [
      uid,
      did,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );
    if (result.isNotEmpty) {
      return FoodDataModel.fromJson(result.first);
    }

    return null;
  }

  /// 查询数据列表
  /// sn 用户sn，必传
  /// start 起始时间，包含时间戳为起始时间的数据 >=
  /// end 结束时间，不包含时间戳为结束时间的数据 <
  Future<List<FoodDataModel>> queryUserDataList(String sn,
      {DateTime? start, DateTime? end, bool orderAsc = false}) async {
    String where =
        "${FoodDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${FoodDataTableColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${FoodDataTableColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${FoodDataTableColumns.time}  ${orderAsc ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return result.map((e) => FoodDataModel.fromJson(e)).toList();
    }

    return [];
  }

  /// 查询修改的数据
  Future<List<FoodDataModel>> queryUserModifyList(String sn) async {
    String where =
        "${FoodDataTableColumns.uid} = ? AND ${DNUColumns.modified} = 1";
    List<Object?> whereArgs = [
      sn,
    ];

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${FoodDataTableColumns.time} ASC');
    if (result.isNotEmpty) {
      return result.map((e) => FoodDataModel.fromJson(e)).toList();
    }

    return [];
  }

  Future<int> queryMaxUpid(String sn, {DateTime? start, DateTime? end}) async {
    String where = "${FoodDataTableColumns.uid} = ?";
    List<Object?> whereArgs = [
      sn,
    ];

    if (start != null) {
      where = '$where AND ${FoodDataTableColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${FoodDataTableColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
      columns: ['Max(${FoodDataTableColumns.upId}) AS maxUpid'],
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isNotEmpty) {
      try {
        return result.first['maxUpid'] as int;
      } catch (e) {
        return 0;
      }
    }

    return 0;
  }

  /// 查询一天数据的条数
  /// 参数sn 用户sn
  /// 参数dateTime 某一天的起始时间，查询指定时间点的24小时
  Future<int> queryNumberOfDataInDay(String sn, DateTime dateTime) async {
    int n = 0;
    List<String> sel = ['COUNT(${FoodDataTableColumns.did}) AS num'];
    String where =
        '${FoodDataTableColumns.uid} = ? AND ${DNUColumns.deleted} = 0 AND ${FoodDataTableColumns.time} >= ? AND ${FoodDataTableColumns.time} < ?';
    int start = (dateTime.millisecondsSinceEpoch / 1000).round();
    List<dynamic> whereArgs = [sn, start, start + 86400];
    List<Map<String, Object?>> result =
        await queryMap(columns: sel, where: where, whereArgs: whereArgs);
    Log.d('$dateTime的数据条数：$result');
    if (result.isNotEmpty) {
      Map<String, Object?> item = result.first;
      n = item['num'] as int;
    }

    return n;
  }

  Future<FoodDataModel?> queryMaxMin(String sn,
      {DateTime? start, DateTime? end, bool isMax = true}) async {
    String where =
        "${FoodDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${FoodDataTableColumns.time} >= ?';
      whereArgs.add(start.secondsSinceEpoch);
    }

    if (end != null) {
      where = '$where AND ${FoodDataTableColumns.time} < ?';
      whereArgs.add(end.secondsSinceEpoch);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        limit: 1,
        orderBy: '${FoodDataTableColumns.time}  ${!isMax ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return FoodDataModel.fromJson(result.first);
    }

    return null;
  }

  Future<int> getFoodCalorie(String sn, int startTime, int endTime,
      {int? type}) async {
    String where =
        "${FoodDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ? and ${FoodDataTableColumns.time} >= ? and ${FoodDataTableColumns.time} < ?";
    List<Object?> whereArgs = [
      sn,
      0,
      startTime,
      endTime,
    ];

    if (type != null) {
      where = '$where AND ${FoodDataTableColumns.timePoint} = ?';
      whereArgs.add(type);
    }

    List<Map<String, Object?>> result = await queryMap(
      columns: ["sum(${FoodDataTableColumns.calorie}) as calorie"],
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isNotEmpty) {
      return getColumnInt(
        result,
        column: 'calorie',
      );
    }

    return 0;
  }

  Future<List<FoodDataModel>> getFoodByTimeType(
      String sn, int startTime, int endTime,
      {int? type}) async {
    String where =
        "${FoodDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ? and ${FoodDataTableColumns.time} >= ? and ${FoodDataTableColumns.time} < ?";
    List<Object?> whereArgs = [
      sn,
      0,
      startTime,
      endTime,
    ];

    if (type != null) {
      where = '$where AND ${FoodDataTableColumns.timePoint} = ?';
      whereArgs.add(type);
    }

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      orderBy:
          '${FoodDataTableColumns.timePoint} asc , ${FoodDataTableColumns.time} desc',
    );

    if (result.isEmpty) {
      return [];
    }

    return result.map((e) {
      return FoodDataModel.fromJson(e);
    }).toList();
  }

  Future<List<DayCaloriesModel>> queryIntakeCaloriesByDay(String sn, int startTime, int endTime) async {
    // select datetime(time, 'unixepoch', 'localtime','start of day') as day, sum(calorie)  as calorie from food_table where time >= 1751212800 and  time < 1751904000 group by datetime(time, 'unixepoch', 'localtime','start of day')
    String where =
        "${FoodDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ? and ${FoodDataTableColumns.time} >= ? and ${FoodDataTableColumns.time} < ? and ${FoodDataTableColumns.calorie} > 0";
    List<Object?> whereArgs = [
      sn,
      0,
      startTime,
      endTime,
    ];

    List<String> columns = [
      "datetime(${FoodDataTableColumns.time}, 'unixepoch', 'localtime','start of day') as day",
      "sum(${FoodDataTableColumns.calorie}) as calorie",
    ];

    List<Map<String, Object?>> result = await queryMap(
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: "datetime(${FoodDataTableColumns.time}, 'unixepoch', 'localtime','start of day') ",
    );

    if (result.isEmpty) {
      return [];
    }

    return result.map((e) {
      return DayCaloriesModel.fromJson(e);
    }).toList();
  }
}
