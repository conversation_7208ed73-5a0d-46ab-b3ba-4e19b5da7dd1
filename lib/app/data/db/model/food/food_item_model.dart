/// FileName: food_item_model
///
/// @Author: ygc
/// @Date: 2025/7/10 19:14
/// @Description:
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'food_item_model.freezed.dart';
part 'food_item_model.g.dart';

@unfreezed
class FoodItemModel with _$FoodItemModel {
  FoodItemModel._();

  factory FoodItemModel({
    @Default('') String imgUrl,
    @Default(0) int calorie,
    required String name,
    required int unit,
    required num amount,
    @Default(0) int fromUser,
  }) = _FoodItemModel;

  factory FoodItemModel.fromJson(Map<String, Object?> json) =>
      _$FoodItemModelFromJson(json);
}
