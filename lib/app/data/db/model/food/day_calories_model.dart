/// FileName: day_calories_model
///
/// @Author: ygc
/// @Date: 2025/7/4 16:41
/// @Description: 
import 'package:freezed_annotation/freezed_annotation.dart';

part 'day_calories_model.freezed.dart';
part 'day_calories_model.g.dart';

@freezed
class DayCaloriesModel with _$DayCaloriesModel {
  factory DayCaloriesModel({
    @Default('')  String day,
    @Default(0) int calorie,

  }) = _DayCaloriesModel;

  DayCaloriesModel._();

  factory DayCaloriesModel.fromJson(Map<String, Object?> json) =>
      _$DayCaloriesModelFromJson(json);
}
