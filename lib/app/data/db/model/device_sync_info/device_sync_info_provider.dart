/// FileName: device_sync_info_provider
///
/// @Author: ygc
/// @Date: 2024/9/30 10:33
/// @Description:

import '../../../../../framework/database/provider.dart';
import '../../../../../framework/utils/log.dart';
import 'device_sync_info_model.dart';

class DeviceSyncInfoProvider extends Provider {
  DeviceSyncInfoProvider._internal() : super(deviceSyncInfoTable);
  static final DeviceSyncInfoProvider _instance =
      DeviceSyncInfoProvider._internal();

  static DeviceSyncInfoProvider get instance {
    return _instance;
  }

  Future<DeviceSyncInfoModel> insertInfo(DeviceSyncInfoModel model) async {
    final int id = await insertMap(model.toDB());
    model.id = id;
    return model;
  }

  Future<List<String>?> getDeviceSyncDataId(
    String uid,
    String deviceSn,
    int device,
  ) async {
    String where =
        "${DeviceSyncInfoColumns.uid} = ? AND ${DeviceSyncInfoColumns.deviceSn} = ? "
        " AND ${DeviceSyncInfoColumns.deviceType} = ? ";

    List<Object?> whereArgs = [
      uid,
      deviceSn,
      device,
    ];

    Log.d('getSyncInfoList: $where, $whereArgs');

    List<Map<String, Object?>> result = await queryMap(
      distinct: true,
      columns: [DeviceSyncInfoColumns.did],
      where: where,
      whereArgs: whereArgs,
    );
    if (result.isEmpty) {
      return null;
    }

    return result.map((e) => e[DeviceSyncInfoColumns.did] as String).toList();
  }
}
