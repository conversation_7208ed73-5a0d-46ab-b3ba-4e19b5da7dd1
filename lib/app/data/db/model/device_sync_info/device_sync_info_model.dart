/// FileName: devcie_sync_data_model
///
/// @Author: ygc
/// @Date: 2024/9/30 10:14
/// @Description:

import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'device_sync_info_model.freezed.dart';

part 'device_sync_info_model.g.dart';

const deviceSyncInfoTable = 'device_sync_info';

class DeviceSyncInfoColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String deviceType = "deviceType";
  static const String deviceSn = "deviceSn";
}

@unfreezed
class DeviceSyncInfoModel with _$DeviceSyncInfoModel {
  DeviceSyncInfoModel._();

  factory DeviceSyncInfoModel({
    @Default(0) int id,
    required String uid,
    required String did,
    required int deviceType,
    required String deviceSn,
  }) = _DeviceSyncInfoModel;

  factory DeviceSyncInfoModel.fromJson(Map<String, Object?> json) =>
      _$DeviceSyncInfoModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }
}
