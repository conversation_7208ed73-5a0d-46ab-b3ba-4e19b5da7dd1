/// FileName: monitor_plan_model
///
/// @Author: ygc
/// @Date: 2024/8/6 16:32
/// @Description:

import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'monitor_plan_model.freezed.dart';
part 'monitor_plan_model.g.dart';

const monitorPlanTable = 'monitor_plan';

class MonitorPlanColumns extends DNUColumns {
  static const String hour = 'hour';
  static const String minute = 'minute';
  static const String repeated = 'repeated';
  static const String complete = 'complete';
  static const String enable = 'enable';
  static const String timePoint = 'timePoint';
  static const String uid = 'uid';
  static const String did = 'did';
  static const String type = 'type';
}

@unfreezed
class MonitorPlanModel with _$MonitorPlanModel {
  factory MonitorPlanModel({
    @Default(0) int id,
    @Default('') String uid,
    @Default('') String did,
    @Default(0) int timePoint,
    @Default(0) int hour,
    @Default(0) int minute,
    @Default(0) int repeated,
    @Default(0) int enable,
    @Default(0) int type,
    @Default(0) int complete,
  }) = _MonitorPlanModel;

  MonitorPlanModel._();

  factory MonitorPlanModel.fromJson(Map<String, Object?> json) =>
      _$MonitorPlanModelFromJson(json);

  Map<String, dynamic> toDB() {
    final Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);

    return mapping;
  }
}
