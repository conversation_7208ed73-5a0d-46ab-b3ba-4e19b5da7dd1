/// FileName: monitor_plan_provider
///
/// @Author: ygc
/// @Date: 2024/8/6 16:38
/// @Description:
///
import 'package:dnurse/framework/database/model/columns.dart';

import '../../../../../framework/database/provider.dart';
import 'monitor_plan_model.dart';

class MonitorPlanProvider extends Provider {
  MonitorPlanProvider._internal() : super(monitorPlanTable);
  static final MonitorPlanProvider _instance = MonitorPlanProvider._internal();

  static MonitorPlanProvider get instance {
    return _instance;
  }

  Future<List<MonitorPlanModel>> queryMonitorPlan(String userSn) async {
    const String where = '${MonitorPlanColumns.uid} = ?';
    final List<Map<String, Object?>> result =
        await queryMap(where: where, whereArgs: [userSn]);

    return result.map((e) => MonitorPlanModel.fromJson(e)).toList();
  }

  Future<MonitorPlanModel> insertMonitorPlan(MonitorPlanModel model) async {
    final int id = await insertMap(model.toDB());
    model.id = id;
    return model;
  }

  Future<int> updateMonitorPlan(MonitorPlanModel model) async {
    if (model.id == 0) {
      return 0;
    }

    const String where = '${DNUColumns.id} = ?';
    final List<Object?> whereArgs = [
      model.id,
    ];

    return updateMap(model.toDB(), where: where, whereArgs: whereArgs);
  }

  Future<MonitorPlanModel?> queryMonitorPlanByTimePoint(
      String userSn, int timePoint) async {
    const String where =
        '${MonitorPlanColumns.uid} = ? and ${MonitorPlanColumns.timePoint} = ?';
    final List<Map<String, Object?>> result =
        await queryMap(where: where, whereArgs: [userSn, timePoint], limit: 1);
    if (result.isNotEmpty) {
      return MonitorPlanModel.fromJson(result.first);
    }
    return null;
  }
}
