/// FileName: day_drug_model
///
/// @Author: ygc
/// @Date: 2025/7/10 13:41
/// @Description: 
import 'package:freezed_annotation/freezed_annotation.dart';

part 'day_drug_model.freezed.dart';
part 'day_drug_model.g.dart';

@freezed
class DayDrugModel with _$DayDrugModel {
  factory DayDrugModel({
    @Default('')  String day,
    @Default(0) int count,

  }) = _DayDrugModel;

  DayDrugModel._();

  factory DayDrugModel.fromJson(Map<String, Object?> json) =>
      _$DayDrugModelFromJson(json);
}