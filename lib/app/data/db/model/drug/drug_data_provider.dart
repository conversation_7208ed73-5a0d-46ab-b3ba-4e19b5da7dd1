import 'package:dnurse/app/data/common/data_utils.dart';
import 'package:dnurse/app/data/db/model/drug/drug_data_model.dart';

import '../../../../../framework/database/model/columns.dart';
import '../../../../../framework/database/provider.dart';
import 'day_drug_model.dart';

class DrugDataProvider extends Provider {
  DrugDataProvider._internal() : super(drugDataTable);
  static final DrugDataProvider _instance = DrugDataProvider._internal();

  static DrugDataProvider get instance {
    return _instance;
  }

  /// 插入一条数据
  Future<DrugDataModel> insert(DrugDataModel sportDataModel) async {
    if (sportDataModel.did.isEmpty) {
      sportDataModel.did = DataUtils.generateDidWithPrefix('DR');
    }
    int id = await insertMap(sportDataModel.toDB());
    sportDataModel.id = id;

    return sportDataModel;
  }

  /// 根据did更新数据
  Future<int> updateDataByDid(DrugDataModel sportDataModel) async {
    String where =
        "${DrugDataTableColumns.did} = ? and ${DrugDataTableColumns.uid} = ?";
    List<Object?> whereArgs = [
      sportDataModel.did,
      sportDataModel.uid,
    ];

    return await updateMap(sportDataModel.toDB(),
        where: where, whereArgs: whereArgs);
  }

  /// 插入或更新数据
  Future<int> insertOrUpdateData(DrugDataModel sportDataModel) async {
    DrugDataModel? row =
        await getDataByDid(sportDataModel.did, sportDataModel.uid);

    if (row == null) {
      DrugDataModel ret = await insert(sportDataModel);
      return ret.id;
    }

    return await updateDataByDid(sportDataModel);
  }

  /// 标记数据修改状态和upid
  Future<int> markModified(int id, {bool modified = false, int? upid}) async {
    String where = "${DNUColumns.id} = ?";
    List<Object?> whereArgs = [
      id,
    ];

    Map<String, dynamic> data = {DNUColumns.modified: modified ? 1 : 0};
    if (upid != null) {
      data[DrugDataTableColumns.upid] = upid;
    }
    return await updateMap(data, where: where, whereArgs: whereArgs);
  }

  /// 根据did查询数据
  Future<DrugDataModel?> getDataByDid(
    String did,
    String uid,
  ) async {
    String where =
        "${DrugDataTableColumns.did} = ? and ${DrugDataTableColumns.uid} = ?";
    List<Object?> whereArgs = [
      did,
      uid,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );
    if (result.isNotEmpty) {
      return DrugDataModel.fromJson(result.first);
    }

    return null;
  }

  /// 查询数据列表
  /// sn 用户sn，必传
  /// start 起始时间，包含时间戳为起始时间的数据 >=
  /// end 结束时间，不包含时间戳为结束时间的数据 <
  Future<List<DrugDataModel>> queryUserDataList(String sn,
      {DateTime? start, DateTime? end, bool orderAsc = false}) async {
    String where =
        "${DrugDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ?";
    List<Object?> whereArgs = [
      sn,
      0,
    ];

    if (start != null) {
      where = '$where AND ${DrugDataTableColumns.time} >= ?';
      whereArgs.add(start.millisecondsSinceEpoch / 1000);
    }

    if (end != null) {
      where = '$where AND ${DrugDataTableColumns.time} < ?';
      whereArgs.add(end.millisecondsSinceEpoch / 1000);
    }

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${DrugDataTableColumns.time}  ${orderAsc ? 'asc' : 'desc'}');
    if (result.isNotEmpty) {
      return result.map((e) => DrugDataModel.fromJson(e)).toList();
    }

    return [];
  }

  /// 查询修改的数据
  Future<List<DrugDataModel>> queryUserModifyList(String sn) async {
    String where =
        "${DrugDataTableColumns.uid} = ? AND ${DNUColumns.modified} = 1";
    List<Object?> whereArgs = [
      sn,
    ];

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        orderBy: '${DrugDataTableColumns.time} ASC');
    if (result.isNotEmpty) {
      return result.map((e) => DrugDataModel.fromJson(e)).toList();
    }

    return [];
  }

  Future<DrugDataModel?> getLatestDrug(String sn) async {
    String where =
        "${DrugDataTableColumns.uid} = ? AND ${DNUColumns.deleted} = 0";
    List<Object?> whereArgs = [
      sn,
    ];

    List<Map<String, Object?>> result = await queryMap(
        where: where,
        whereArgs: whereArgs,
        limit: 1,
        orderBy: '${DrugDataTableColumns.time} DESC');
    if (result.isNotEmpty) {
      return DrugDataModel.fromJson(result.first);
    }

    return null;
  }

  Future<List<DayDrugModel>> queryDrugCountByDay(String sn, int startTime, int endTime) async {
    // select datetime(time, 'unixepoch', 'localtime','start of day') as day, sum(calorie)  as calorie from food_table where time >= 1751212800 and  time < 1751904000 group by datetime(time, 'unixepoch', 'localtime','start of day')
    String where =
        "${DrugDataTableColumns.uid} = ? and ${DNUColumns.deleted} = ? and ${DrugDataTableColumns.time} >= ? and ${DrugDataTableColumns.time} < ?";
    List<Object?> whereArgs = [
      sn,
      0,
      startTime,
      endTime,
    ];

    List<String> columns = [
      "datetime(${DrugDataTableColumns.time}, 'unixepoch', 'localtime','start of day') as day",
      "count(${DrugDataTableColumns.time}) as count",
    ];

    List<Map<String, Object?>> result = await queryMap(
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: "datetime(${DrugDataTableColumns.time}, 'unixepoch', 'localtime','start of day') ",
    );

    if (result.isEmpty) {
      return [];
    }

    return result.map((e) {
      return DayDrugModel.fromJson(e);
    }).toList();
  }
}
