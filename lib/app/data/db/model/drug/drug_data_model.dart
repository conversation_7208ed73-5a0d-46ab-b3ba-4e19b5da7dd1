/// FileName: glucose_value_model
///
/// @Author: ygc
/// @Date: 2022/6/23 15:18
/// @Description: TO
///
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'drug_data_model.freezed.dart';
part 'drug_data_model.g.dart';

const drugDataTable = 'data_drug';

class DrugDataTableColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String upid = "upid";
  static const String name = "name";
  static const String time = "time";
  static const String num = "num";
  static const String category = "category";
  static const String isPlan = "isPlan";
  static const String fromUser = "fromUser";
  static const String insulinkPenName = "insulinkPenName";
  static const String uuid = "uuid";
}

@unfreezed
class DrugDataModel with _$DrugDataModel {
  DrugDataModel._();

  factory DrugDataModel({
    @Default(0) int id,
    required String uid,
    required String did,
    @Default(0) int upid,
    @Default(0) int modifiedAt,
    @Default(0) int deleted,
    @Default(0) int modified,
    @Default(0) int isPlan,
    required String name,
    required int category,
    @Default(0) @JsonKey(name: "num")   int numCount,
    required int time,
    @Default(0) int fromUser,
    String? insulinkPenName,
    String? uuid,
  }) = _DrugDataModel;

  factory DrugDataModel.fromJson(Map<String, Object?> json) =>
      _$DrugDataModelFromJson(json);

  Map<String, dynamic> toDB() {
    Map<String, dynamic> mapping = toJson();
    mapping.remove(DNUColumns.id);
    return mapping;
  }

  void markModified({bool markDeleted = false}) {
    modified = 1;
    deleted = markDeleted ? 1 : 0;
    modifiedAt = (DateTime.now().millisecondsSinceEpoch / 1000).round();
  }
}
