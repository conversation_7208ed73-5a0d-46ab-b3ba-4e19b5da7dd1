/// FileName: data_log_provider
///
/// @Author: ygc
/// @Date: 2025/7/10 16:16
/// @Description:
import 'package:dnurse/framework/utils/datetime_extension.dart';

import '../../../../../framework/database/provider.dart';
import 'data_log_model.dart';

class DataLogProvider extends Provider {
  DataLogProvider._internal() : super(dataLogView);
  static final DataLogProvider _instance = DataLogProvider._internal();

  static DataLogProvider get instance {
    return _instance;
  }

  Future<List<DataLogModel>> queryData(
    String sn,
    DateTime startTime,
    DateTime endTime,
  ) async {
    String where =
        "${DataLogViewColumns.uid} = ? and ${DataLogViewColumns.time} >= ? and ${DataLogViewColumns.time} < ?";
    List<Object?> whereArgs = [
      sn,
      startTime.secondsSinceEpoch,
      endTime.secondsSinceEpoch,
    ];

    List<Map<String, Object?>> result = await queryMap(
      where: where,
      whereArgs: whereArgs,
      orderBy: '${DataLogViewColumns.time} desc',
    );

    if (result.isEmpty) {
      return [];
    }

    return result.map((e) {
      return DataLogModel.fromJson(e);
    }).toList();
  }

  Future<List<Map<String, Object?>> > queryDataMap(
      String sn,
      DateTime startTime,
      DateTime endTime,
      ) async {
    String where =
        "${DataLogViewColumns.uid} = ? and ${DataLogViewColumns.time} >= ? and ${DataLogViewColumns.time} < ?";
    List<Object?> whereArgs = [
      sn,
      startTime.secondsSinceEpoch,
      endTime.secondsSinceEpoch,
    ];

    return queryMap(
      where: where,
      whereArgs: whereArgs,
      orderBy: '${DataLogViewColumns.time} desc',
    );
  }
}
