/// FileName: data_log_model
///
/// @Author: ygc
/// @Date: 2025/7/10 16:05
/// @Description:
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../framework/database/model/columns.dart';

part 'data_log_model.freezed.dart';
part 'data_log_model.g.dart';

const dataLogView = 'data_log';

//                        unit    num category    isPlan
class DataLogViewColumns extends DNUColumns {
  static const String uid = "uid";
  static const String did = "did";
  static const String value = "value";
  static const String name = "name";
  static const String time = "time";
  static const String num = "num";
  static const String unit = "unit";
  static const String category = "category";
  static const String timePoint = "timePoint";
  static const String source = "source";
  static const String dataType = "dataType";
  static const String fromUser = "fromUser";
  static const String amount = "amount";
  static const String calorie = "calorie";
  static const String isPlan = "isPlan";
}

@unfreezed
class DataLogModel with _$DataLogModel {
  DataLogModel._();

  factory DataLogModel({
    @Default(0) int id,
    required String uid,
    required String did,
    @Default(0) int value,
    @Default('') String name,
    @Default(0) int time,
    @Default(0) @JsonKey(name: "num")  int numCount,
    @Default(0) int unit,
    @Default(0) int category,
    @Default(0) int fromUser,
    @Default(0) int source,
    @Default(0) int dataType,
    @Default(0) num amount,
    @Default(0) int calorie,
    @Default(0) int isPlan,
    @Default(0) int timePoint,
  }) = _DataLogModel;

  factory DataLogModel.fromJson(Map<String, Object?> json) =>
      _$DataLogModelFromJson(json);
}
