/// FileName: device_sync_migration
///
/// @Author: ygc
/// @Date: 2024/9/30 13:47
/// @Description:
import '../../../../../framework/database/model/migration.dart';
import '../../../../framework/database/model/columns.dart';
import '../model/device_sync_info/device_sync_info_model.dart';

class DeviceSyncMigration extends Migration {
  DeviceSyncMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $deviceSyncInfoTable (
          ${DNUColumns.id}  INTEGER PRIMARY KEY AUTOINCREMENT, 
          ${DeviceSyncInfoColumns.uid} TEXT,
          ${DeviceSyncInfoColumns.deviceSn} TEXT,
          ${DeviceSyncInfoColumns.deviceType} INTEGER,
          ${DeviceSyncInfoColumns.did} TEXT
      );
    """;
  }
}
