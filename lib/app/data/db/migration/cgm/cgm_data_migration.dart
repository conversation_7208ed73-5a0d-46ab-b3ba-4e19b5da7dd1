/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///
import '../../../../../../framework/database/model/migration.dart';
import '../../model/cgm/cgm_data_model.dart';
import '../../model/cgm/cgm_data_package_model.dart';

class CgmDataMigration extends Migration {
  CgmDataMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $cgmDataTable (
          ${Migration.getCommSql()}
          ${CGMDataTableColumns.uid} TEXT,
          ${CGMDataTableColumns.did} TEXT,
          ${CGMDataTableColumns.value} DECIMAL ( 5, 1 ),
          ${CGMDataTableColumns.time} INTEGER,
          ${CGMDataTableColumns.pid} TEXT,
          ${CGMDataTableColumns.rValue} DECIMAL ( 5, 1 ),
          ${CGMDataTableColumns.temp} INTEGER,
          ${CGMDataTableColumns.flags} INTEGER,
          ${CGMDataTableColumns.point} SMALLINT,
          ${CGMDataTableColumns.devType} SMALLINT,
          ${CGMDataTableColumns.devSn} TEXT,
          ${CGMDataTableColumns.startTime} INTEGER,
          ${CGMDataTableColumns.upid} INTEGER
      );
    """;
  }

  static String getCreateDataPackageSql() {
    return """CREATE TABLE $cgmDataPackageTable (
          ${Migration.getCommSql()}
          ${CGMDataPackageTableColumns.uid} TEXT,
          ${CGMDataPackageTableColumns.did} TEXT,
          ${CGMDataPackageTableColumns.dataStr} TEXT,
          ${CGMDataPackageTableColumns.devSn} TEXT,
          ${CGMDataPackageTableColumns.devType} SMALLINT,
          ${CGMDataPackageTableColumns.captureTime} INTEGER,
          ${CGMDataPackageTableColumns.upid} INTEGER,
          ${CGMDataPackageTableColumns.startTime} INTEGER,
          ${CGMDataPackageTableColumns.identifierStr} TEXT,
          ${CGMDataPackageTableColumns.icManufacturerCode} INTEGER,
          ${CGMDataPackageTableColumns.icSerialNumber} TEXT,
          ${CGMDataPackageTableColumns.isAnalytic} SMALLINT
      );
    """;
  }
}
