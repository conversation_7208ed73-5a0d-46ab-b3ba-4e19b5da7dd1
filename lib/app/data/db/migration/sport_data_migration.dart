/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///

import '../../../../../../framework/database/model/migration.dart';
import '../model/sport/sport_data_model.dart';

class SportDataMigration extends Migration {
  SportDataMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $sportDataTable (
          ${Migration.getCommSql()}
          ${SportDataTableColumns.uid} TEXT,
          ${SportDataTableColumns.did} TEXT,
          ${SportDataTableColumns.time} INTEGER,
          ${SportDataTableColumns.name} TEXT,
          ${SportDataTableColumns.duration} INTEGER,
          ${SportDataTableColumns.fromType} SMALLINT,
          ${SportDataTableColumns.calorie} SMALLINT,
          ${SportDataTableColumns.isFromWalk} SMALLINT,
          ${SportDataTableColumns.upid} INTEGER
      );
    """;
  }
}
