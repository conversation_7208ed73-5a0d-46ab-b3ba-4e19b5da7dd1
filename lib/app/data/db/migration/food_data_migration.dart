/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///
import 'package:dnurse/app/data/db/model/food/food_data_model.dart';

import '../../../../../../framework/database/model/migration.dart';

class FoodDataMigration extends Migration {
  FoodDataMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $foodDataTable (
          ${Migration.getCommSql()}
          ${FoodDataTableColumns.uid} TEXT,
          ${FoodDataTableColumns.did} TEXT,
          ${FoodDataTableColumns.time} INTEGER, 
          ${FoodDataTableColumns.timePoint} SMALLINT,
          ${FoodDataTableColumns.calorie} DECIMAL ( 5, 1 ),
          ${FoodDataTableColumns.items} TEXT,
          ${FoodDataTableColumns.upId} INTEGER
      );
    """;
  }
}
