/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///
import '../../../../../../framework/database/model/migration.dart';
import '../../../model/glucose/target/data_target_model.dart';

class GlucoseDataTargetMigration extends Migration {
  GlucoseDataTargetMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $glucoseDataTargetTable (
          ${Migration.getCommIdSql()}
          ${GlucoseDataTargetColumns.uid} TEXT,
          ${GlucoseDataTargetColumns.diastatic} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.dawnLow} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.dawnHigh} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.emptyLow} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.emptyHigh} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.mealBeforeLow} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.mealBeforeHigh} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.mealAfterLow} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.mealAfterHigh} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.sleepLow} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.sleepHigh} DECIMAL ( 5, 1 ),
          ${GlucoseDataTargetColumns.userModified} SMALLINT
      );
    """;
  }
}
