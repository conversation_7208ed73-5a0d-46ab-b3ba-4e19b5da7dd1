/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///
import '../../../../../../framework/database/model/migration.dart';
import '../../model/glucose/data_sync_time_interval_model.dart';

class DataSyncTimeIntervalMigration extends Migration {
  DataSyncTimeIntervalMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $dataSyncTimeIntervalTable (
          ${Migration.getCommIdSql()}
          ${DataSyncTimeIntervalColumns.uid} TEXT,
          ${DataSyncTimeIntervalColumns.start} INTEGER,
          ${DataSyncTimeIntervalColumns.end} INTEGER
      );
    """;
  }
}
