/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///
import 'package:dnurse/framework/database/model/columns.dart';

import '../../../../../../framework/database/model/migration.dart';
import '../../model/glucose/glucose_ua_value_model.dart';

class GlucoseUADataMigration extends Migration {
  GlucoseUADataMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $glucoseValueTable (
          ${Migration.getCommSql()}
          ${GlucoseUAValueColumns.uid} TEXT,
          ${GlucoseUAValueColumns.did} TEXT,
          ${GlucoseUAValueColumns.value} INTEGER,
          ${GlucoseUAValueColumns.time} INTEGER,
          ${GlucoseUAValueColumns.timePoint} SMALLINT,
          ${GlucoseUAValueColumns.source} SMALLINT,
          ${GlucoseUAValueColumns.dataFrom} SMALLINT,
          ${GlucoseUAValueColumns.dataType} SMALLINT,
          ${GlucoseUAValueColumns.upid} INTEGER
      );
    """;
  }

  static String getCreateViewSql() {
    return 'CREATE VIEW $glucoseDataView '
        'AS SELECT * FROM $glucoseValueTable '
        'WHERE ${GlucoseUAValueColumns.dataType} = 0 AND ${GlucoseUAValueColumns.value} >= 110 '
        'AND ${GlucoseUAValueColumns.value} < 3340 AND ${DNUColumns.deleted} = 0';
  }

  static String addRemarkColumn() {
    return """ALTER TABLE $glucoseValueTable ADD ${GlucoseUAValueColumns.remark} TEXT;""";
  }

  static String addRemark2Column() {
    return """ALTER TABLE $glucoseValueTable ADD ${GlucoseUAValueColumns.remark2} TEXT;""";
  }

  static String addExtraColumn() {
    return """ALTER TABLE $glucoseValueTable ADD ${GlucoseUAValueColumns.extra} TEXT;""";
  }

  static String addExtraSampleTypeColumn() {
    return """ALTER TABLE $glucoseValueTable ADD ${GlucoseUAValueColumns.sampleType} INTEGER;
    """;
  }
}
