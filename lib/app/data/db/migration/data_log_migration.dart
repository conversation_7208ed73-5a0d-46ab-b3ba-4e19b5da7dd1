/// FileName: data_log_migration
///
/// @Author: ygc
/// @Date: 2025/7/10 16:29
/// @Description:
import '../../../../../../framework/database/model/migration.dart';

class DataLogMigration extends Migration {
  DataLogMigration._();

  static String getCreateViewSql() {
    return """
      CREATE VIEW data_log AS SELECT
      * 
      FROM
        (
        SELECT
          id,
          uid,
          did,
          time,
          value,
          timePoint,
          source,
          dataType,
          NULL AS name,
          0 AS calorie,
          NULL AS items,
          0 AS num,
          0 AS category,
          0 AS isPlan, 
          0 AS duration, 
          0 AS fromType, 
          0 AS isFromWalk
        FROM
          glucose_ua 
        WHERE
          deleted = 0 UNION
        SELECT
          id,
          uid,
          did,
          time,
          0 AS value,
           timePoint,
          0 AS source,
          2 AS dataType,
          NULL AS name,
          calorie,
          items,
          0 AS num,
          0 AS category,
          0 AS isPlan, 
          0 AS duration, 
          0 AS fromType, 
          0 AS isFromWalk
        FROM
          data_food 
        WHERE
          deleted = 0 
          AND calorie > 0 UNION
        SELECT
          id,
          uid,
          did,
          time,
          0 AS value,
          0 AS timePoint,
          0 AS source,
          4 AS dataType,
          name,
          0 AS calorie,
          NULL AS items,
          num,
          category,
          isPlan , 
          0 AS duration, 
          0 AS fromType, 
          0 AS isFromWalk
        FROM
          data_drug 
        WHERE
        deleted = 0 UNION
        SELECT
          id,
          uid,
          did,
          time,
          0 AS value,
          0 AS timePoint,
          0 AS source,
          3 AS dataType,
          name,
          calorie,
          NULL AS items,
          0 as num,
          0 as category,
          0 as isPlan , 
          duration, 
          fromType, 
          isFromWalk
        FROM
          data_sport
        WHERE
        deleted = 0 
        ) AS data_all;
    """;
  }
}
