/// FileName: monitor_plan_migration
///
/// @Author: ygc
/// @Date: 2024/8/6 18:11
/// @Description:
import '../../../../../framework/database/model/migration.dart';
import '../../model/reminder/monitor_plan_model.dart';

class MonitorPlanMigration extends Migration {
  MonitorPlanMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $monitorPlanTable (
          ${Migration.getCommSql()}
          ${MonitorPlanColumns.uid} TEXT,
          ${MonitorPlanColumns.did} TEXT,
          ${MonitorPlanColumns.hour} INTEGER,
          ${MonitorPlanColumns.minute} INTEGER,
          ${MonitorPlanColumns.repeated} INTEGER,
          ${MonitorPlanColumns.enable} INTEGER,
          ${MonitorPlanColumns.timePoint} INTEGER,
          ${MonitorPlanColumns.type} INTEGER,
          ${MonitorPlanColumns.complete} INTEGER
      );
    """;
  }
}
