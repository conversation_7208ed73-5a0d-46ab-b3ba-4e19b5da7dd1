/// FileName: target_migration
///
/// @Author: ygc
/// @Date: 2024/8/8 13:53
/// @Description:
///
///

import '../../../../../../framework/database/model/migration.dart';
import '../model/drug/drug_data_model.dart';

class DrugDataMigration extends Migration {
  DrugDataMigration._();

  static String getCreateSql() {
    return """CREATE TABLE $drugDataTable (
          ${Migration.getCommSql()}
          ${DrugDataTableColumns.uid} TEXT,
          ${DrugDataTableColumns.did} TEXT,
          ${DrugDataTableColumns.time} INTEGER,
          ${DrugDataTableColumns.name} TEXT,
          ${DrugDataTableColumns.num} INTEGER,
          ${DrugDataTableColumns.category} INTEGER,
          ${DrugDataTableColumns.isPlan} SMALLINT,
          ${DrugDataTableColumns.fromUser} SMALLINT,
          ${DrugDataTableColumns.insulinkPenName} TEXT,
          ${DrugDataTableColumns.uuid} TEXT,
          ${DrugDataTableColumns.upid} INTEGER
      );
    """;
  }
}
