/// FileName: community_module.dart
///
/// @Date: 2024-05-22 16:32:25
/// @Description:

import 'package:dnurse/config/module_priority.dart';
import 'package:dnurse/framework/module/build/annotations.dart';
import 'package:dnurse/framework/module/module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

import '../../../router/routes.dart';
import '../pages/community_home/binding.dart';
import '../pages/community_home/view.dart';

@AppModule(priority: modulePriorityDefault)
class CommunityModule extends Module {
  const CommunityModule({super.priority}) : super(name: "community");

  @override
  // ignore: long-method
  List<GetPage> getRouters() {
    return [
      GetPage(
        name: Routes.community,
        page: () => const CommunityHomePage(),
        binding: CommunityHomeBinding(),
      ),
    ];
  }
}
