import 'package:dnurse/ui/style/color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../bootstrap/app.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../../ui/icons/icons.dart';
import '../../../../ui/widgets/app_bar/dnu_app_bar.dart';
import '../../../../ui/widgets/avatar.dart';
import 'logic.dart';

class CommunityHomePage extends StatelessWidget {
  const CommunityHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CommunityHomeLogic>();
    return Scaffold(
      backgroundColor: AppColor.background1,
      appBar: _buildAppBar(context),
      body: WebViewWidget(controller: controller.webViewController),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      leading: Padding(
        padding: const EdgeInsets.only(left: 10),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                Navigator.maybePop(context);
              },
              child: const Center(
                child: Icon(
                  Icons.arrow_back_ios,
                  size: 24,
                  color: (Colors.black),
                ),
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: () {
                final controller = Get.find<CommunityHomeLogic>();
                controller.onUserTap(context);
              },
              child: Avatar(
                size: 50,
                url: AppContext.to.isTemp
                    ? null
                    : AppContext.to.auth.value.headImgUrl,
              ),
            ),
          ],
        ),
      ),
      title: const Text(
        "社区",
        style: TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.w400,
          fontSize: 20,
          height: 1.1,
        ),
      ),
      leadingWidth: 120,
      actions: [
        getAppBarAction(
          iconData: DNUIconFont.lingdang,
          onPressed: () {
            AppRouter.push(Routes.userNotice);
          },
        ),
      ],
    );
  }
}
