import 'dart:async';

import 'package:dnurse/app/common/link/deep_link.dart';
import 'package:dnurse/bootstrap/app.dart';
import 'package:dnurse/framework/route/base_controller.dart';
import 'package:dnurse/ui/widgets/dnu_toast.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../framework/network/api_host.dart';
import '../../../../framework/service/event_service.dart';
import '../../../../framework/utils/log.dart';
import '../../../../router/router.dart';
import '../../../../router/routes.dart';
import '../../../common/web/web_utils.dart';
import '../../../common/web/webview_handler.dart';
import '../../../common/web/webview_interface.dart';
import '../../../user/event/login_changed.dart';
import 'state.dart';

class CommunityHomeLogic extends DNUBaseController {
  final state = CommunityHomeState();

  late WebViewController webViewController;
  bool _hasLoaded = false;
  late StreamSubscription loginChangedSubscription;
  late WebInterfaceHandler webInterfaceHandler;
  late String shopPath;

  @override
  void onInit() {
    super.onInit();
    shopPath = "${DNUHost.getActBaseUrl()}/community/";
    _initSubscription();
    _initWeb();
  }

  @override
  void onClose() {
    _cancelSubscription();

    super.onClose();
  }

  void _initSubscription() {
    loginChangedSubscription =
        EventService.to.bus.on<LoginChanged>().listen((event) {
      WebUtils.setTokenCookie();
    });
  }

  void _cancelSubscription() {
    loginChangedSubscription.cancel();
  }

  void _initWeb() async {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setOnJavaScriptAlertDialog((request) async {
        Log.d("setOnJavaScriptAlertDialog: ${request.message}, ${request.url}");

        webInterfaceHandler.handleMessage(request.message);
      });

    webViewController.setNavigationDelegate(
      WebUtils.getNavigationDelegate(onPageFinished: (String url) {
        _hasLoaded = true;
        WebViewInterface.injectDnuApp(webViewController);

      }, onNavigationRequest: (NavigationRequest request) {
        if (shopPath == request.url ||
            "https://act.test.dnurse.cn/community/0" == request.url) {
          webViewController.reload();
        } else if (request.url.startsWith(shopPath)) {
          AppRouter.push(Routes.browser,
              arguments: request.url,
              parameters: {RouteParams.appVerAtHead: "1"});
        } else if ("dnurseapp://com.dnurse/openwith?act=RESET_REPORT" ==
            request.url) {
          // MainRouter.getInstance(appContext).showActivity(UserUI.SET_USER_IDENTITY);

          AppRouter.push(Routes.userHealthGuide,
              parameters: {RouteParams.source: "community"});
        } else if ("dnurseapp://com.dnurse/openwith?act=DATA_SETTINGS" ==
            request.url) {
          AppRouter.push(Routes.dataSugarTarget,
              parameters: {RouteParams.source: "community"});
          // Bundle bundle = new Bundle();
//           bundle.putBoolean(StringPool.FROM_GENERAG, true);
//           MainRouter.getInstance(appContext).showActivity(DataUI.DATA_SETTINGS, bundle);
        } else if (!request.url.startsWith("dnurseapp:")) {
          if (AppContext.to.isTemp) {
            AppRouter.push(Routes.login,
                parameters: {RouteParams.source: "community"});
          }

          AppRouter.push(Routes.browser, arguments: request.url);
        } else {
          DeepLink.handleWidthParameter(request.url);
        }

        if (request.url.split(":")[0] == "openapp.jdmobile") {
//           boolean appInstalled = AppUtils.isAppInstalled("com.jingdong.app.mall");
//           if (appInstalled) {
//             Intent intent = new Intent();
//             intent.setAction("android.intent.action.VIEW");
//             Uri uri = Uri.parse(url);
//             intent.setData(uri);
//             intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//             startActivity(intent);
// //                    webView.goBack();
//           } else {
//             ToastUtils.showLong("未安装京东app");
//           }
        }

        return NavigationDecision.prevent;
      }),
    );

    WebViewInterface.setInterface(webViewController);

    WebUtils.setUserAgent(webViewController, appVerAtHead: true);
    WebUtils.setTokenCookie();
    WebUtils.initGrant(webViewController);

    _initWebHandler();
  }

  void _initWebHandler() {
    webInterfaceHandler = WebInterfaceHandler(
      webViewController: webViewController,
    );
  }

  @override
  void onReady() {
    super.onReady();

    webViewController.loadRequest(Uri.parse(shopPath));
  }

  @override
  void onResume() {
    super.onResume();
    if (_hasLoaded) {
      webViewController.runJavaScript("window.\$nuxt.refresh()");
    }
  }

  void onUserTap(BuildContext context) {
    if (AppContext.to.isTemp) {
      DNUToast.show("请先登录您的账号", context);

      AppRouter.push(Routes.login,
          parameters: {RouteParams.source: "community"});
      return;
    }

    AppRouter.push(Routes.browser,
        arguments: "${shopPath}user/0",
        parameters: {RouteParams.appVerAtHead: "1"});
  }
}
